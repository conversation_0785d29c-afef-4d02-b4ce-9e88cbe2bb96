<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("生成发货通知单")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/productCategory.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/utils.js"></script>

<script type="text/javascript" src="/resources/js/base/file.js"></script>
<style type="text/css">
tr.s-tr, tr.s-tr td {
	height: 10px !important;
}

.mefont {
	color: red;
	font-style：italic;
	斜体
	font-weight：bold;
	加粗
	font-size：30px;
	大小
	line-height：30px;
	行高
	font-family：“SimHei”;
	字体
}
</style>
<script type="text/javascript">

function productOrganization(){
	var typesystemDictFlag = $("input.typesystemDictFlag").val();
	if(isNull(typesystemDictFlag) != null){
		$("input.productId").each(function(){
			var $this = $(this);
			var $tr = $this.closest("tr");
			if(typesystemDictFlag == 0){
				$tr.find(".productOrganizationName").text($(".organizationName").text());
				$tr.find("input.productOrganizationId").val($("input.organizationId").val());
				[#if linkStock == 1 ] 
					loadAttQuantity($tr);
				[/#if]
			}else if (typesystemDictFlag == 1){
				[#if linkStock == 1 ] 
					loadAttQuantity($tr);
				[/#if]
			}
		});
	}
}

//加载库存
function loadAttQuantity($tr){
	[#if shipmentStockQueryRoles ==0 ]
		jurisdictionStockQueryRoles($tr);
	    return;
	[/#if]
	//产品经营组织
	var organizationId = $tr.find(".productOrganizationId").val();
	if(isNull(organizationId) != null){
		//仓库
		var warehouseId = $("input.warehouseId").val();
		//产品
		var productId = $tr.find("input.productId").val();
		//等级
		var productGrade = $tr.find(".productGrade").val();
		//色号
		var colourNumber = $tr.find(".colourNumber").val();
		if(isNull(colourNumber) == null){
			colourNumber = "";
		}
		//含水率
		var moistureContent = $tr.find(".moistureContent").val();
		if(isNull(moistureContent) == null){
			moistureContent = "";
		}
		//批次
		var batchIds = $tr.find("input.batchIds").val();
		if(isNull(batchIds) == null){
			batchIds = "";
		}else{
			batchIds = batchIds.split(';');
		}
		var params='&warehouseId='+warehouseId+'&productId='+productId+'&productGrade='+productGrade
		  		+'&organizationId='+organizationId+'&colourNumber='+colourNumber+'&moistureContent='+moistureContent
		  		+'&batchIds='+batchIds;
			params = params.substring(1,params.length);
		$.ajax({
			url:'/stock/stock/findViewStock.jhtml?'+params,
   			type : "post",
   			success : function(rows) {
   				var data= $.parseJSON(rows.content);
                   if(data.length>0){
                       for (var i = 0; i < data.length;i++) {
	                        //可用库存箱数
	                       	if(isNull(data[i].totalAttQuantity2) != null) {
	                       		$tr.find(".attQuantity2BoxNum").text(data[i].totalAttQuantity2);		                   
	                       	}else {
	                       		$tr.find(".attQuantity2BoxNum").text(0);		                       		
	                       	}		                     
	                        //可用库存支数
	                       	if(isNull(data[i].totalAttQuantity3) != null) {
	                       		$tr.find(".attQuantity3Num").text(data[i].totalAttQuantity3);
	                       	}else {
	                       		$tr.find(".attQuantity3Num").text(0);		                       		
	                       	}
	                        //可用库存数量
	                       	if(isNull(data[i].totalAttQuantity1) != null) {
	                       		$tr.find(".attQuantity1Num").text(data[i].totalAttQuantity1);
	                       	}else {
	                       		$tr.find(".attQuantity1Num").text(0);		                       		
	                       	}
                   	}
               	}else{
               		$tr.find(".attQuantity2BoxNum").text(0);	
               		$tr.find(".attQuantity3Num").text(0);		
               		$tr.find(".attQuantity1Num").text(0);	
               	}
                //获取件数值
                var boxQuantity = $tr.find("input.boxQuantity").val();
                //支数
                var branchQuantity = $tr.find("input.branchQuantity").val();
                //数量
                var quantity = $tr.find("input.quantity").val();		                   
                if(boxQuantity > $tr.find(".attQuantity2BoxNum").text()) {
                		$tr.addClass("backColor");
                }else {
             	   $tr.removeClass("backColor");
                }
                
   			}
		})	
	}
} 


//控制颜色
function addBackColor(t) {
	//控制颜色
	var fileDir = $(t).closest("span").text();			
	if($(t).val()>fileDir) {
		$(t).parents("tr").addClass("backColor");
	}else if($(t).val()<=fileDir) {
		$(t).parents("tr").removeClass("backColor");;
	}
}

function countTotal(t){
	
	var totalBoxQuantity = 0;
	var totalBranchQuantity = 0;
	var totalVolume = 0;
	var totalWeight = 0;
	var $bInput = $("input.boxQuantity");
	$bInput.each(function(){
        var quantity = 0;
        var $tr = $(this).closest("tr");
        var isEqual = null;
        if(t!=undefined){
            isEqual = (t.find(".orderItemId").val() == $tr.find(".orderItemId").val());                	
        }
        var boxQuantity=$(this).val();
        var branchPerBox=$tr.find(".branchPerBox").val();
        var perBranch=$tr.find(".perBranch").val();
        var scatteredQuantity=$tr.find(".scatteredQuantity").val();//零散支数
        var perBox = $tr.find(".perBox").val();//
        var type = productDisc(branchPerBox,perBranch,perBox);
        if(isEqual==null){
        	if(type==0){
            	var branchQuantity=accMul(boxQuantity,branchPerBox);
            	branchQuantity=accAdd(branchQuantity,scatteredQuantity);
            	if(isNaN(branchQuantity)){
            		branchQuantity = 0;
        		}
            	totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
            	totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
            	$tr.find(".branchQuantity").val(branchQuantity);//支数
            	$tr.find(".branchQuantityStr").html(branchQuantity);
            	quantity=accMul(branchQuantity,perBranch);
            	
            	if(isNaN(quantity)){
            		quantity = 0;
        		}
            	$tr.find(".quantity").val(quantity);//数量
            	$tr.find(".quantityStr").html(quantity);
            	
            	
            	var volume=$tr.find(".volume").val();
            	
            	var volumeAmount=Number(accMul($(this).val(),volume)).toFixed(6);
        		
        		if(isNaN(volumeAmount)){
        			volumeAmount = 0;
        		}
        		totalVolume = accAdd(totalVolume,Number(volumeAmount).toFixed(6)); 
        		$tr.find(".lineVolumeAmount").html(volumeAmount);//体积
            }
          	//辅料计算逻辑	
        	if(type==2){
        		var a = accDiv(perBox,10);
        		quantity = accMul(boxQuantity,a);
        	}
          	if(type==1){
          		quantity = $tr.find(".quantity").val();
          	}
          	if(type==3){
          		var branchQuantity = $tr.find(".branchQuantity").val();
          		quantity = accMul(branchQuantity,perBranch);
          	}
        }else{
        	if(type==0&&isEqual){
            	var branchQuantity=accMul(boxQuantity,branchPerBox);
            	branchQuantity=accAdd(branchQuantity,scatteredQuantity);
            	if(isNaN(branchQuantity)){
            		branchQuantity = 0;
        		}
            	totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
            	totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
            	$tr.find(".branchQuantity").val(branchQuantity);//支数
            	$tr.find(".branchQuantityStr").html(branchQuantity);
            	quantity=accMul(branchQuantity,perBranch);
            	
            	if(isNaN(quantity)){
            		quantity = 0;
        		}
            	$tr.find(".quantity").val(quantity);//数量
            	$tr.find(".quantityStr").html(quantity);
            	
            	
            	var volume=$tr.find(".volume").val();
            	
            	var volumeAmount=Number(accMul($(this).val(),volume)).toFixed(6);
        		
        		if(isNaN(volumeAmount)){
        			volumeAmount = 0;
        		}
        		totalVolume = accAdd(totalVolume,Number(volumeAmount).toFixed(6)); 
        		$tr.find(".lineVolumeAmount").html(volumeAmount);//体积
            }
          	//辅料计算逻辑	
        	if(type==2&&isEqual){
        		var a = accDiv(perBox,10);
        		quantity = accMul(boxQuantity,a);
        		$tr.find(".quantity").val(quantity);//数量
            	$tr.find(".quantityStr").html(quantity);
        	}
          	if(type==1&&isEqual){
          		quantity = $tr.find(".quantity").val();
          		$tr.find(".quantity").val(quantity);//数量
            	$tr.find(".quantityStr").html(quantity);
          	}
          	if(type==3&&isEqual){
          		var branchQuantity = $tr.find(".branchQuantity").val();
          		quantity = accMul(branchQuantity,perBranch);
          		$tr.find(".quantity").val(quantity);//数量
            	$tr.find(".quantityStr").html(quantity);
          	}
        }
   
      	
		
		var volume=$tr.find(".volume").val();
    	var volumeAmount=Number(accMul($(this).val(),volume)).toFixed(6);
		if(isNaN(volumeAmount)){
			volumeAmount = 0;
		}
		totalVolume = accAdd(totalVolume,Number(volumeAmount).toFixed(6)); 
		$tr.find(".lineVolumeAmount").html(volumeAmount);//体积
	});
	$("#totalBoxQuantity").text(totalBoxQuantity);
	$("#totalBranchQuantity").text(totalBranchQuantity);
	
	var $input = $("input.quantity");
	var total = 0;
	var totalQuantity = 0;
	
	var b = $("#storeBalance").val();
	$input.each(function(){
		var $this = $(this);
		var $tr = $this.closest("tr");
		var $price_box = $tr.find(".price-box");
		var price;
		if($price_box.length==0 || $price_box.prop("checked")==false){
			price = Number($tr.find("input.price").val());
		}else{
			price = Number($tr.find("input.origMemberPrice ").val());
		}
		var quantity = $this.val();
		var amount = Number($this.val())*price;
		
		totalQuantity=Number(accAdd(totalQuantity,Number($this.val()))).toFixed(6);
		
		if(isNaN(amount)){
			amount = 0;
		}
		total = Number(total)+Number(currency(amount,false));
		$tr.find(".trprice").html(currency(amount,true));//订单行金额
		
		var weight=$tr.find(".weight").val();
    	
		var weightAmount=Number(accMul($(this).val(),weight)).toFixed(6);
		
		if(isNaN(weightAmount)){
			weightAmount = 0;
		}
		totalWeight = accAdd(totalWeight,Number(weightAmount).toFixed(6)); 
		$tr.find(".lineWeightAmount").html(weightAmount);//重量
		
	});
	$("#total").text(currency(total,true));
	$("#totalVolume").text(totalVolume);
	$("#totalWeight").text(totalWeight);
	$("#totalQuantity").text(totalQuantity);
	
	var storeBalance = $("#storeBalance").val()
	$("#chae").text(currency(storeBalance-total,true));
	

	
}



function editQty(t,e){
	
	if($(t).attr("kid")=="quantity"){//平方
		if(extractNumber(t,6,false,e)){
			var $tr = $(t).closest("tr");
			var branch_quantity=0;
			var box_quantity=0;
			
			var quantity=$(t).val();
			var perBox = $tr.find(".perBox").val();
			var perBranch=$tr.find(".perBranch").val();  //每支单位数
			var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
			var type = productDisc(branchPerBox,perBranch,perBox);
			var scattered=0;
			if(perBranch!=0 && branchPerBox!=0){
				 branch_quantity=quantity/perBranch;
				 box_quantity=parseInt(branch_quantity/branchPerBox);
				 scattered=(branch_quantity%branchPerBox).toFixed(6);
			}
			if(type==2){
				box_quantity = modulo(quantity,perBox);
			}
			if(type==3){
				branch_quantity = modulo(quantity,perBranch);
			}
			$tr.find(".boxQuantity").val(box_quantity);
			$tr.find(".branchQuantity").val(branch_quantity);
			$tr.find(".scatteredQuantityStr").html(scattered);
			$tr.find(".scatteredQuantity").val(scattered);
			
			
			countTotal($tr);
			$(t).val(quantity);
			editLineInfo(t);
			addBackColor(t);
			
		}
	}else{
		if(extractNumber(t,3,false,e)){
			var $tr = $(t).closest("tr");
			
			var branch_quantity=0;
			var box_quantity=0;
			
			if($(t).attr("kid")=="box"){//箱
				$tr.find(".scatteredQuantityStr").html(0);
				$tr.find(".scatteredQuantity").val(0);
			}else if($(t).attr("kid")=="branch"){//支
				var quantity=$(t).val();
				var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
				var box=parseInt(quantity/branchPerBox);
				var scattered=quantity%branchPerBox;
				$tr.find(".boxQuantity").val(box);
				$tr.find(".scatteredQuantityStr").html(scattered);
				$tr.find(".scatteredQuantity").val(scattered);
			}else if($(t).attr("kid")=="quantity"){//平方
				var quantity=$(t).val();
				var perBranch=$tr.find(".perBranch").val();  //每支单位数
				var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
				var branch_quantity=quantity/perBranch;
				var box_quantity=parseInt(branch_quantity/branchPerBox);
				var scattered=branch_quantity%branchPerBox;
				$tr.find(".boxQuantity").val(box_quantity);
				$tr.find(".scatteredQuantityStr").html(scattered);
				$tr.find(".scatteredQuantity").val(scattered);
			}
			
			countTotal($tr);
			editLineInfo(t);
			addBackColor(t);
		}
	}
}

function editLineInfo(t){
	var $tr = $(t).closest("tr");
	var quantity = Number($tr.find(".quantity").val());
	var volume = Number($tr.find("input.volume").val());
	var weight = Number($tr.find("input.weight").val());
	var price = Number($tr.find("input.price").val());
	var lineVolume = accMul(quantity,volume);
	var lineWeight = accMul(quantity,weight);
	var lineAmount = accMul(quantity,price);
	lineVolume = lineVolume.toFixed(6);
	lineVolume = parseFloat(lineVolume);
	lineWeight = lineWeight.toFixed(6);
	lineWeight = parseFloat(lineWeight);
	$tr.find(".volumeSpan").text(lineVolume);
	$tr.find(".weightSpan").text(lineWeight);
	$tr.find(".lineAmountSpan").text(currency(lineAmount,true));
}



 <!-- 2019-08-05 冯旗 检测备注字数是否超过240字符-->  
 function listenLength(){
   
  var memeo=$(".memo").val();
  var len = 0;  
    for (var i=0; i<memeo.length; i++) {   
     var c = memeo.charCodeAt(i);   
    //单字节加1   
     if ((c >= 0x0001 && c <= 0x007e) || (0xff60<=c && c<=0xff9f)) {   
       len++;   
     }   
     else {   
      len+=2;   
     }   
    }   
    if(len>80){
      $("#isMemo").val("字符长度已超过240!");
    }else{
     $("#isMemo").val("");
    }
 
 
 }



var map = {};
var total = 0;

$().ready(function() {

	// 地区选择
		$("#areaId").lSelect();
	
	$("#selectWarehouse").click(function(){
		var vId = $(".supplierId").val();
		warehouseWin(this,vId);
	});
	
	$("#selectDriverInfo").click(function(){
		var dId = $("#deliveryId").val();
		driverInfoWin(this,dId);
	})
	
	$("#selectSupplier").click(function(){
		supplierWin(this);
	})
	
	$(".delBtn").live("click",function(){
		var index = $(this).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$mmGrid.removeRow(index);
		})
	
	})
	
	var $warehouseId = $("input[name='warehouseId']");
	var $warehouseName = $warehouseId.parent().find(".warehouseName");
	initWarehouseId = $warehouseId.val();
	initWarehouseName = $warehouseName.val();
	
	sw = 0;
	var itemIndex = 0;
	var cols = [
			{ title:'${message("行号")}', name:'line_no' ,width:40, align:'center', renderer: function(val,item,rowIndex,obj){
				if(obj==undefined){
					return item.line_no;
				}else{
					return obj.line_no;
				}
			}},
			{ title:'${message("操作")}', hidden:true, name:'ship_quantity' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
				var html = '';
				var bomFlag = '';
				if(obj==undefined){
					bomFlag = item.bom_flag;
					if(item.bom_flag==2){
						var sourceId= 'id_'+item.id;
						var suiteItemId = item.suite_item_id;
						html = '<a href="javascript:;" class=" btn-delete" onclick="add(this,\''+sourceId+'\','+suiteItemId+')">替换</a>';
					}else{
						html = '-';
					}
				}else{
					bomFlag = 2;
					if(sw==1){
						var sourceId= 'id_'+item.id;
						var suiteItemId = item.suite_item_id;
						html = '<a href="javascript:;" class=" btn-delete" onclick="add(this,\''+sourceId+'\','+suiteItemId+')">替换</a>';
						sw = 0;
					}else{
						html = '-';
					}
				}
				html+= '<input type="hidden"  name="shippingItems['+itemIndex+'].bomFlag" value="'+bomFlag+'">';
				return html;
			}},			
			{ title:'${message("产品编码")}', name:'vonder_code' ,width:120, align:'center', renderer: function(val,item,rowIndex,obj){
				return '<span class="vonderCode">'+val+'</span>';
			}},
			{ title:'${message("产品描述")}', name:'description',width:200,align:'center'},
			{ title:'${message("经营组织")}',width:80, align:'center', renderer: function(val,item,rowIndex,obj){
				var productOrganizationName = '';
 				if(item.product_organization_name != null && item.product_organization_name !=''){
 					productOrganizationName = item.product_organization_name;
 				}
 				var productOrganizationId = '';
 				if(item.product_organization_id != null && item.product_organization_id !=''){
 					productOrganizationId = item.product_organization_id;
 				}
     			var html='<span class="productOrganizationName">'+productOrganizationName+'</span>'+
     				       '<input type="hidden" class="productOrganizationId text" value="'+productOrganizationId+'" />';
        		return html;
  		 	}},
			{ title:'${message("产品级别")}', name:'levelName' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
				 return '<span>'+item.levelName+'</span><input type="hidden" class="productGrade"  value="'+item.level_Id+'">';
			}},									
			{ title:'${message("制单件数")}',[#if sbua.id == 3]hidden:true,[/#if] name:'ship_box_quantity' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
				var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
				var str = '';
				var pid= item.product;
				if(pid==null){
					pid=0;
				}
				if(item.bom_flag!=2){
					str+= '<input type="hidden"  name="shippingItems['+itemIndex+'].orderItem.id" value="'+item.id+'">';
				}
				var quantity = parseInt((item.branch_quantity - item.shiped_branch_quantity-item.closed_quantity)/item.branch_per_box);
				if(isNaN(quantity)){
					//临时加的
					quantity = val;
				}
				if(type==1||type==3){
					str+= '-'+
					'<input type="hidden"  name="shippingItems['+itemIndex+'].boxQuantity" value="0" >'+
					'<input type="hidden"  name="shippingItems['+itemIndex+'].order.id" value="'+item.orders+'">'+
					'<input type="hidden"  name="shippingItems['+itemIndex+'].product.id" class="product_id_'+pid+' productId" value="'+pid+'">'+
					'<input type="hidden"  name="productIds" class="product_id_'+pid+'" value="'+pid+'">'+
					'<input type="hidden" id="id_'+item.id+'" max="'+quantity+'" class="orderItemId" orderItemId="'+item.id+'" orderId="'+item.orders+'" productId="'+item.product+'" value="'+item.id+'">'+
					'<input type="hidden" kid="box" class="t boxQuantity" sourceId="id_'+item.id+'"  value="'+val+'" minData="0" maxData="'+quantity+'">';
				}else{
					str+= 
					'<input type="hidden"  name="shippingItems['+itemIndex+'].order.id" value="'+item.orders+'">'+
					'<input type="hidden"  name="shippingItems['+itemIndex+'].product.id" class="product_id_'+pid+' productId" value="'+pid+'">'+
					'<input type="hidden"  name="productIds" class="product_id_'+pid+'" value="'+pid+'">'+
					'<input type="hidden" id="id_'+item.id+'" max="'+quantity+'" class="orderItemId" orderItemId="'+item.id+'" orderId="'+item.orders+'" productId="'+item.product+'" value="'+item.id+'">'+
					'<ul><li><div class="nums-input ov">'+
		            	//'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty(this.nextSibling,event)">'+
		            	'<input type="text" kid="box" class="t boxQuantity" sourceId="id_'+item.id+'" name="shippingItems['+itemIndex+'].boxQuantity" value="'+val+'" minData="0" maxData="'+quantity+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
		            	//'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty(this.previousSibling,event)">'+
		        	'</div></li><li><span class="attQuantity2BoxNum" >0</span></li></ul>';
				}
				return str;	
			}},
			
			{ title:'${message("支数")}',[#if sbua.id == 3]hidden:true,[/#if] name:'ship_branch_quantity' ,align:'center', width:80, renderer: function(val,item,rowIndex, obj){
				var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
				var branchQuantity='';
				if(obj==undefined){
					branchQuantity = val;
				}
				var branchPerBox = item.branch_per_box == null?0:item.branch_per_box;
				var perBox = item.per_box == null?0:item.per_box;
				var perBranch = item.per_branch == null?0:item.per_branch;
				if(type==1||type==2){
					branchQuantity=0;
					return '-'+
					'<input type=hidden class="branchPerBox" name="shippingItems['+itemIndex+'].branchPerBox" value="'+branchPerBox+'" /> '+
					'<input type=hidden class="perBox"  value="'+perBox+'" />'+
					'<input type=hidden class="perBranch" name="shippingItems['+itemIndex+'].perBranch" value="'+perBranch+'" />'+
					'<input type="hidden"  kid="branch" class="t branchQuantity" sourceId="id_'+item.id+'" name="shippingItems['+itemIndex+'].branchQuantity" value="'+branchQuantity+'" minData="0"  >';
				}else{
					var html='<input type=hidden class="branchPerBox" name="shippingItems['+itemIndex+'].branchPerBox" value="'+branchPerBox+'" /> '+
					'<input type=hidden class="perBox"  value="'+perBox+'" />'+
					'<input type=hidden class="perBranch" name="shippingItems['+itemIndex+'].perBranch" value="'+perBranch+'" />'+
					'<ul><li><div class="nums-input ov">'+
	            //	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty(this.nextSibling,event)">'+
	            	'<input type="text"  kid="branch" class="t branchQuantity" sourceId="id_'+item.id+'" name="shippingItems['+itemIndex+'].branchQuantity" value="'+val+'" minData="0" maxData="'+(item.branch_quantity-item.shiped_branch_quantity-item.closed_quantity)+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
	            	//'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty(this.previousSibling,event)">'+
	        	'</div></li><li><span class="attQuantity3Num">0</span></li></ul>';
				return html;
				}
			}},
			{ title:'${message("零散支数")}',[#if sbua.id == 3]hidden:true,[/#if] name:'' ,align:'center', width:110, renderer: function(val,item,rowIndex, obj){
				var branchPerBox=item.branch_per_box;
				var scatteredQuantity=item.ship_branch_quantity%item.branch_per_box;
				if(isNaN(scatteredQuantity)){
					scatteredQuantity = 0;
				}
				var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
				if(type==2||type==3||type==1){
					return '-'+'<input type="hidden" name="shippingItems['+itemIndex+'].scatteredQuantity" value="0" />';
				}
				var html='<span class="scatteredQuantityStr">'+scatteredQuantity+'</span>'+
					'<input type="hidden" name="shippingItems['+itemIndex+'].scatteredQuantity" class="scatteredQuantity text" value="'+scatteredQuantity+'" />';
				return html;
				
			}},
			{ title:'${message("宽")}',[#if sbua.id != 3]hidden:true,[/#if]  name:'length', align:'center', width:110, renderer:function(val,item,rowIndex,obj){
			        var sbu=${sbua.id}
                	if(item.unit=='m2' && sbu==3){
            		  	return val+'<input type="hidden" name="shippingItems['+itemIndex+'].length" class="length text" value="'+val+'" />';
					 }else{
				      	return '-'+'<input type="hidden" name="shippingItems['+itemIndex+'].length" class="length text" value="'+val+'" />';
				    }	
			}},
			{ title:'${message("高")}',[#if sbua.id != 3]hidden:true,[/#if]  name:'width', align:'center', width:110, renderer:function(val,item,rowIndex,obj){
			  		var sbu=${sbua.id}
                	if(item.unit=='m2' && sbu==3){
            		 	return val+'<input type="hidden" name="shippingItems['+itemIndex+'].width" class="width text" value="'+val+'" />';
					}else{
				      	return '-'+'<input type="hidden" name="shippingItems['+itemIndex+'].width" class="width text" value="'+val+'" />';
				    }	
			}},
			{ title:'${message("数量")}', name:'ship_quantity', align:'center', width:80, renderer:function(val,item,rowIndex,obj){
				var quantity='';
				if(obj==undefined&&item.per_branch!=undefined){
					quantity = accMul(item.ship_branch_quantity,item.per_branch);
				}
				if(item.branch_per_box==undefined || item.per_branch == undefined || item.branch_per_box==0 || item.per_branch == 0){
					quantity=item.ship_quantity;
				}
				if(item.length!='' && item.width!='' && item.length>0 && item.width>0){
				  return quantity+'<input type="hidden" kid="quantity" class="t quantity"  name="shippingItems['+itemIndex+'].quantity" value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >';
				}else{
				 var text = '<div class="lh20">'+
					'<ul><li><div class="nums-input ov square">'+
			        	//'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
			        	'<input type="text" kid="quantity" class="t quantity"  name="shippingItems['+itemIndex+'].quantity" value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
			        	//'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
			        	'<input type="hidden" class="minPriceApplyQuantity" />'+
						'<input type="hidden" class="maxPriceApplyQuantity" />'+
			    	'</div></div></li><li><span class="attQuantity1Num">0</span></li></ul>';
			    	return text;
				}			
			}},
			[#if hiddenBatch !=0]     
					{ title:'${message("色号")}',align:'center', width:80, renderer: function(val,item,rowIndex, obj){
						var str='selected="selected"';
						var html='<select name="shippingItems['+itemIndex+'].colourNumbers.id" class="text colourNumber">';
							html+='<option value="">请选择</option> ';
							[#list colorNumberList as colorNumber]
								if(${colorNumber.id}==item.colour_number_id){
									html+='<option value="${colorNumber.id}" '+str+' >${colorNumber.value}</option> ';
								}else{
									html+='<option value="${colorNumber.id}">${colorNumber.value}</option> ';
								}
							[/#list]
							html+='</select>';
						return html; 
					}},
					{ title:'${message("含水率")}',align:'center', width:80, renderer: function(val,item,rowIndex, obj){
						var str='selected="selected"';
						var html='<select name="shippingItems['+itemIndex+'].moistureContents.id" class="text moistureContent">';
							html+='<option value="">请选择</option> ';
							[#list moistureContentList as  moistureContent]
								if(${moistureContent.id}==item.moisture_content_id){
									html+='<option value="${moistureContent.id}" '+str+' >${moistureContent.value}</option> ';
								}else{
									html+='<option value="${moistureContent.id}">${moistureContent.value}</option> ';
								}
							[/#list]
							html+='</select>';
						return html; 
					}},
					{ title:'${message("批次")}', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
						var batch = '';
						if(isNull(item.batch) != null){
							batch = item.batch;
						}
						var batch_encoding = '';
						if(isNull(item.batch_encoding) != null){
							batch_encoding = item.batch_encoding;
						}
			        	 var html = '<span class="search" style="position:relative">'
			        		    +'<input type="hidden" name="shippingItems['+itemIndex+'].batch" class="text batchIds" value="'+batch+'" />'
			        		    +'<input type="text" name="shippingItems['+itemIndex+'].batchEncoding" class="text batchEncodings" value="'+batch_encoding+'" readonly/>'
								+'<input type="button" class="iconSearch" value="" id="selectBatch"/>'
							+'</span>';
						 return html;
			         }},
			 [/#if]
			{ title:'${message("备注")}',name:'seller_memo',width:100,align:'center',renderer:function(val,item,rowIndex,obj){
				var memo='';
				if(obj==undefined){
					memo = item.seller_memo;
				}
				var html=
				'<input type="text" name="shippingItems['+itemIndex+'].memo" class="text" value="'+memo+'" />';
				return html;
			}},
 			{ title:'${message("产品价格")}',[#if hiddenAmount==0] hidden:true, [/#if] name:'price',width:80, align:'center', renderer: function(val,item,rowIndex,obj){
 				var html = '<span class="red">'+currency(val,true)+'</span><input type="hidden" class="price" value="'+val+'">';
 				return html;
 			}},
 			{ title:'${message("平台结算价")}', [#if isMember!=0 ||  seeSaleOrgPrice == 0 ]hidden:true,[/#if] name:'sale_org_price' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
 				var html = '<span class="red">'+currency(val,true)+'</span><input type="hidden" class="price" value="'+val+'">';
 				return html;
 			}}, 			
 			{ title:'${message("产品名称")}', name:'name' ,width:100, align:'center', renderer: function(val,item,rowIndex,obj){
				if(obj==undefined){
					return val;
				}else{
					return item.name;
				}
			}},
 			{ title:'${message("工厂/供应商")}',[#if sbua.id != 3||isMember==1]hidden:true,[/#if] name:'manufactory_name' ,width:250, align:'center', renderer: function(val,item,rowIndex,obj){
				if(obj==undefined){
					return val;
				}else{
					if( item.manufactory_name!=null){
						return item.manufactory_name;
					}
				}
			}},
			{ title:'${message("供应商型号")}',[#if sbua.id != 3||isMember==1]hidden:true,[/#if] name:'supplier ' ,width:100, align:'center', renderer: function(val,item,rowIndex,obj){
				return item.supplier;
			}},
			{ title:'${message("产品型号")}', name:'model' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
				if(obj==undefined){
					return val;
				}else{
					if( item.model!=null){
						return item.model;
					}
				}
			}},
			{ title:'${message("单位")}', align:'center',width:60,name:'unit'},
			{ title:'${message("金额")}',[#if hiddenAmount==0] hidden:true, [/#if] name:'volume' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
 				var lineAmount = item.ship_quantity*item.price;
				var html = '<span class="red lineAmountSpan">'+currency(lineAmount,true)+'</span>';
 				return html;
 			}},						 
			{ title:'${message("体积")}',[#if sbua.id == 3]hidden:true,[/#if] name:'volume' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
				var volume = (val!="")?val:0;
				var lineVolume = volume*item.ship_quantity;
				lineVolume = lineVolume.toFixed(6);
				lineVolume = parseFloat(lineVolume);
				var html = '<span class="volumeSpan">'+lineVolume+'</span>'+
					'<input type="hidden" class="text volume" value="'+volume+'" />';
				return html;
			}},
			{ title:'${message("重量")}',[#if sbua.id == 3]hidden:true,[/#if] name:'weight' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
				var weight = (val!="")?val:0;
				var lineWeight = weight*item.ship_quantity;
				lineWeight = lineWeight.toFixed(6);
				lineWeight = parseFloat(lineWeight);
				var html = '<span class="weightSpan">'+lineWeight+'</span>'+
					'<input type="hidden" class="text weight" value="'+weight+'" />';
				return html;
			}},
			{ hidden:true, name:'line_no' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
				var lineNo;
				var warehouseId;
				var orderSn;
				var suite_quantity = '';
				var html = '';
				if(obj==undefined){
					lineNo = item.line_no;
					orderSn = item.order_sn;
					if(item.bom_flag==2){
						suite_quantity = item.suite_quantity;
					}
				}else{
					lineNo = obj.line_no;
					orderSn = obj.order_sn;
					suite_quantity = obj.suite_quantity;
				}
				html+= '<input type="hidden" class="lineNo" name="shippingItems['+itemIndex+'].lineNo" value="'+lineNo+'">'+
							'<input type="hidden" class="orderSn"  value="'+orderSn+'">'+
							'<input type="hidden" class="suiteQuantity"  value="'+suite_quantity+'">';
				if(item.suite_item_id!=undefined && item.suite_item_id!=null){
					html+= '<input type="hidden" class="suiteItemId suiteItemId_'+item.suite_item_id+'" value="'+item.suite_item_id+'">';
				}
				if(item.bom_flag==1){
					html+= '<input type="hidden" class="hideInput">';
				}
				return html;
			}},		
			{ title:'${message("订单编号")}', name:'order_sn' ,width:120, align:'center', renderer: function(val,item,rowIndex,obj){
				itemIndex++;
				if(obj==undefined){
					return item.order_sn;
				}else{
					return obj.order_sn;
				}
			}},
	];
    
    var param = jQuery.param( $("#dataForm").serializeObject(), true);
    ajaxSubmit('',{
		method:'post',
		url:'shipping_operate_list_data.jhtml',
		data: param,
		callback: function(resultMsg) {
			var data = $.parseJSON(resultMsg.content);
			var shippingItems = data.shippingItems;
		    $shipping_mmGrid = $('#table-shipping').mmGrid({
				height:'auto',
				fullWidthRows:true,
				autoLoad:true,
				useCountText:false,
				checkCol:false,
		        cols: cols,
		        items: shippingItems,
		        callback:function(){
		        	$(".hideInput").each(function(){
		        		$(this).closest("tr").hide();
		        	});
		        	countTotal();
		        	productOrganization();
		        }
		    });
		    
		    
		}
	})
	
	 /**初始化附件*/
    var attachIndex=0;
	var attachcols = [				
    	{ title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex){
    		var url = item.url;
			var fileObj = getfileObj(item.name);
			/**设置隐藏值*/
			var hideValues = {};
			hideValues['shippingAttachs['+attachIndex+'].url']=url;
			hideValues['shippingAttachs['+attachIndex+'].suffix']=fileObj.suffix;
			
			return createFileStr({
				url : url,
				fileName : fileObj.file_name,
				name : fileObj.name,
				suffix : fileObj.suffix,
				time : '',
				textName:'shippingAttachs['+attachIndex+'].name',
				hideValues:hideValues
			});               
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:590 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text file_memo" name="shippingAttachs['+attachIndex+'].memo" >'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
			attachIndex++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}}
	];
	var $amGrid=$('#table-attach').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: attachcols,
        checkCol: false,
        autoLoad: true
    });
    
    
    var $addAttach = $("#addAttach");
	var attachIdnex = 0;
	var option1 = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row = data[i].file_info;
				$amGrid.addRow(row,null,1);
	        }
			
        }
    }
    $addAttach.file_upload(option1);
    
    var $deleteAttachment = $(".deleteAttachment");
	
	$deleteAttachment.live("click", function() {
		var $this = $(this);
		$this.closest("tr").remove();
	});
	
	$("#openArea").bindQueryBtn({
		type:'area',
		title:'${message("查询地区")}',
		url:'/basic/area/select_area.jhtml',
		callback:function(rows){
			if(rows.length>0){
				var $tr =$this.closest("tr");
				$(".areaId").val(rows[0].id);
				$(".areaName").val(rows[0].full_name);
				$("input[name='zipCode']").val('');
			}
		}
	});
	
	$("#selectInvoice").bindQueryBtn({
		type:'supplier',
		title:'${message("查询发票抬头")}',
		url:'/member/store/select_store_invoice.jhtml?storeId='+${store.id},
		callback:function(rows){
			if(rows.length>0){
				var t=$this.closest("table.input-edit")
				t.find("input[name='invoiceTitle']").val(rows[0].invoice_title);
			}
		}
	});
	
	 //打开选择地址界面
    $("#addReceiveAddress").click(function(){
        var storeId = $(".storeId").val();
        if(storeId==""){
            $.message_alert('请选择客户');
        }else{
        		select_store_address(storeId);
        }
    });
    
    //更换地址
    function select_store_address(storeId){
     	$("#addReceiveAddress").bindQueryBtn({
    	        type:'store',
    	        bindClick:false,
    	        title:'${message("更换地址")}',
    	        url:'/member/store/select_store_address.jhtml?storeId='+storeId,
    	        callback:function(rows){
    	            if(rows.length>0){
    	            	var row = rows[0];
    	                $("input[name='consignee']").attr("value",row.consignee);
    	                $("input[name='phone']").attr("value",row.mobile);
    	                $("input[name='address']").attr("value",row.address);
    	                $("input[name='zipCode']").attr("value",row.zip_code);
    	                $("input[name='addressOutTradeNo']").attr("value",row.out_trade_no);
    	                $(".select_area").find(".fieldSet").empty();
    	                var areaId = row.area;
    	                if(areaId==null)areaId='';
    	                var areaName = row.area_full_name;
    	                if(areaName==null)areaName='';
    	                var treePath = row.tree_path;
    	                if(treePath==null)treePath='';
    	                $("input[name='areaId']").val(areaId);
    	                $("input[name='areaName']").val(areaName);
    	                
    	            }
    	        }   
    	 });
     }
    
    
  	//批次
    $("#selectBatch").live("click",function(){
        var $this = $(this);
        var $tr = $this.closest("tr");
        var url = '/stock/batch/edit_post.jhtml'
    	//经营组织
        var organizationId = $tr.find(".productOrganizationId").val();
        if(isNull(organizationId) == null){
        	$.message_alert("单据行项的经营组织不能为空");
			return false;
	  	}
        url+='?organizationId='+organizationId;
       	//仓库工厂
        var productionPlantId = $("input.productionPlantId").val();
       	if(isNull(productionPlantId) != null){
       		url+='&productionPlantId='+productionPlantId;
       	}
        //批次
        var batchIds = $tr.find(".batchIds").val();
        if(isNull(batchIds) != null){
        	url+='&bacthIds='+batchIds;
        }
		var iframeId = "iframeId" + (new Date()).valueOf() + Math.floor(Math.random() * 1000000);
		var $dialog = $.dialog({
			title:'查询批次',
			width:1200,
			height:508,
			content: "<iframe  id='"+iframeId+"' src='"+url+"' width='100%'  height='"+420+"px'><\/iframe>",
			onOk: function() {
				var rows = $("#"+iframeId)[0].contentWindow.childMethod();
				var allId = '';
				var allName = '';
				for(var i=0;i<rows.length;i++){
					var row = rows[i];
					if(isNull(row) != null){
						if(isNull(row.id) != null){
							if(isNull(allId) != null){
  								allId =allId +';'+ row.id; 
  								allName = allName +';'+ row.batch_encoding;
  							}else{
  								allId = row.id; 
  								allName = row.batch_encoding;
  							}
						}
					}
				}   
				$tr.find(".batchIds").val(allId);
				$tr.find(".batchEncodings").val(allName);
				[#if linkStock == 1 ] 
			 		loadAttQuantity($tr); 
			 	[/#if]
			}
		});
    })
    
    [#if linkStock == 1 ] 
	 //色号
	 $(".colourNumber").live("change", function() {
		 var $tr = $(this).closest("tr");
		 loadAttQuantity($tr); 
	 })
	 //含水率
	 $(".moistureContent").live("change", function() {
		 var $tr = $(this).closest("tr");
		 loadAttQuantity($tr); 
	 })
	[/#if]
    
});			


function select_post(e,organizationId,productionPlantId,callback){
	$(e).bindQueryBtn({
		bindClick:false,
		type:'bacth',
		title:'选择批次',
		url:'/stock/batch/select_bacth.jhtml?organizationId='+organizationId+'&productionPlantId='+productionPlantId,
		callback:function(rows){
			if(rows.length>0){
				if(callback(rows)==false){
					return false;
				}
			}
		}
	});
}

//查询库存
function queryStock(){
	countTotal();
	$("input[name='productIds']").each(function(){
		var $this = $(this);
		var productId = $this.val();
		var $tr = $this.closest("tr");
		var warehouseId = $tr.find(".warehouseId").val();
		if(warehouseId==""){
			$tr.find("#u"+productId).html(0);
			$tr.find("#a"+productId).html(0);
		}else{
			queryLineStock($tr,productId);
		}
	});
}

function queryLineStock(t,productId){
	var $tr = $(t);
	var warehouseId = $tr.find(".warehouseId").val();
	if(warehouseId==""){
			$tr.find("#u"+productId).html(0);
			$tr.find("#a"+productId).html(0);
	}else{
		ajaxSubmit('',{
			url: 'getStock.jhtml?warehouseId='+warehouseId+'&productIds='+productId,
			method: "post",
			async:false,
			callback:function(resultMsg){
				var data = $.parseJSON(resultMsg.content);
				if(data.length>0){
					$tr.find("#u"+productId).html(data[0].useable_stock);
					$tr.find("#a"+productId).html(data[0].actual_stock);
				}else{
					$tr.find("#u"+productId).html(0);
					$tr.find("#a"+productId).html(0);
				}
			}
		})
	}
}

function sub(e){
	 var data = $("#inputForm").serializeObject();
	 var result = false;
	 //判断备注长度
     var memeo=$(".memo").val();
 	 var len = 0;  
   	 for (var i=0; i<memeo.length; i++) {   
   	  	var c = memeo.charCodeAt(i);   
   	 	//单字节加1   
    	if ((c >= 0x0001 && c <= 0x007e) || (0xff60<=c && c<=0xff9f)) {   
    	   len++;   
    	} else {   
    	  len+=2;   
   	  	}   
 	 }   
	 var str = "您确定要保存吗？";
	 $.message_confirm(str,function() {
		Mask();
		ajaxSubmit(e,{
			url:"save.jhtml",
			method:"post",
			async: false,
			data:data,
			failCallback:function(resultMsg){				
				// 访问地址失败，或发生异常没有正常返回
				messageAlert(resultMsg);
				UnMask();
			},
			callback:function(resultMsg){
				location.href= '/b2b/shipping/view.jhtml?id='+resultMsg.objx;
			}
		});
	});
}

function childMethod(){
   var data = $("#inputForm").serializeObject();
   var result = false;
   ajaxSubmit(null,{
		url:"save.jhtml",
		method:"post",
		async: false,
		data:data,
		callback:function(resultMsg){
			result = true;
		}
	});
	
};

function clearSelectDriverInfo(e){
	var $this = $(e);
	var value = $this.val();
	if(value==undefined || value.length==0){
		var $tr = $(e).closest("table");
		$this.prev("input").val("");
		$tr.find(".driverInfoPhone").val("");
		$tr.find(".driverInfoCarNumber").val("");
	}
}

function clearSelectSupplier(e){
	var $this = $(e);
	var value = $this.val();
	if(value==undefined || value.length==0){
		var $tr = $(e).closest("table");
		$this.prev("input").val("");
		$tr.find(".virtualWarehouseName").val("");
		$tr.find(".virtualWarehouseId").val("");
	}
}

 function query_product(e,suiteItemId,callback){
		$(e).bindQueryBtn({
					type:'product',
					bindClick:false,
					title:'${message("查询替代件")}',
					url:'/b2b/shipping/replace_product_list.jhtml?multi=2&suiteItemId='+suiteItemId,
					callback:function(rows){
						if(callback(rows)==false){
							return false;
						}
					}
		});
}

//仓库弹框
function warehouseWin(e,vId){
	var saleOrgId = $(".saleOrgId").val();
	var sbuId = $(".sbuId").val();
	var orgId=$(".organizationId").val();
	var $e=$(e);
	var type;
	var warehouseType=${order.warehouse.typeSystemDict.id};
	if(vId==""){
		type = 0;
	}else{
		type = '';
	}
	$(e).bindQueryBtn({
		bindClick:false,
		type:'warehouse',
		title:'${message("查询仓库")}',
		url:'/stock/warehouse/select_warehouse.jhtml?type='+type+'&warehouseType='+warehouseType+'&saleOrgId='+saleOrgId+'&sbuId='+sbuId+'&organizationId='+orgId+'&isNotwarehouseType=458',
		callback:function(rows){
			if(rows.length>0){
				var t=$(e).closest("table.input-edit");
				t.find(".warehouseId").val(rows[0].id);
				t.find(".warehouseName").val(rows[0].name);
				t.find(".typesystemDictFlag").val(rows[0].type_system_dict_flag);
				t.find(".organizationId").val(rows[0].management_organization_id);
				t.find(".organizationName").text(rows[0].management_organization_name);
				t.find(".productionPlantId").val(rows[0].production_plant);
				var id = rows[0].id;
				$.ajax({
					url : '/stock/warehouse/findWarehouseSmethod.jhtml',
        			type : "post",
        			data : {id:id},
        			success : function(data) {
        				var content = JSON.parse(data.content);
        				var a = content.length;
        				var smethod = $("#smethodId");
        				smethod.empty();
        				if(a!=0){
        					var html = "";
        					for(var i=0;i<a;i++){
        						var c = content[i];
        						var id = c.shippingwayId;
        						var name = c.shippingwayName;
        						var isb = c.is_default;
        						if(isb==1){
        							html += '<option value="'+name+'" selected >'+name+'</option>';
        						}else{
	        						html += '<option value="'+name+'">'+name+'</option>';        							
        						}
        					}
        					smethod.html(html);
        				}
        			}
				})
				$("input.productId").each(function(){
					var $this = $(this);
					var $tr = $this.closest("tr");
					$tr.find(".batchIds").val('');
   					$tr.find(".batchEncodings").val('');
				});
				productOrganization();
			}
		}
	});
	
	
}
function driverInfoWin(e,dId){
	if (dId = 'undefined') {
		dId = '';
	}
	$(e).bindQueryBtn({
		bindClick:false,
		type:'driverInfo',
		title:'${message("查询车辆信息")}',
		url:'/basic/driver_info/select_driverInfo.jhtml?deliveryCorpId='+dId,
		callback:function(rows){
			if(rows.length>0){
				var t=$(e).closest("table.input-edit");
				t.find("input[name='driverInfoId']").val(rows[0].id);
				t.find(".driverInfoName").val(rows[0].name);
				t.find(".driverInfoPhone").val(rows[0].phone);
				t.find(".driverInfoCarNumber").val(rows[0].car_number);
			}
		}	
	});
}

function supplierWin(e){
	$(e).bindQueryBtn({
		bindClick:false,
		type:'supplier',
		title:'${message("查询供应商")}',
		url:'/member/store/select_store.jhtml?type=provider&isMember=1',
		callback:function(rows){
			if(rows.length>0){
				var t=$(e).closest("table.input-edit")
				t.find("input[name='supplierId']").val(rows[0].id);
				t.find("input[name='supplierName']").val(rows[0].name);
				t.find(".warehouseId").val("");
				t.find(".warehouseName").val("");
			}
		}
	});
}

//仓库弹框
function allWarehouse(e,vId){
	var $e=$(e);
	var type;
	if(vId==""){
		type = 0;
	}else{
		type = '';
	}
	$(e).bindQueryBtn({
		bindClick:false,
		type:'warehouse',
		title:'${message("查询仓库")}',
		url:'/stock/warehouse/select_warehouse.jhtml?type='+type
	});
	
	
}

</script>
</head>
<body>
	<div class="pathh">&nbsp;新增发货单</div>
	<form id="dataForm">
		[#list quantity as qty] 
			<input type="hidden" name="quantity" value="${qty}"> 
		[/#list] 
		[#list ids as id] 
			<input type="hidden" name="ids" value="${id}"> 
		[/#list] 
		[#list boxQuantity as bqty] 
			<input type="hidden" name="boxQuantity"	value="${bqty}"> 
		[/#list] 
		[#list branchQuantity as brqty] 
			<input type="hidden" name="branchQuantity" value="${brqty}">
		[/#list] 
		[#list closedQuantity as cqty] 
			<input type="hidden" name="closedQuantity" value="${cqty}"> 
		[/#list] 
	</form>
	<form id="inputForm">
		<input type="hidden" name="flag" value="0">
			<div class="tabContent">
				<table class="input input-edit">
					<tr>
						<th>${message("发货单编号")}:</th>
						<td></td>
						<th>${message("客户")}:</th>
						<td>
							<input name="storeId" class="storeId" type="hidden" value="${store.id}">
							<span>${store.name}</span>
						</td>
						<th>${message("发货单状态")}:</th>
						<td></td>
						<th>仓库:</th>
						<td>
							<span class="search" style="position: relative"> 
								<input name="warehouseId" class="text warehouseId" type="hidden" value="${warehouse.id}">
[#--                                <span>${warehouse.name}</span>--]
								<input class="text warehouseName" maxlength="200" type="text" value="${warehouse.name}" onkeyup="clearSelect(this)" readonly/>
								<input type="hidden" class="typesystemDictFlag" value="${warehouse.typeSystemDict.flag}" />
								<input type="hidden" class="productionPlantId" value="${warehouse.productionPlant.id}" />
								<input type="button" class="iconSearch" value="" id="selectWarehouse">
							</span>
						</td>
					</tr>
					<tr>
						<th>承运商:</th>
						<td>
							<select class="text" name="deliveryId" id="deliveryId">
								<option value="">-- 请选择 --</option> 
								[#list deliveryCorps as dc]
									<option value="${dc.id}">${dc.name}</option> 
								[/#list]
							</select>
						</td>
						<th>配送方式:</th>
						<td>
							<select class="text" name="shippingMethod">
								[#list shippingMethods as sm]
									<option value="${sm.name}">${sm.name}</option> 
								[/#list]
							</select>
						</td>
						<th>${message("机构")}:</th>
						<td>
							<input name="saleOrgId" class="saleOrgId" type="hidden" value="${saleOrg.id}">${saleOrg.name}</td>
						<th>经营组织:</th>
						<td>
							<input type="hidden" name="organizationId" value="${warehouse.managementOrganization.id}" class="organizationId">
							<span class="organizationName">${warehouse.managementOrganization.name}</span>
						</td>
					</tr>
					<tr>
						<th>${message("计划发货日期")}:</th>
						<td>
							<input type="text" class="text" name="shippingTime" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'});" btn-fun="clear" />
						</td>
						<th>司机姓名:</th>
						<td>
							<span class="search" style="position: relative"> 
								<input name="driverInfoId" class="text driverInfoId" type="hidden" />
								<input class="text driverInfoName" maxlength="200" type="text" onkeyup="clearSelectDriverInfo(this)" /> 
							</span>
						</td>
						<th>车主电话:</th>
						<td>
							<input type="text" class="text driverInfoPhone" btn-fun="clear" />
						</td>
						<th>车牌号:</th>
						<td>
							<input type="text" class="text driverInfoCarNumber" btn-fun="clear" />
						</td>
					</tr>
					<tr>
						<th>${message("发票抬头")}:</th>
						<td>
							<span class="search" style="position: relative">
									<input name="invoiceTitle" class="text invoiceTitle" maxlength="200" type="text" value="${invoiceTitle}" onkeyup="clearSelect(this)" readonly/>
									<input type="button" class="iconSearch" value="" id="selectInvoice" />
							</span>
						</td>
						<th>${message("创建时间")}:</th>
						<td></td>
						<th>${message("Sbu")}:</th>
						<td>
							<input type="hidden" name="sbuId" class="text sbuId" id="sbuId" btn-fun="clear" value="${sbua.id}" />
							<span id="sbuName">${sbua.name}</span>
						</td>
						<th>${message("发运方式")}:</th>
						<td>
							<select id="smethodId" name="smethod" class="text">
								[#list smethods as smethod]
									<option value="${smethod.smethod.value}" [#if smethod.isDefault==true]selected[/#if]>${smethod.smethod.value}</option> 
								[/#list]
							</select>
						</td>
					</tr>
					<tr>
						<th>
							<span class="red">*</span>${message("单据日期")}:
						</th>
						<td>
							<input id="startTime" name="glDate" class="text" value="${GLDate}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" /> 
							<input id="endTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear" />
						</td>
						
						<th>${message("区域经理")}:</th>
		                <td>
		                    <input type="hidden" name="regionalManagerId" class="text storeMemberId" btn-fun="clear" value="${store.storeMember.id}"/>
		                    <span id="regionalManagerName">
		                   	 	${store.storeMember.name}
		                    </span>
		                </td>
		                <th>${message("运输方式")}:</th>
						<td>
							<select id="transportTypeName" name="transportTypeId" class="text"> 
								[#list transportTypeList as transportType]
									<option value="${transportType.id}">${transportType.value}</option>
								[/#list]
							</select>
						</td>
						<th>${message("客户简称")}:</th>
						<td>
							<span id="storeAlias">${store.alias}</span>
						</td>
					</tr>
                    <tr>
                        <th>${message("出货类型")}:</th>
                        <td>
							<input type="hidden" name="outOfTheWarehouseTypeId" class="text outOfTheWarehouseTypeId" btn-fun="clear" value="${order.outOfTheWarehouseType.id}"/>
							<span id="outOfTheWarehouseTypeName" >${order.outOfTheWarehouseType.value}</span>
                        </td>
					[#if sbua.id == 3]
							<th>
								<span class="red">*</span>${message("是否需转采购")}:
							</th>
							<td>
								<select name="isPurchase" class="text">
									<option value="">${message("请选择")}</option>
									<option value="Y">${message("是")}</option>
									<option value="N">${message("否")}</option>
								</select>
							</td>
					[/#if]
                    </tr>
					<tr>
						<th>备注:</th>
						<td colspan="7">
							<textarea class="text memo"  name="memo">${orderMemo}</textarea>
						</td>
					</tr>
					<tr>
						<td colspan="7">
							<textarea class="mefont isMemo" id="isMemo" value=""></textarea>
						</td>
					</tr>
				</table>
				<div class="title-style">${message("发货项")}:</div>
				<table id="table-shipping"></table>
				<div class="title-style">${message("发货汇总")}</div>
				<table class="input input-edit">
					<tr>
						<!-- 2019-05-16 冯旗 壁纸隐藏箱支，重量体积-->
						[#if sbua.id != 3]
							<th>${message("发货箱数")}:</th>
							<td>
								<span id="totalBoxQuantity"></span>
							</td>
							<th>${message("发货支数")}:</th>
							<td>
								<span id="totalBranchQuantity"></span>
							</td> 
						[/#if]
						<th>${message("发货数量")}:</th>
						<td>
							<span id="totalQuantity"></span>
						</td> 
						[#if sbua.id != 3]
							<th>${message("发货重量")}:</th>
							<td>
								<span id="totalWeight"></span>
							</td> 
						[/#if]
					</tr>
					[#if sbua.id != 3]
						<tr>
							<th>${message("发货体积")}:</th>
							<td>
								<span id="totalVolume"></span>
							</td>
						</tr>
					[/#if]
				</table>
				<div class="title-style">${message("收货信息")}
					<div class="btns">
						<a href="javascript:;" id="addReceiveAddress" class="button">更换收货信息</a>
					</div>
				</div>
				<table class="input input-edit">
					<tr>
						<th>收货人:</th>
						<td>
							<input type="text" class="text" name="consignee" value="${consignee}" btn-fun="clear" readonly="readonly" />
						</td>
						<th>收货人电话:</th>
						<td>
							<input type="text" class="text" name="phone" value="${phone}" btn-fun="clear" readonly="readonly" />
						</td>
						<th>收货地区邮编:</th>
						<td>
							<input type="text" class="text" name="zipCode" value="${zipCode}" btn-fun="clear" readonly="readonly" />
						</td>
						<th>地址外部编码:</th>
						<td>
							<input type="text" class="text" name="addressOutTradeNo" value="${addressOutTradeNo}" btn-fun="clear" readonly="readonly" />
						</td>
					</tr>
					<tr>
						<th>收货地区:</th>
						<td colspan="3">
							<span class="search" style="position: relative"> 
								<input type="hidden" name="areaId" class="text areaId" value="${area.id}" btn-fun="clear" /> 
								<input type="text" name="areaName" class="text areaName" value="${area.fullName}" maxlength="200" onkeyup="clearSelect(this)" readonly="readonly" /> 
							</span>
						</td>
						<th>收货地址:</th>
						<td colspan="3">
							<input type="text" class="text" name="address" value="${address}" btn-fun="clear" readonly="readonly" />
						</td>
					</tr>
				</table>
				<div class="title-style">
					${message("附件信息")}:
					<div class="btns">
						<a href="javascript:;" id="addAttach" class="button">添加附件</a>
					</div>
				</div>
				<table id="table-attach"></table>
			</div>
			<div class="fixed-top">
				<input type="button" id="submit_button" class="button sureButton" value="${message("保存")}" onclick="sub(this)"> 
				<input type="button" onclick="reloding()" class="button resetButton ml15" value="${message("重置")}">
			</div>
	</form>
</body>
</html>