<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>添加发货</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<style type="text/css">
</style>
<script type="text/javascript">

	$.validator.addClassRules({
		productSn: {
			required: true
		},
		stockOutItemOutStock: {
			required: true,
			integer: 12
		}
	});

	$().ready(function() {
		var $inputForm = $("#inputForm");
		var $addProductTable = $("#addProductTable"); 
		var $addProduct = $("#addProduct");
		var stockOutItemIndex = 1;

		$addProduct.click(function() {
			[@compress single_line = true]
				var trHtml = 
				'<tr class="productTr">
					<input type="hidden" name="stockOutItems[0].vonderCode" class="vonderCode"/>
					<input type="hidden" name="stockOutItems[0].model" class="model"/>
					<input type="hidden" name="stockOutItems[0].productName" class="productName"/>
					<input name="stockOutItems[0].productId" class="productId" type="hidden">
					<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="productCategoryId" class="text productCategoryId" btn-fun="clear"/>
					<input type="text" name="productSn" class="text productSn" maxlength="200" onkeyup="clearSelect(this)" style="width:90px"  readOnly/>
					<input type="button" class="iconSearch" value="" onclick="selectProduct_stockOut(this)">
					</span>
					</td>
					<td><span class="vonderCode"></span></td>
					<td><span class="model"></span></td>
					<td><span class="productName"></span></td>
					<td>
						<input type="number" min="1" name="stockOutItems[0].outStock" class="text stockOutItemOutStock" style=" width:50px;"/>
					</td>
					<td>
						<input type="text" name="stockOutItems[0].note" class="text note" style=" width: 90px;"/>
					</td>
					<td>
						<a href="javascript:;" class="deleteProduct btn-delete"style="border:solid 1px #999;padding:2px 8px;border-radius: 19px;">${message("1002")}</a>
					</td>
				</tr>';
			[/@compress]
			$addProductTable.append(trHtml);
			stockOutItemIndex ++;
		});

		// 删除商品
		$("a.deleteProduct").live("click", function() {
			var $this = $(this);
			$.dialog({
				type: "warn",
				ok: "确定",
				cancel: "取消",
				content: "${message("你确定要删除吗？")}",
				onOk: function() {
					var tr = $this.closest("tr");
					var le = $("#addProductTable").find("tr").length;
					if(le == 2){
						$.dialog({
							type: "warn",
							ok: null,
							cancel: "确定",
							content: "至少保留一个明细"
						});
					}else{
						tr.remove();
					}
				}
			});
		});
		
		$("#warehouseId").change(function(){
			$("#addProductTable").find("tr").siblings("tr.productTr").remove();
			[@compress single_line = true]
				var trHtml = 
				'<tr class="productTr">
					<input type="hidden" name="stockOutItems[0].vonderCode" class="vonderCode"/>
					<input type="hidden" name="stockOutItems[0].model" class="model"/>
					<input type="hidden" name="stockOutItems[0].productName" class="productName"/>
					<input name="stockOutItems[0].productId" class="productId" type="hidden">
					<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="productCategoryId" class="text productCategoryId" btn-fun="clear"/>
					<input type="text" name="productSn" class="text productSn" maxlength="200" onkeyup="clearSelect(this)" style="width:90px"  readOnly/>
					<input type="button" class="iconSearch" value="" onclick="selectProduct_stockOut(this)">
					</span>
					</td>
					<td><span class="vonderCode"></span></td>
					<td><span class="model"></span></td>
					<td><span class="productName"></span></td>
					<td>
						<input type="number" min="1" name="stockOutItems[0].outStock" class="text stockOutItemOutStock" style=" width:50px;"/>
					</td>
					<td>
						<input type="text" name="stockOutItems[0].note" class="text note" style=" width: 90px;"/>
					</td>
					<td>
						<a href="javascript:;" class="deleteProduct btn-delete"style="border:solid 1px #999;padding:2px 8px;border-radius: 19px;">${message("1002")}</a>
					</td>
				</tr>';
				[/@compress]
			$addProductTable.append(trHtml);
			stockOutItemIndex = 1;
			
		
		})
		
		 /**初始化附件*/
    var attachIndex=0;
	var cols = [				
    	{ title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex){
    		var url = item.url;
			var fileObj = getfileObj(item.name);
			/**设置隐藏值*/
			var hideValues = {};
			hideValues['shippingAttachs['+attachIndex+'].url']=url;
			hideValues['shippingAttachs['+attachIndex+'].suffix']=fileObj.suffix;
			
			return createFileStr({
				url : url,
				fileName : fileObj.file_name,
				name : fileObj.name,
				suffix : fileObj.suffix,
				time : '',
				textName:'shippingAttachs['+attachIndex+'].name',
				hideValues:hideValues
			});               
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:590 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text file_memo" name="shippingAttachs['+attachIndex+'].memo" >'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
			attachIndex++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}}
	];
	var $amGrid=$('#table-attach').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        checkCol: false,
        autoLoad: true
    });
    
    
    var $addAttach = $("#addAttach");
	var attachIdnex = 0;
	var option1 = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row = data[i].file_info;
				$amGrid.addRow(row,null,1);
	        }
			
        }
    }
    $addAttach.file_upload(option1);
    
     var $deleteAttachment = $(".deleteAttachment");
	$deleteAttachment.live("click", function() {
		var $this = $(this);
		$this.closest("tr").remove();
	});
		
		// 表单验证
		$inputForm.validate({
			rules: {
				warehouseId: "required"
			},
			submitHandler:function(form){
			return false;
			}
			 
		});
		
		//打开选择地址界面
	    $("#addReceiveAddress").click(function(){
	        var storeId = $(".storeId").val();
	        if(storeId==""){
	            $.message_alert('请选择客户');
	        }else{
	        		select_store_address(storeId);
	        }
	    });
		
	});
	
	//打开选产品
	function selectProduct_stockOut(e){
		var warehouseId=$("#warehouseId").val();
		$.dialog({
			title:"查询产品",
			width:900,
			height:608,
			content: "<iframe  name='iframePager_stockOut' src='/product/product/selectProduct.jhtml?warehouseId="+warehouseId+"&isMarketable=true'  width='100%' height='520px'><\/iframe>",
			ok: "确定",
			cancel: "取消",
			onOk: function() {
				var rows = iframePager_stockOut.window.childMethod();
				if(rows.length>0){
					var row = rows[0];
					var $tr = $(e).closest("tr");
					$tr.find("input.productId").val(row.id);
					$tr.find("input.productSn").val(row.sn);
					$tr.find("input.vonderCode").val(row.vonderCode);
					$tr.find("input.model").val(row.model);
					
					var name = row.name;
					$tr.find("input.productName").val(name);
					$tr.find("span.productName").attr("title",name);
					$tr.find("span.productName").text(name.substr(0,15)+"...");
					$tr.find("span.vonderCode").text(row.vonderCode);
					$tr.find("span.model").text(row.model);
				}
			}
		});
	}
	
//打开选择仓库界面
function openWarehouseWindow(domEl){
	$_this = $(domEl);

	$.dialog({
		title:"查询仓库",
		width:900,
		height:608,
		content: "<iframe  name='iframePage_member' src='/stock/warehouse/select_warehouse.jhtml' width='100%'  height='520px'><\/iframe>",
		ok: "确定",
		cancel: "取消",
		onShow:function(){
				$(".xxDialog").css("top","20px");
		},
		onOk: function() {
			var rows = iframePage_member.window.childMethod();
			var $tr = $_this.closest("td");
			
			if(rows.length>0){
				var row = rows[0];
				$tr.find("input.warehouseId").val(row.id);
				$tr.find("input.warehouseName").val(row.name);
			}
		}
	});
}; 

//打开选择订单界面
function openOrderWindow(domEl){
	$_this = $(domEl);
	$.dialog({
		title:"查询订单",
		width:900,
		height:608,
		content: "<iframe  name='iframePage_order' src='/order/order/select_order.jhtml?orderStatus=6' width='100%'  height='520px'><\/iframe>",
		ok: "确定",
		cancel: "取消",
		onShow:function(){
				$(".xxDialog").css("top","20px");
		},
		onOk: function() {
			var rows = iframePage_order.window.childMethod();
			if(rows.length>0){
				var row = rows[0];
				$("input.orderId").val(row.id);
				$("input.orderSn").val(row.sn);
			}
			
			ajaxSubmit(unll,{
				url:'/order/order/get_order_info.jhtml',
				method:"post",
				data:{id:row.id},
				callback:fuction(jsonPage){
					var data = $.parseJSON(jsonPage.content);
					$("tr.productTr").remove();
			 		var items = data.items;
			 		$("span.address").html(data.address);
			 		$("span.outTradeNo").html(data.out_trade_no);
					$("span.consignee").html(data.consignee);
					$("span.zipCode").html(data.zip_code);
					$("span.phone").html(data.phone);
					$("span.area").html(data.area_name);
					$("input.warehouseName").val(data.warehouse_name);
					$("input.warehouseId").val(data.warehouse_id);
			 		var html='';
			 		for(var i=0; i<items.length;i++){
			 		
			 		var vonderCode=items[i].vonder_code==null?"":items[i].vonder_code;
			 		var model=items[i].model==null?"":items[i].model;
			 		[@compress single_line = true]
			 			html +='<tr class="productTr">
					<input name="shippingItems['+i+'].orderItem.id" class="orderItemId" type="hidden" value="'+items[i].id+'">
					<td>
						<span class="productSn"> '+items[i].sn+'</span>
					</td>
					<td><span class="vonderCode">'+vonderCode+'</span></td>
					<td><span class="model">'+model+'</span></td>
					<td><span class="productName">'+items[i].full_name+'</span></td>
					<td><span class="quantity">'+items[i].quantity+'</span></td>
					<td><span class="shippedQuantity">'+items[i].shipped_quantity+'</span></td>
					<td>
						<input value="'+(items[i].quantity-items[i].shipped_quantity)+'" type="number" min="1" max="'+(items[i].quantity-items[i].shipped_quantity)+'" name="shippingItems['+i+'].quantity" class="text shippingItemQuantity" style=" width:50px;"/>
					</td>
					<td>
						<a href="javascript:;" class="deleteProduct btn-delete"style="border:solid 1px #999;padding:2px 8px;border-radius: 19px;">${message("1002")}</a>
					</td>
				</tr>';
			 		[/@compress]
			 		
			 		}
			 		$("#addProductTable").append(html);
					
				}
			})
			
			/*$.ajax({
				url:'/order/order/get_order_info.jhtml',
				type:"POST",
			 	data:{id:row.id},
			 	dataType:"json",
			 	cache:false,
			 	success:function(data){
			 		$("tr.productTr").remove();
			 		var items = data.items;
			 		$("span.address").html(data.address);
			 		$("span.outTradeNo").html(data.out_trade_no);
					$("span.consignee").html(data.consignee);
					$("span.zipCode").html(data.zip_code);
					$("span.phone").html(data.phone);
					$("span.area").html(data.area_name);
					$("input.warehouseName").val(data.warehouse_name);
					$("input.warehouseId").val(data.warehouse_id);
			 		var html='';
			 		for(var i=0; i<items.length;i++){
			 		
			 		var vonderCode=items[i].vonder_code==null?"":items[i].vonder_code;
			 		var model=items[i].model==null?"":items[i].model;
			 		[@compress single_line = true]
			 			html +='<tr class="productTr">
					<input name="shippingItems['+i+'].orderItem.id" class="orderItemId" type="hidden" value="'+items[i].id+'">
					<td>
						<span class="productSn"> '+items[i].sn+'</span>
					</td>
					<td><span class="vonderCode">'+vonderCode+'</span></td>
					<td><span class="model">'+model+'</span></td>
					<td><span class="productName">'+items[i].full_name+'</span></td>
					<td><span class="quantity">'+items[i].quantity+'</span></td>
					<td><span class="shippedQuantity">'+items[i].shipped_quantity+'</span></td>
					<td>
						<input value="'+(items[i].quantity-items[i].shipped_quantity)+'" type="number" min="1" max="'+(items[i].quantity-items[i].shipped_quantity)+'" name="shippingItems['+i+'].quantity" class="text shippingItemQuantity" style=" width:50px;"/>
					</td>
					<td>
						<a href="javascript:;" class="deleteProduct btn-delete"style="border:solid 1px #999;padding:2px 8px;border-radius: 19px;">${message("1002")}</a>
					</td>
				</tr>';
			 		[/@compress]
			 		
			 		}
			 		$("#addProductTable").append(html);
			 	}*/
			})
			
		}
	});
};

//更换地址
function select_store_address(storeId){
	$("#addReceiveAddress").bindQueryBtn({
        type:'store',
        bindClick:false,
        title:'${message("更换地址")}',
        url:'/member/store/select_store_address.jhtml?storeId='+storeId,
        callback:function(rows){
            if(rows.length>0){
            	var row = rows[0];
                $("input[name='consignee']").attr("value",row.consignee);
                $("input[name='phone']").attr("value",row.mobile);
                $("input[name='address']").attr("value",row.address);
                $("input[name='zipCode']").attr("value",row.zip_code);
                $("input[name='addressOutTradeNo']").attr("value",row.address_out_tradeNo);
                $(".select_area").find(".fieldSet").empty();
                var areaId = row.area;
                if(areaId==null)areaId='';
                var areaName = row.area_full_name;
                if(areaName==null)areaName='';
                var treePath = row.tree_path;
                if(treePath==null)treePath='';
               // $(".select_area").find(".fieldSet").append('<input type="hidden" id="areaId" name="areaId"  value="'+areaId+'" treePath="'+treePath+'" />');
                //地区选择
               // $("#areaId").lSelect();
                $("input[name='areaId']").val(areaId);
                $("input[name='areaName']").val(areaName);
                
            }
        }   
    });
 }
 
</script>
</head>
<body>
	<div class="pathh">
		 &nbsp; 添加发货单
	</div>
	<form id="inputForm" action="save.jhtml" method="post" type="ajax" validate-type="validate" handleType="-1">
		<ul id="tab" class="tab">
			<li>
				<input type="button" value="${message("12133")}" class="current"/>
			</li>
		</ul>
		<div class="tabContent">
			<table class="input input-edit">
				<tr>
					<th>
						<span class="requiredField">*</span>发货仓库:
					</th>
					<td>
						<span class="search" style="position:relative">
							<input type="hidden" name="warehouseId" class="text warehouseId" btn-fun="clear" value="" id="warehouseId"/>
							<input type="text" name="warehouseName" class="text warehouseName" maxlength="200" value="" onkeyup="clearSelect(this)" />
							<input type="button" class="iconSearch [#if useLockStock==1] hidden[/#if]" value="" onclick="openWarehouseWindow(this);">
							</span>
					</td>
					<th>
						<span class="requiredField">*</span>订单:
					</th>
					<td>
						<span class="search" style="position:relative">
							<input type="hidden" name="orderId" class="text orderId" btn-fun="clear" value="" id="orderId"/>
							<input type="text" name="orderSn" class="text orderSn" maxlength="200" value="" onkeyup="clearSelect(this)" />
							<input type="button" class="iconSearch" value="" onclick="openOrderWindow(this);">
						</span>
					</td>
					<th>
						来源单号:
					</th>
					<td>
						<span class="outTradeNo"></span>
					</td>
				</tr>
				<tr>
					<th>
						物流快递:
					</th>
					<td >
						<select class="text" name="deliveryId">
								[#list deliveryCorps as dc]
								<option value="${dc.id}" >${dc.name}</option>
								[/#list]
							</select>
					</td>
					<th>
						配送方式:
					</th>
					<td >
						<select class="text" name="shippingMethod">
								[#list shippingMethods as sm]
								<option value="${sm.name}" >${sm.name}</option>
								[/#list]
							</select>
					</td>
					<th>
						运单号:
					</th>
					<td >
						<input name="trackingNo" type="text" class="text">
					</td>
				</tr>
				<tr>
					<th>
						收货人:
					</th>
					<td>
					<span class="consignee"></span>
					</td>
					<th>
						电话:
					</th>
					<td>
					<span class="phone"></span>
					</td>
					<th>
						地区:
					</th>
					<td>
					<span class="area"></span>
					</td>
				</tr>
				<tr>
					<th>
						地址:
					</th>
					<td>
					<span class="address"></span>
					</td>	
					<th>
						邮编:
					</th>
					<td>
					<span class="zipCode"></span>
					</td>				
				</tr>
				<tr>
					<th>
						备注:
					</th>
					<td colspan="5">
						<textarea name="memo" class="text" id="memo"></textarea>
					</td>
				</tr>
			
			<tr class="border-L1">
				<th>订单明细:</th>
				<td colspan="5">
					<table class="input table-fixed" id="addProductTable" >
						<thead>	
						<tr class="js-head">
							<th>
								产品系统编号
							</th>
							<th>
								${message("12211")}
							</th>
							<th>
								产品型号
							</th>
							<th>
								产品名称
							</th>
							<th>
								下单数量
							</th>
							<th>
								实际发货数量
							</th>
							<th>
								计划发货数量
							</th>
							
							<th style="width:60px">
								操作
							</th>
						</tr>
						</thead>
					</table>
				</td>
			</tr>
			<tr class="border-L2">
				<th>${message("附件信息")}:</th>
				<td colspan="7"><a href="javascript:;" id="addAttach" class="button">添加附件</a></td>
			</tr>
			<tr class="border-L2">
				<td colspan="8">
					<div  class="w_1135">
					<table id="table-attach"></table>
					</div>
				</td>
			</tr>
			
			
		</table>
		</div>
		
		<div class="fixed-top">
			<input type="submit" class="button sureButton" value="${message("1013")}" />
			<input type="button" class="button backButton" value="${message("1014")}" onClick="cancle_iframe();" />
		</div>
	</form>
</body>
</html>