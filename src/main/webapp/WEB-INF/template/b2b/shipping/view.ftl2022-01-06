<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("查看发货单")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/js/productCategory.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/utils.js"></script>
<script type="text/javascript" src="/resources/js/dynamicForm/searchControlType.js"></script>
<style type="text/css">
tr.s-tr, tr.s-tr td {
	height: 10px !important;
}

.mefont {
	color: red;
	font-style：italic;
	斜体
	font-weight：bold;
	加粗
	font-size：30px;
	大小
	line-height：30px;
	行高
	font-family：“SimHei”;
	字体
}
</style>
<script type="text/javascript">


function productOrganization(){
	var typesystemDictFlag = $("input.typesystemDictFlag").val();
	if(isNull(typesystemDictFlag) != null){
		$("input.productId").each(function(){
			var $this = $(this);
			var $tr = $this.closest("tr");
			if(typesystemDictFlag == 0){
				$tr.find(".productOrganizationName").text($(".organizationName").text());
				$tr.find("input.productOrganizationId").val($("input.organizationId").val());
				[#if linkStock == 1 ] 
					loadAttQuantity($tr);
				[/#if]
			}else if (typesystemDictFlag == 1){
				[#if linkStock == 1 ] 
					loadAttQuantity($tr);
				[/#if]
			}
		});
	}
}


//加载库存
function loadAttQuantity($tr){
	[#if shipmentStockQueryRoles ==0] 
		jurisdictionStockQueryRoles($tr);
		return;
	[/#if]
	//产品经营组织
	var organizationId = $tr.find(".productOrganizationId").val();
	if(isNull(organizationId) != null){
		//仓库
		var warehouseId = $("input.warehouseId").val();
		//产品
		var productId = $tr.find("input.productId").val();
		//等级
		var productGrade = $tr.find(".productGrade").val();
		//色号
		var colourNumber = $tr.find(".colourNumber").val();
		if(isNull(colourNumber) == null){
			colourNumber = "";
		}
		//含水率
		var moistureContent = $tr.find(".moistureContent").val();
		if(isNull(moistureContent) == null){
			moistureContent = "";
		}
		//批次
		var batchIds = $tr.find("input.batchIds").val();
		if(isNull(batchIds) == null){
			batchIds = "";
		}else{
			batchIds = batchIds.split(';');
		}
		var params='&warehouseId='+warehouseId+'&productId='+productId+'&productGrade='+productGrade
		  		+'&organizationId='+organizationId+'&colourNumber='+colourNumber+'&moistureContent='+moistureContent
		  		+'&batchIds='+batchIds;
			params = params.substring(1,params.length);
		$.ajax({
			url:'/stock/stock/findViewStock.jhtml?'+params,
   			type : "post",
   			success : function(rows) {
   				var data= $.parseJSON(rows.content);
                   if(data.length>0){
                       for (var i = 0; i < data.length;i++) {
	                        //可用库存箱数
	                       	if(isNull(data[i].totalAttQuantity2) != null) {
	                       		$tr.find(".attQuantity2BoxNum").text(data[i].totalAttQuantity2);		                   
	                       	}else {
	                       		$tr.find(".attQuantity2BoxNum").text(0);		                       		
	                       	}		                     
	                        //可用库存支数
	                       	if(isNull(data[i].totalAttQuantity3) != null) {
	                       		$tr.find(".attQuantity3Num").text(data[i].totalAttQuantity3);
	                       	}else {
	                       		$tr.find(".attQuantity3Num").text(0);		                       		
	                       	}
	                        //可用库存数量
	                       	if(isNull(data[i].totalAttQuantity1) != null) {
	                       		$tr.find(".attQuantity1Num").text(data[i].totalAttQuantity1);
	                       	}else {
	                       		$tr.find(".attQuantity1Num").text(0);		                       		
	                       	}
                   	}
               	}else{
               		$tr.find(".attQuantity2BoxNum").text(0);		 
               		$tr.find(".attQuantity3Num").text(0);		
               		$tr.find(".attQuantity1Num").text(0);		   
               	}
                //获取件数值
                var boxQuantity = $tr.find("input.boxQuantity").val();
                //支数
                var branchQuantity = $tr.find("input.branchQuantity").val();
                //数量
                var quantity = $tr.find("input.quantity").val();		                   
                if(boxQuantity > $tr.find(".attQuantity2BoxNum").text()) {
                	$tr.addClass("backColor");
                }else {
             	   $tr.removeClass("backColor");
                }
   			}
		})	
	}
} 






function countTotal(){
 	var $bInput = $("input.boxQuantity");
 	$bInput.each(function(){
         var quantity = 0;
         var $tr = $(this).closest("tr");	
         var boxQuantity=$(this).val();
         var branchPerBox=$tr.find(".branchPerBox").val();
         var perBox=$tr.find(".perBox").val();
         var perBranch=$tr.find(".perBranch").val();
         var scatteredQuantity=$tr.find(".scatteredQuantity").val();//零散支数
         var perBox = $tr.find(".perBox").val();//每箱单位数
         var type = productDisc(branchPerBox,perBranch,perBox);
      	if(type==0){
         	var branchQuantity=accMul(boxQuantity,branchPerBox);
         	branchQuantity=accAdd(branchQuantity,scatteredQuantity);
         	if(isNaN(branchQuantity)){
         		branchQuantity = 0;
     		}
         	$tr.find(".branchQuantity").val(branchQuantity);//支数
         	$tr.find(".branchQuantityStr").html(branchQuantity);
         	quantity=accMul(branchQuantity,perBranch);
         	if(isNaN(quantity)){
         		quantity = 0;
     		}
         	$tr.find(".quantity").val(quantity);//数量
          	$tr.find(".quantityStr").html(quantity);
         }
      	 if(type==1){
      		quantity = $tr.find(".quantity").val();
      	 }
         //辅料处理逻辑
         if(type==2){
        	 var a = accDiv(perBox,10);
        	 quantity = accMul(boxQuantity,a);
         }
         if(type==3){
        	 var branchQuantity = $tr.find(".branchQuantity").val();
        	 quantity = accMul(perBranch,branchQuantity);
         }
         $tr.find(".quantity").val(quantity);//数量
       	 $tr.find(".quantityStr").html(quantity);
	});
	var $input = $("input.quantity");
	var totalVolume = 0;
	var totalAmount = 0;
	var totalWeight = 0;
	var totalBoxQuantity = 0;
	var totalBranchQuantity = 0;
	var totalQuantity = 0;
	$input.each(function(){
		var $this = $(this);
		var $tr = $this.closest("tr");
		var quantity = Number($this.val());
		var boxQuantity = Number($tr.find("input.boxQuantity").val());
		var branchQuantity = Number($tr.find("input.branchQuantity").val());
		var volume = Number($tr.find("input.volume").val());
		var price = Number($tr.find("input.price").val());
		var weight = Number($tr.find("input.weight").val());
		//行体积=每箱体积（产品体积）* 下单箱数；
		var lineVolume = accMul(boxQuantity,volume);
		//行重量=行重量 * 数量；
		var lineWeight = accMul(quantity,weight);
		if(isNaN(lineVolume)){
			lineVolume = 0;
		}
		totalVolume = totalVolume+lineVolume;
		totalWeight = totalWeight + lineWeight;
		var lineAmount = Number(accMul(quantity,price).toFixed(2));
		if(isNaN(lineAmount)){
			lineAmount = 0;
		}
		totalAmount = totalAmount+lineAmount;
		totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
    	totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
    	totalQuantity=accAdd(totalQuantity,quantity);
	});
	$("#totalVolume").text(totalVolume.toFixed(6));
	$("#totalWeight").text(totalWeight);
	$("#amount").text(currency(totalAmount,true));
	$("#totalBoxQuantity").text(totalBoxQuantity);
	$("#totalBranchQuantity").text(totalBranchQuantity);
	$("#totalQuantity").text(totalQuantity);
}

function editLineInfo(t){
	var $tr = $(t).closest("tr");
	var quantity = Number($tr.find(".quantity").val());
	var volume = Number($tr.find("input.volume").val());
	var weight = Number($tr.find("input.weight").val());
	var price = Number($tr.find("input.price").val());
	var lineVolume = accMul(quantity,volume);
	var lineWeight = accMul(quantity,weight);
	var lineAmount = accMul(quantity,price);
	$tr.find(".volumeSpan").text(lineVolume);
	$tr.find(".weightSpan").text(lineWeight);
	$tr.find(".lineAmountSpan").text(currency(lineAmount,true));
}

function editQty(t,e){
	if(extractNumber(t,3,false,e)){
		var $tr = $(t).closest("tr");
		var branchQuantity=$tr.find(".branchQuantity").val();
		var branchPerBox=$tr.find(".branchPerBox").val();
		var perBranch=$tr.find(".perBranch").val();
		var quantity = $tr.find(".quantity").val();
		if($(t).attr("kid")=="box"){
			var quantity=$(t).val();
			branchQuantity=accMul(quantity,branchPerBox);
			if(isNaN(branchQuantity)){
    			branchQuantity = 0;
			}
    		$tr.find(".branchQuantity").val(branchQuantity);//支数
    		$tr.find(".scatteredQuantityStr").html(0);
			$tr.find(".scatteredQuantity").val(0);
		}else if($(t).attr("kid")=="branch"){
			var quantity=$(t).val();
			var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
			var box=parseInt(quantity/branchPerBox);
			var  scattered=quantity%branchPerBox;
			
			$tr.find(".boxQuantity").val(box);
			$tr.find(".scatteredQuantityStr").html(scattered);
			$tr.find(".scatteredQuantity").val(scattered);
		}if($(t).attr("kid")=="quantity"){//平方
				var $tr = $(t).closest("tr");
				var branch_quantity=0;
				var box_quantity=0;
				var quantity=$(t).val();
				var perBranch=$tr.find(".perBranch").val();  //每支单位数
				var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
				var perBox=$tr.find(".perBox").val();
				var type = productDisc(branchPerBox,perBranch,perBox);
				var scattered=0;
				if(perBranch!=0 && branchPerBox!=0){
					 branch_quantity=quantity/perBranch;
					 box_quantity=parseInt(branch_quantity/branchPerBox);
					 scattered=(branch_quantity%branchPerBox).toFixed(6);
				}
				if(type==2){
					box_quantity = modulo(quantity,perBox);
				}
				if(type==3){
					branch_quantity = modulo(quantity,perBranch);
				}
				$tr.find(".boxQuantity").val(box_quantity);
				$tr.find(".branchQuantity").val(branch_quantity);
				$tr.find(".scatteredQuantityStr").html(scattered);
				$tr.find(".scatteredQuantity").val(scattered);
				$(t).val(quantity);
			
		}
    	if(isNaN(quantity)){
    		quantity = 0;
		}
		$tr.find(".quantityStr").html(quantity);//数量
    	$tr.find(".quantity").val(quantity);

		countTotal();
		editLineInfo(t);
		addBackColor(t);
	}
}


//控制颜色
function addBackColor(t) {
	//控制颜色
	var fileDir = $(t).closest("span").text();			
	if($(t).val()>fileDir) {
		$(t).parents("tr").addClass("backColor");
	}else if($(t).val()<=fileDir) {
		$(t).parents("tr").removeClass("backColor");;
	}
}

 <!-- 2019-08-05 冯旗 检测备注字数是否超过240字符-->  
 function listenLength(){
   
  var memeo=$(".memo").val();
  var len = 0;  
    for (var i=0; i<memeo.length; i++) {   
     var c = memeo.charCodeAt(i);   
    //单字节加1   
     if ((c >= 0x0001 && c <= 0x007e) || (0xff60<=c && c<=0xff9f)) {   
       len++;   
     }   
     else {   
      len+=2;   
     }   
    }   
    if(len>80){
      $("#isMemo").val("字符长度已超过240!");
    }else{
     $("#isMemo").val("");
    }
 
 
 }
$().ready(function(){
	// 地区选择
	$("#areaId").lSelect();
	$("#selectWarehouse").click(function(){
		var vId = $(".supplierId").val();
		warehouseWin(this,vId);
	});
	$("#selectDriverInfo").click(function(){
		var dId = $("#deliveryId").val();
		driverInfoWin(this,dId);
	})
	$.validator.addClassRules({
		quantity: {
			required: true
		}
	});
	$("#wf_area").load("/wf/wf.jhtml?wfid=${shipping.wfId}");
	var items = ${jsonStr};
	var itemIndex=0;
	var cols = [	
		{ title:'${message("行号")}', name:'line_no' ,width:60, align:'center'},
		{ title:'${message("操作")}', align:'center', width:60, renderer:function(val,item){
			[#if shipping.status ==1 || shipping.status==3]
			if(item.status!=2 && item.shipped_quantity==0 ){
				return '<a href="javascript:;" class="cancelItem btn-delete">作废行</a>';
			}else{
				return '';
			}
			[/#if]
		}},
		
		{ title:'${message("产品编码")}', name:'vonder_code' ,width:120, align:'center', renderer: function(val,item,rowIndex,obj){
			return '<span class="vonderCode">'+val+'</span><input type="hidden"   class="productId" value="'+item.product+'">';
		}},
		{ title:'${message("产品名称")}', align:'center',width:100 , renderer: function(val,item,rowIndex,obj){
			var style_str = '';
			if(item.bom_flag==2){
				style_str = 'style="margin-left:25px;"';
			}
			return '<span '+style_str+'><input type="hidden" class="itemId" name="shippingItems['+itemIndex+'].id" value="'+item.id+'">'+item.name+'</span>';
		}},
		{ title:'${message("产品描述")}', name:'description',width:200, align:'center'},
		{ title:'${message("经营组织")}',width:80, align:'center', renderer: function(val,item,rowIndex,obj){
			var productOrganizationName = '';
				if(item.product_organization_name != null && item.product_organization_name !=''){
					productOrganizationName = item.product_organization_name;
				}
				var productOrganizationId = '';
				if(item.product_organization_id != null && item.product_organization_id !=''){
					productOrganizationId = item.product_organization_id;
				}
 			var html='<span class="productOrganizationName">'+productOrganizationName+'</span>'+
 				       '<input type="hidden" class="productOrganizationId text" value="'+productOrganizationId+'" />';
    		return html;
		}}, 
		{ title:'${message("产品级别")}', name:'levelName' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
			 return '<span>'+item.levelName+'</span><input type="hidden" class="productGrade"  value="'+item.level_Id+'">';
		}},						
		{ title:'${message("计划发货件数")}',[#if sbuIds ==3 ]hidden:true,[/#if] name:'box_quantity', align:'center',renderer: function(val,item,rowIndex,obj){
			var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
			var max_box=parseInt((item.oi_branch_quantity-item.ship_branch_quantity+item.branch_quantity-item.closed_quantity)/item.branch_per_box);
			if(isNaN(max_box)){
				max_box = val;
			}
			if(type==1||type==3){
				var html = '-'+
            	'<input type="hidden" kid="box" class="t boxQuantity"   value="'+val+'"   >';
        	return html;
			}else{
				var html = '<ul><li><div class="nums-input ov">'+
            	//'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty(this.nextSibling,event)">'+
            	'<input type="text" kid="box" class="t boxQuantity"  name="shippingItems['+itemIndex+'].boxQuantity" value="'+val+'" minData="0" maxData="'+val+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
            	//'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty(this.previousSibling,event)">'+
        	'</div></li><li><span class="attQuantity2BoxNum" >0</span></li></ul>';
        	return html;
			}
		}},
		{ title:'${message("支数")}',[#if sbuIds ==3 ]hidden:true,[/#if] name:'branch_quantity' ,align:'center', width:110, renderer: function(val,item,rowIndex, obj){
			var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
			var branchQuantity='';
			if(obj==undefined){
				branchQuantity = val;
			}
			var branchPerBox = item.branch_per_box==null?0:item.branch_per_box;
			var perBranch = item.per_branch==null?0:item.per_branch;
			if(type==1||type==2){
				branchQuantity=0;
				var html = '-'+
        		'<input type=hidden class="t branchPerBox" name="shippingItems['+itemIndex+'].branchPerBox" value="'+branchPerBox+'" /> '+
				'<input type=hidden class="t perBox" value="'+item.per_box+'" />'+
				'<input type=hidden class="t perBranch" name="shippingItems['+itemIndex+'].perBranch" value="'+perBranch+'" />';
			return html;
				
			}
			var html = '<ul><li><div class="nums-input ov">'+
        		//'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty(this.nextSibling,event)">'+
        		'<input type="text" kid="branch" class="t branchQuantity"  name="shippingItems['+itemIndex+'].branchQuantity" value="'+val+'" minData="0" maxData="'+val+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
        		//'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty(this.previousSibling,event)">'+
        		'<input type=hidden class="t branchPerBox" name="shippingItems['+itemIndex+'].branchPerBox" value="'+branchPerBox+'" /> '+
				'<input type=hidden class="t perBox"  value="'+item.per_box+'" />'+
				'<input type=hidden class="t 	perBranch" name="shippingItems['+itemIndex+'].perBranch" value="'+perBranch+'" />'+
    		'</div></li><li><span class="attQuantity3Num">0</span></li></ul>';
			return html;
		}},
		{ title:'${message("零散支数")}',[#if sbuIds ==3 ]hidden:true,[/#if] align:'center', width:110, renderer: function(val,item,rowIndex, obj){
			var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
			var branchPerBox=item.branch_per_box;
			var scatteredQuantity=item.branch_quantity%item.branch_per_box;
			if(isNaN(scatteredQuantity)){
				scatteredQuantity = 0;
			}
			if(type==1||type==2||type==3){
				return "-";
			}
			var html='<span class="scatteredQuantityStr">'+scatteredQuantity+'</span>'+
				'<input type="hidden" name="shippingItems['+itemIndex+'].scatteredQuantity" class="scatteredQuantity text" value="'+scatteredQuantity+'" />';
			return html;
		}},
		{ title:'${message("宽")}',[#if sbuIds != 3]hidden:true,[/#if]  name:'length', align:'center', width:110, renderer:function(val,item,rowIndex,obj){
	        var sbu=${sbuIds}
              	if(item.unit=='m2' && sbu==3){
          		  	return val+'<input type="hidden" name="shippingItems['+itemIndex+'].length" class="length text" value="'+val+'" />';
			}else{
		      	return '-'+'<input type="hidden" name="shippingItems['+itemIndex+'].length" class="length text" value="'+val+'" />';
		    }			
		}},
		{ title:'${message("高")}',[#if sbuIds != 3]hidden:true,[/#if]  name:'width', align:'center', width:110, renderer:function(val,item,rowIndex,obj){
	  		var sbu=${sbuIds}
              	if(item.unit=='m2' && sbu==3){
          		 return val+'<input type="hidden" name="shippingItems['+itemIndex+'].width" class="width text" value="'+val+'" />';
			 }else{
		      return '-'+'<input type="hidden" name="shippingItems['+itemIndex+'].width" class="width text" value="'+val+'" />';
		    
		    }			
		}},
		{ title:'${message("数量")}', name:'quantity', align:'center', width:100, renderer:function(val,item,rowIndex,obj){
			var quantity='';
			if(obj==undefined){
				quantity = val;
			}
			quantity = quantity.toFixed(6);
			quantity = parseFloat(quantity);
			if((item.branch_per_box==undefined || item.per_branch == undefined || item.branch_per_box==0 || item.per_branch == 0) && item.length!='' && item.length==0 ){
				quantity=item.quantity;
				var text = 
				'<ul><li><div class="nums-input ov">'+
		        	//'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
		        	'<input type="text"  kid="quantity"  class="t quantity" name="shippingItems['+itemIndex+'].quantity" value="'+quantity+'" maxData="'+quantity+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
		        	//'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
		    	'</div></li><li><span class="attQuantity1Num">0</span></li>';
		    	return text;
			}
			var html='<span class="quantityStr">'+quantity+'</span>'+
				'<input type="hidden" name="shippingItems['+itemIndex+'].quantity" class="t quantity" value="'+quantity+'" />';
			return html;
		}},
		{ title:'${message("产品价格/平方米")}',[#if hiddenAmount==0] hidden:true, [/#if] name:'price' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
			var html = '<span class="red">'+currency(val,true)+'</span><input type="hidden" class="price" value="'+val+'">';
			return html;
		}},
		{ title:'${message("平台结算价")}',[#if isMember!=0 ||  seeSaleOrgPrice == 0 ]hidden:true,[/#if] name:'sale_org_price' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
 				var html = '<span class="red">'+currency(val,true)+'</span><input type="hidden" class="price" value="'+val+'">';
 				return html;
 			}},
		{ title:'${message("金额")}',[#if hiddenAmount==0] hidden:true, [/#if] name:'volume' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
			var lineAmount = item.quantity*item.price;
			var html = '<span class="red lineAmountSpan">'+currency(lineAmount,true)+'</span>';
			return html;
		}},
		[#if hiddenBatch !=0]     			
				{ title:'${message("色号")}', align:'center',width:60,renderer: function(val,item,rowIndex, obj){
					var str='selected="selected"';
		   			var html='<select name="shippingItems['+itemIndex+'].colourNumbers.id" class="text colourNumber">';
			   			html+='<option value="">请选择</option> ';  		
			   			[#list colorNumberList as colorNumbers]
			   				if(${colorNumbers.id} == item.colour_numbers){
			   					html+='<option value="${colorNumbers.id}" '+str+' >${colorNumbers.value}</option>';
			   				}else{
			   					html+='<option value="${colorNumbers.id}">${colorNumbers.value}</option> ';
			   				}
		   				[/#list]
		   				html+='</select>';
		   			return html;
		        }},    
		        { title:'${message("含水率")}', align:'center',width:60,renderer: function(val,item,rowIndex, obj){
		        	var str='selected="selected"';
		   			var html='<select name="shippingItems['+itemIndex+'].moistureContents.id" class="text moistureContent">';
			   			html+='<option value="">请选择</option> ';  		
			   			[#list moistureContentList as moistureContents]
			   				if(${moistureContents.id} == item.moisture_contents){
			   					html+='<option value="${moistureContents.id}" '+str+' >${moistureContents.value}</option> ';
			   				}else{
			   					html+='<option value="${moistureContents.id}">${moistureContents.value}</option> ';
			   				}
		   				[/#list]
		   				html+='</select>';
		   			return html;
		        }},
		        { title:'${message("批次")}', align:'center', width:100, renderer: function(val,item,rowIndex, obj){
					var batch = '';
					if(isNull(item.batch) != null){
						batch = item.batch;
					}
					var batch_encoding = '';
					if(isNull(item.batch_encoding) != null){
						batch_encoding = item.batch_encoding;
					}
		        	 var html = '<span class="search" style="position:relative">'
		        		    +'<input type="hidden" name="shippingItems['+itemIndex+'].batch" class="text batchIds" value="'+batch+'" />'
		        		    +'<input type="text" name="shippingItems['+itemIndex+'].batchEncoding" class="text batchEncodings" value="'+batch_encoding+'" readonly/>'
							+'<input type="button" class="iconSearch" value="" id="selectBatch"/>'
						+'</span>';
					 return html;
		         }},
		 [/#if]	
		 {title:'${message("备注")}', name:'memo', align:'center',width:100, renderer: function(val,item,rowIndex){
			var memos='';
			if(obj==undefined){
				memos = item.memo;
			}
			var html='<input type="text" name="shippingItems['+itemIndex+'].memo" [#if shipping.status!=0]readonly[/#if] class="text" value="'+val+'" />';
			return html;
		}},
		{ title:'${message("工厂/供应商")}',[#if sbuIds !=3||isMember==1 ]hidden:true,[/#if] name:'manufactory_name', align:'center',width:250 },
		{ title:'${message("供应商型号")}',[#if sbuIds !=3||isMember==1 ]hidden:true,[/#if] name:'supplier', align:'center',width:100 },
		{ title:'${message("产品型号")}', name:'model', align:'center',width:80 },
		{ title:'${message("单位")}', align:'center',name:'unit'},				
		{ title:'${message("订单编号")}', name:'order_sn', align:'center',width:160, renderer: function(val,item,rowIndex){
			return val;
		}},
		{ title:'${message("实际发货支数 ")}',[#if sbuIds ==3 ]hidden:true,[/#if] name:'shipped_branch_quantity', align:'center', width:100, renderer:function(val,item,rowIndex,obj){
			var quantity= 0;
			if(obj==undefined){
				quantity = val;
			}
			if (val == '' || val == null ) {
				quantity = 0;
			}
			quantity = quantity.toFixed(6);
			quantity = parseFloat(quantity);
			var html='<span class="shippedBranchQuantityStr">'+quantity+'</span>'+
				'<input type="hidden" name="shippingItems['+itemIndex+'].shippedBranchQuantity" class="shippedBranchQuantity text" value="'+quantity+'" />';
			return html;	
		}},
		{ title:'${message("实际发货数量")}', name:'shipped_quantity', align:'center', width:100, renderer:function(val,item,rowIndex,obj){
			var quantity= 0;
			if(obj==undefined){
				quantity = val;
			}
			if (val == '' || val == null ) {
				quantity = 0;
			}
			quantity = quantity.toFixed(6);
			quantity = parseFloat(quantity);
			var html='<span class="shippedBranchQuantityStr">'+quantity+'</span>'+
				'<input type="hidden" name="shippingItems['+itemIndex+'].shippedQuantity" class="shippedQuantity text" value="'+quantity+'" />';
			return html;
				
		}},
		{title:'${message("实际发货金额")}',[#if hiddenAmount==0]hidden:true,[/#if] width:80, align:'center', renderer: function(val,item,rowIndex,obj){
			var lineAmount = item.shipped_quantity*item.price;
			var html = '<span class="red ">'+currency(lineAmount,true)+'</span>';
			return html;
		}},
		[#if isMember==0 || editSaleOrgPrice == 1]
			{title:'${message("实际发货平台金额")}',[#if hiddenAmount==0 || seeSaleOrgPrice == 0]hidden:true,[/#if] width:120, align:'center', renderer: function(val,item,rowIndex,obj){
				var lineAmount = 0;
				if(item.sale_org_sales_price){
					lineAmount = item.sale_org_sales_price *  item.shipped_quantity;
				}else{
					lineAmount = item.sale_org_price * item.shipped_quantity;
				}
				var html = '<span class="red ">'+currency(lineAmount,true)+'</span>';
				return html;
			}},
		[/#if]
		{ title:'${message("已签收数量")}', name:'receipt_quantity', align:'center',width:160 },
		{ title:'${message("体积")}',[#if sbuIds ==3 ]hidden:true,[/#if] name:'volume', align:'center',width:160, renderer: function(val,item,rowIndex){
			var volume = (val!="")?val:0;
			var lineVolume = volume*item.box_quantity;
			lineVolume = lineVolume.toFixed(6);
			lineVolume = parseFloat(lineVolume);
			var html = '<span class="volumeSpan">'+lineVolume+'</span>'+
				'<input type="hidden" class="volume text" name="shippingItems['+itemIndex+'].volume" value="'+volume+'" />';
			return html;
		}},
		{ title:'${message("重量")}',[#if sbuIds ==3 ]hidden:true,[/#if] name:'weight' ,width:80, align:'center', renderer: function(val,item,rowIndex,obj){
			var weight = (val!="")?val:0;
			var lineWeight = weight*item.quantity;
			lineWeight = lineWeight.toFixed(6);
			lineWeight = parseFloat(lineWeight);
			var html = '<span class="weightSpan">'+lineWeight+'</span>'+
				'<input type="hidden" class="weight text" name="shippingItems['+itemIndex+'].weight" value="'+weight+'" />';
			itemIndex++;	
			return html;	
		}}
	];
	$mmGrid = $('#table-m1').mmGrid({
		height:'auto',
        cols: cols,
        items:items,
        fullWidthRows:true,
        checkCol: false,
        autoLoad: true,
        callback:function(){
        	countTotal();
        	productOrganization();
        }
    });
	
	// 作废行
    $("a.cancelItem").live("click", function() {
        var index = $(this).closest("tr").index();
        var $tr=$(this).closest("tr");
        var itemId=$tr.find("input.itemId").val();
        
        ajaxSubmit(this,{
			url: 'cancelItem.jhtml?ids='+${shipping.id}+'&shippingItemId='+itemId,
			method: "post",
			isConfirm:true,
			confirmText:"您确定要作废当前行吗？",
			callback:function(resultMsg){
			 	$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
				})
			 }
		})
    });
    
    var orderFullLink_items = ${fullLink_json};
	var cols = [				
    	{ title:'${message("内容")}', name:'content' ,width:300,align:'center'},
		{ title:'${message("操作人")}', name:'operator_name',width:100 ,align:'center'},
		{ title:'${message("创建日期")}', name:'create_date' ,width:150 ,align:'center'}
	];
	$('#table-full').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        items:orderFullLink_items,
        checkCol: false,
        autoLoad: true
    });
    
    
     var $addAttach = $("#addAttach");
	var attachIdnex = 0;
	var option1 = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row=data[i].file_info;
				$amGrid.addRow(row,null,1);
	        }
			
        }
    }
    
     /**初始化订单附件*/
    var shippingAttach_items = ${shippingAttach_json};
    var attachIndex=0;
	var cols = [				
    	{ title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex,obj){
			if(obj==undefined){
				var url = item.url;
				var fileObj = getfileObj(item.file_name , item.name, item.suffix);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['shippingAttachs['+attachIndex+'].id']=item.id;
				hideValues['shippingAttachs['+attachIndex+'].url']=url;
				hideValues['shippingAttachs['+attachIndex+'].suffix']=fileObj.suffix;
				
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : item.create_date,
					textName:'shippingAttachs['+attachIndex+'].name',
					hideValues: hideValues
				});
			}else{
				var url = item.url;
				var fileObj = getfileObj(item.name);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['shippingAttachs['+attachIndex+'].url']=url;
				hideValues['shippingAttachs['+attachIndex+'].suffix']=fileObj.suffix;
				
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : '',
					textName:'shippingAttachs['+attachIndex+'].name',
					hideValues:hideValues
				});
			}
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:590 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text file_memo" name="shippingAttachs['+attachIndex+'].memo" >'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
			attachIndex++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}}
	];
	var $amGrid=$('#table-attach').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        items:shippingAttach_items,
        checkCol: false,
        autoLoad: true
    });
    
    $addAttach.file_upload(option1);
    
     var $deleteAttachment = $(".deleteAttachment");
	$deleteAttachment.live("click", function() {
		var $this = $(this);
		$this.closest("tr").remove();
	});

	$("form").bindAttribute({
		isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
	    }
	 });
	 
	 $("#openArea").bindQueryBtn({
		type:'area',
		title:'${message("查询地区")}',
		url:'/basic/area/select_area.jhtml',
		callback:function(rows){
			if(rows.length>0){
				var $tr =$this.closest("tr");
				$(".areaId").val(rows[0].id);
				$(".areaName").val(rows[0].full_name);
				$("input[name='zipCode']").val('');
			}
		}
	});
	 
	 $("#selectInvoice").bindQueryBtn({
			type:'supplier',
			title:'${message("查询发票抬头")}',
			url:'/member/store/select_store_invoice.jhtml?storeId='+${shipping.store.id},
			callback:function(rows){
				if(rows.length>0){
					var t=$this.closest("table.input-edit")
					t.find("input[name='invoiceTitle']").val(rows[0].invoice_title);
				}
			}
		});
	 
	  //打开选择地址界面
    $("#addReceiveAddress").click(function(){
        var storeId = $(".storeId").val();
        if(storeId==""){
            $.message_alert('请选择客户');
        }else{
        		select_store_address(storeId);
        }
    });
    
    //更换地址
    function select_store_address(storeId){
     $("#addReceiveAddress").bindQueryBtn({
	        type:'store',
	        bindClick:false,
	        title:'${message("更换地址")}',
	        url:'/member/store/select_store_address.jhtml?storeId='+storeId,
	        callback:function(rows){
	            if(rows.length>0){
	            	var row = rows[0];
	                $("input[name='consignee']").attr("value",row.consignee);
	                $("input[name='phone']").attr("value",row.mobile);
	                $("input[name='address']").attr("value",row.address);
	                $("input[name='zipCode']").attr("value",row.zip_code);
	                $("input[name='addressOutTradeNo']").attr("value",row.out_trade_no);
	                $(".select_area").find(".fieldSet").empty();
	                var areaId = row.area;
	                if(areaId==null)areaId='';
	                var areaName = row.area_full_name;
	                if(areaName==null)areaName='';
	                var treePath = row.tree_path;
	                if(treePath==null)treePath='';
	                $("input[name='areaId']").val(areaId);
	                $("input[name='areaName']").val(areaName);
	                
	            }
	        }   
	    });
     }

    // 查询经销商
    $("#openStore").click(function(){
        var saleOrgId = $(".saleOrgId").val();
        if(saleOrgId==''){
            $.message_alert('请选择机构');
            return false;
        }
        $("#openStore").bindQueryBtn({
            type:'store',
            bindClick:false,
            title:'${message("查询客户")}',
            url:'/member/store/select_store.jhtml?isMember=1&type=distributor[#if companyId==19]&isSl=1[/#if]',
            callback:function(rows){
                if(rows.length>0){
                    var row = rows[0];
                    $(".storeId").val(row.id);
                    $(".storeName").val(row.name);
                    $("input[name='serviceProvider']").attr("value",row.id);
                }
            }
        });

    })

    $("select#serviceCharge").change(function(){
        if($(this).val() == "false"){
            $("#openStore").attr("disabled","disabled");
            $("#servicePrice").attr("disabled","disabled");
            $("#serviceExplain").attr("disabled","disabled");
            $("#openStore").attr("disabled","disabled");
        } else {
            $("#openStore").removeAttr("disabled");
            $("#servicePrice").removeAttr("disabled");
            $("#serviceExplain").removeAttr("disabled");
            $("#openStore").removeAttr("disabled");
        }
    });
    
    [#if linkStock == 1 ] 
		 //色号
		 $(".colourNumber").live("change", function() {
			 var $tr = $(this).closest("tr");
			 loadAttQuantity($tr); 
		 })
		 //含水率
		 $(".moistureContent").live("change", function() {
			 var $tr = $(this).closest("tr");
			 loadAttQuantity($tr); 	
		 })
	[/#if]
    
    
	//批次
    $("#selectBatch").live("click",function(){
        var $this = $(this);
        var $tr = $this.closest("tr");
        var url = '/stock/batch/edit_post.jhtml'
    	//经营组织
        var organizationId = $tr.find(".productOrganizationId").val();
        if(isNull(organizationId) == null){
        	$.message_alert("单据行项的经营组织不能为空");
			return false;
	  	}
        url+='?organizationId='+organizationId;
       	//仓库工厂
        var productionPlantId = $("input.productionPlantId").val();
       	if(isNull(productionPlantId) != null){
       		url+='&productionPlantId='+productionPlantId;
       	}
        //批次
        var batchIds = $tr.find(".batchIds").val();
        if(isNull(batchIds) != null){
        	url+='&bacthIds='+batchIds;
        }
		var iframeId = "iframeId" + (new Date()).valueOf() + Math.floor(Math.random() * 1000000);
		var $dialog = $.dialog({
			title:'查询批次',
			width:1200,
			height:508,
			content: "<iframe  id='"+iframeId+"' src='"+url+"' width='100%'  height='"+420+"px'><\/iframe>",
			onOk: function() {
				var rows = $("#"+iframeId)[0].contentWindow.childMethod();
				var allId = '';
				var allName = '';
				for(var i=0;i<rows.length;i++){
					var row = rows[i];
					if(isNull(row) != null){
						if(isNull(row.id) != null){
							if(isNull(allId) != null){
  								allId =allId +';'+ row.id; 
  								allName = allName +';'+ row.batch_encoding;
  							}else{
  								allId = row.id; 
  								allName = row.batch_encoding;
  							}
						}
					}
				}   
				$tr.find(".batchIds").val(allId);
				$tr.find(".batchEncodings").val(allName);
				[#if linkStock == 1 ] 
			 		loadAttQuantity($tr); 
			 	[/#if]
			}
		});
    })
    
    
})

function select_post(e,organizationId,productionPlantId,callback){
	$(e).bindQueryBtn({
		bindClick:false,
		type:'bacth',
		title:'选择批次',
		url:'/stock/batch/select_bacth.jhtml?organizationId='+organizationId+'&productionPlantId='+productionPlantId,
		callback:function(rows){
			if(rows.length>0){
				if(callback(rows)==false){
					return false;
				}
			}
		}
	});
}


function check_pdf(e,id){
	ajaxSubmit(e,{
		url:"checkPdf.jhtml",
		method:"post",
		data:{ids:id},
		async: false,
		callback:function(resultMsg){
			window.open(resultMsg.content);
		}
	});
}

function reject(e){
	ajaxSubmit(e,{
		url: 'reject.jhtml?ids='+${shipping.id},
		method: "post",
		isConfirm:true,
		confirmText:"您确定要驳回当前发货通知单吗？",
		callback:function(resultMsg){
		 	$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
		 }
	})
}

//作废
function shippingCancel(e){
	var str = "您确定要作废吗？";
	$.message_confirm(str,function() {
		Mask();
		ajaxSubmit(e,{
			url:'cancel.jhtml?ids='+${shipping.id},
			method:"post",
			failCallback:function(resultMsg){				
				// 访问地址失败，或发生异常没有正常返回
				messageAlert(resultMsg);
				UnMask();
			},
			callback:function(resultMsg){
				location.reload(true);
			}
		});
	});
}

function callStart(e){
	var id = $(e).prev("span.search").find("input.warehouseId").val();
	iframePager_asign_warehouse.window.startProcess(e,id);
}

function callStop(e){
	iframePager_asign_warehouse.window.stopProcess(e);
}

function callClose(t,e){
	$(e).closest(".xxDialog").children(".dialogClose").click();
	if(t==1){
		location.load(true);
	}
	
}

function shippingAudit(){
	var bottomContent =
    	'<input type="button" class="button orderStar" value="开始执行" onclick="callStart(this)" >'+
    	'<input type="button" class="button backButton back-btn-one" value="取消" onclick="callClose(0,this)">'+
    	'<input type="button" class="button orderStop" value="中断执行" style="display:none;" onclick="callStop(this)">'+
    	'<input type="button" class="button backButton back-btn-two" value="返回" style="display:none;" onclick="callClose(1,this)">';

	var $content = '<iframe name="iframePager_asign_warehouse" src="/b2b/shipping/shippinglist.jhtml?ids='+${shipping.id}+'" width="100%" height="482px"><\/iframe>';
	var $dialog_win = $.dialog({
			title:"${message("发货单审核")}",
			width:1200,
			height:570,
			content: $content,
			bottomContent:bottomContent,
			closeIconHide:true,
			ok: null,
			cancel: null,
		});

}
function check_wf(e){
	var $this = $(e);
	var $form = $("#inputForm");
	if($form.valid()){
		$.message_confirm("您确定要审批流程吗？",function(){
			var url="check_wf.jhtml?ids="+${shipping.id};
			var data = $form.serialize();
			
			ajaxSubmit(e,{
				 url: '/wf/wf_obj_config/get_config.jhtml?obj_type_id=57&objid=${shipping.id}',
				 method: "post",
				 callback:function(resultMsg){
				 	var rows = resultMsg.objx;
				 	if(rows.length==1){
				 		data = data+'&objConfId='+rows[0].id;
				 		ajaxSubmit('',{
							 url: url,
							 method: "post",
							 data: data,
							 callback:function(resultMsg){
								$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
									reflush_wf();
								});
							 }
						})
				 	}else{
				 		var str = '';
					 	for(var i=0;i<rows.length;i++){
					 		var row = rows[i];
					 		str+='<option value="'+row.id+'">'+row.wf_temp_name+'</option>';
					 	}
					 	var content = '<table class="input input-edit" style="width:100%">'
								+'<tbody><tr><th>流程模版</th>'
								+'<td>'
									+'<select class="text" id="objConfId">'
										+str
									+'</select>'
								+'</td>'
							+'</tr></tbody></table>';
						var $dialog_check = $.dialog({
							title:"${message("发货单审核")}",
							height:'135',
							content: content,
							onOk:function(){
								var objConfId = $("#objConfId").val();
								if(objConfId=='' || objConfId == null){
									$.message_alert("请选择流程模版");
									return false;
								}
								url = url+'&objConfId='+objConfId;
								
								ajaxSubmit($this,{
									 url: url,
									 method: "post",
									 callback:function(resultMsg){
										$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
											reflush_wf();
										});
									 }
								})
								
							}
						});
				 	}
				 	
				 }
			})
		});
	}
}

// 审核
function check(e){
	var $this = $(e);
	var $form = $("#inputForm");
	if($form.valid()){
		var url="check.jhtml";
		var data = $form.serialize()+"&flag=1";
		var str = "您确定要审核吗？";
		$.message_confirm(str,function() {
			Mask();
			ajaxSubmit($this,{
				url: url,
				method: "post",
				data: data,
				failCallback:function(resultMsg){				
					// 访问地址失败，或发生异常没有正常返回
					messageAlert(resultMsg);
					UnMask();
				},
				callback:function(resultMsg){
					location.reload(true);
				}
			});
		});
	}
}

function saveAttach(e){
	var data = $("#inputForm").serialize();
	var str = "您确定要保存附件吗？";
	$.message_confirm(str,function() {
		Mask();
		ajaxSubmit(e,{
			url: 'saveAttach.jhtml',
			method: "post",
			isConfirm:true,
			data:data,
			confirmText:"",
			failCallback:function(resultMsg){				
				// 访问地址失败，或发生异常没有正常返回
				messageAlert(resultMsg);
				UnMask();
			},
			callback:function(resultMsg){
				location.reload(true);
			}
		});
	});

}

//仓库弹框
function warehouseWin(e,vId){
	var saleOrgId = $(".saleOrgId").val();
	var sbuId = $(".sbuId").val();
	var orgId=$(".organizationId").val();
	var $e=$(e);
	var type;
	var warehouseType=${warehouse.typeSystemDict.id};
	if(vId==""){
		type = 0;
	}else{
		type = '';
	}
	$(e).bindQueryBtn({
		bindClick:false,
		type:'warehouse',
		title:'${message("查询仓库")}',
		url:'/stock/warehouse/select_warehouse.jhtml?type='+type+'&warehouseType='+warehouseType+'&saleOrgId='+saleOrgId+'&sbuId='+sbuId+'&organizationId='+orgId+'&isNotwarehouseType=458',
		callback:function(rows){
			if(rows.length>0){
				var t=$(e).closest("table.input-edit");
				t.find(".warehouseId").val(rows[0].id);
				t.find(".warehouseName").val(rows[0].name);
				t.find(".typesystemDictFlag").val(rows[0].type_system_dict_flag);
				t.find(".organizationId").val(rows[0].management_organization_id);
				t.find(".organizationName").text(rows[0].management_organization_name);
				t.find(".productionPlantId").val(rows[0].production_plant);
				var id = rows[0].id;
				$.ajax({
					url : '/stock/warehouse/findWarehouseSmethod.jhtml',
        			type : "post",
        			data : {id:id},
        			success : function(data) {
        				var content = JSON.parse(data.content);
        				var a = content.length;
        				var smethod = $("#smethodId");
        				smethod.empty();
        				if(a!=0){
        					var html = "";
        					for(var i=0;i<a;i++){
        						var c = content[i];
        						var id = c.shippingwayId;
        						var name = c.shippingwayName;
        						var isb = c.is_default;
        						if(isb==1){
        							html += '<option value="'+name+'" selected >'+name+'</option>';
        						}else{
	        						html += '<option value="'+name+'">'+name+'</option>';        							
        						}
        					}
        					smethod.html(html);
        				}
        			}
				});
				$("input.productId").each(function(){
					var $this = $(this);
					var $tr = $this.closest("tr");
					$tr.find(".batchIds").val('');
   					$tr.find(".batchEncodings").val('');
				});
				productOrganization();
			}
		}
	});
}

//锁库弹框
function lockInfoWin(e){
	var shippingSn = $("input[name=shippingSn]").val()
	var $th = $(".mmg-head").find("th");
	var sy = 0;
	for(var j=0;j<$th.length;j++){
		if($th.eq(j).find(".mmg-title").text()=="产品编码"){
			sy = j;
		}	
	}
	var $tr = $("#table-m1").find("tr");
	var param = "";
	for(var i=0;i<$tr.length;i++){
		var a = $tr.eq(i).find("td");
		param +="&params="+a.eq(sy).find(".nowrap").text();
	}

	$(e).bindQueryBtn({
		bindClick:false,
		type:'lock',
		title:'${message("锁库查询")}',
		url:'/b2b/shipping/select_lock_list.jhtml?'+param+"&shippingSn="+shippingSn,
		callback:function(rows){
			if(rows.length>0){
				var t=$(e).closest("table.input-edit");				
			}
		}
	});	
}
function driverInfoWin(e,dId){
	$(e).bindQueryBtn({
		bindClick:false,
		type:'driverInfo',
		title:'${message("查询车辆信息")}',
		url:'/basic/driver_info/select_driverInfo.jhtml?deliveryCorpId='+dId,
		callback:function(rows){
			if(rows.length>0){
				if(rows[0].id != $("input[name='driverInfoId']").attr("value") ){
					$mmGrid.removeRow();
					countTotal();
				}
				var t=$(e).closest("table.input-edit")
				t.find("input[name='driverInfoId']").val(rows[0].id);
				t.find(".driverInfoName").val(rows[0].name);
				t.find(".driverInfoPhone").val(rows[0].phone);
				t.find(".driverInfoCarNumber").val(rows[0].car_number);
			}
		}	
	});
}

function clearSelectDriverInfo(e){
	var $this = $(e);
	var value = $this.val();
	if(value==undefined || value.length==0){
		var $tr = $(e).closest("table");
		$this.prev("input").val("");
		$tr.find(".driverInfoPhone").val("");
		$tr.find(".driverInfoCarNumber").val("");
	}
}

function save(e, flag){
    isNum();
	var $input = $("input.pprice");
	var str = '您确定要保存吗？'
	var url = 'update.jhtml';
	if($("#inputForm").valid()){
		$.message_confirm(str,function() {
			Mask();
			ajaxSubmit(e,{
				url: url,
				data:$("#inputForm").serialize(),
				method: "post",
				failCallback:function(resultMsg){				
					// 访问地址失败，或发生异常没有正常返回
					messageAlert(resultMsg);
					UnMask();
				},
				callback:function(resultMsg){
					location.reload(true);
				}
			});
		});
	}
}

function clearSelect(e){
    var $this = $(e);
    var value = $this.val();
    if(value==undefined || value.length==0){
        var $tr = $(e).closest("div.search");
        $this.prev("input").val("");
    }
}

function isNum() {
    if ($("#servicePrice").val() != '') {
        var reg = /^[0-9]+.?[0-9]*$/;
        var pattern = new RegExp(reg);
        if (!pattern.test($("#servicePrice").val())){
            $.message_alert("服务费金额请输入数值类型");
            $("#servicePrice").val(0);
		}

    }
}

</script>
</head>
<body>
	<div class="pathh">&nbsp;${message("查看发货单")}</div>
	<form id="inputForm" action="update.jhtml" method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="id" id="pid" value="${shipping.id}" /> 
		<input type="hidden" name="status" id="status" value="" /> 
		<input type="hidden" name="flag" value="0">
			<div class="tabContent order-info">
				<table class="input input-edit">
					<tr>
						<th>${message("发货单编号")}:</th>
						<td>
							<samp>${shipping.sn}</samp>
							<input type="hidden" class="no" name="shippingSn" value="${shipping.sn}" />
						</td>
						<th>${message("ERP单号")}:</th>
						<td>${shipping.erpSn}</td>
						<th>${message("客户")}:</th>
						<td>${shipping.store.name} <input type="hidden"
							class="storeId" value="${shipping.store.id}"></td>
						<th>${message("发货单状态")}:</th>
						<td>[#if shipping.status==0] 未审核 [#elseif shipping.status==1]
							已审核 [#elseif shipping.status==2] 已作废 [#elseif shipping.status==3]
							部分发货 [#elseif shipping.status==4] 完全发货 [/#if]</td>
					</tr>
					<tr>
						<th>${message("承运商")}:</th>
						<td>
							<select class="text" name="deliveryId" id="deliveryId">
									<option value="">-- 请选择 --</option> 
									[#list deliveryCorps as dc]
										<option value="${dc.id}" [#if shipping.delivery.id==dc.id]selected[/#if]>${dc.name}</option>
									[/#list]
							</select>
						</td>
						<th>${message("配送方式")}:</th>
						<td>
							<select class="text" name="shippingMethod">
								[#list shippingMethods as sm]
									<option value="${sm.name}" [#if shipping.shippingMethod=sm.name]selected[/#if]>${sm.name}</option>
								[/#list]
							</select>
						</td>
						<th>${message("机构")}:</th>
						<td>
							<input type="hidden" name="saleOrgId" class="saleOrgId" value="${shipping.saleOrg.id}">
							<samp>${shipping.saleOrg.name}</samp>
						</td>
						<th>经营组织:</th>
						<td>
							<input type="hidden" name="organizationId" class="organizationId" value="${shipping.organization.id}">
							<span class="organizationName">${shipping.organization.name}</span>
						</td>
					</tr>
					<tr>
						<th>${message("计划发货日期")}:</th>
						<td><input type="text" class="text" name="shippingTime"
							value="${shipping.shippingTime}"
							onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'});" btn-fun="clear" />
						</td>
						<th>${message("车主姓名")}:</th>
						<td><input type="text" class="text driverInfoName"
							name="driverInfoName" id="driverInfoName"
							value="${shipping.driverInfoName}" /></td>
						<th>${message("车主电话")}:</th>
						<td><input type="text" class="text driverInfoMobile"
							name="driverInfoMobile" id="driverInfoMobile"
							value="${shipping.driverInfoMobile}" /></td>
						<th>${message("车牌号")}:</th>
						<td><input type="text" class="text carNumber"
							name="carNumber" id="carNumber" value="${shipping.carNumber}" />
						</td>
					</tr>
					<tr>
						<th>仓库:</th>
						<td>
							<span class="search" style="position: relative"> 
								<input name="warehouseId" class="text warehouseId" type="hidden" value="${shipping.warehouse.id}">
                                [#--<span>${shipping.warehouse.name}</span>--]
                                <input class="text warehouseName" maxlength="200" type="text" value="${shipping.warehouse.name}" onkeyup="clearSelect(this)" readonly/>
								<input type="hidden" class="typesystemDictFlag" value="${shipping.warehouse.typeSystemDict.flag}" /> 
								<input type="hidden" class="productionPlantId" value="${shipping.warehouse.productionPlant.id}" />
								<input type="button" class="iconSearch" value="" id="selectWarehouse">
							</span> 
						</td>
						<th>${message("发票抬头")}:</th>
						<td>
							<span class="search" style="position: relative"> 
								<input name="invoiceTitle" class="text invoiceTitle" maxlength="200" type="text" value="${shipping.invoiceTitle}" onkeyup="clearSelect(this)" readonly/>
								<input type="button" class="iconSearch" value="" id="selectInvoice">
							</span>
						</td>
						<th>金额：</th>
						<td>
							<span class="red" id="amount" [#if hiddenAmount==0]style="display: none;"[/#if]>${currency(shipping.amount,true)}</span>
						</td>
						<th></th>
						<td></td>
					</tr>
					<tr>
						<th>${message("实际出库时间")}:</th>
						<td>
							<samp>${(shipping.erpDate?string("yyyy-MM-dd HH:mm:ss"))!}</samp>
						</td>
						<th>${message("创建时间")}:</th>
						<td>
							<span>${shipping.createDate?string("yyyy-MM-dd HH:mm:ss")}</span>
						</td>
						<th>${message("创建人")}:</th>
						<td>
							<samp>${shipping.storeMember.name}</samp>
						</td>
						<th>${message("Sbu")}:</th>
						<td>
							<input type="hidden" name="sbuId" class="text sbuId" id="sbuId" btn-fun="clear" value="${shipping.sbu.id}" />
							<span id="sbuName"> ${shipping.sbu.name} </span>
						</td>
					</tr>
					<tr>
						<th>${message("是否服务费")}:</th>
						<td>
							<select id="serviceCharge" name="serviceCharge" class="text">
								<option value="false" [#if isServiceCharge=="0"]selected[/#if]>否</option>
								<option value="true" [#if isServiceCharge=="1"]selected[/#if]>是</option>
							</select>
						</td>
						<th><span class="requiredField">*</span>${message("服务商")}:</th>
						<td>
							<span class="search" style="position: relative"> 
								<input type="hidden" class="text storeId" value="${shipping.serviceProvider}" btn-fun="clear" /> 
								<input type="text" class="text storeName" maxlength="200" value="${serviceProvider}" onkeyup="clearSelect(this)" [#if isServiceCharge== "0"]disabled[/#if] /> 
								<input type="hidden" name="serviceProvider" class="text serviceProvider" value="" btn-fun="clear" /> 
								<input type="button" class="iconSearch" value="" id="openStore" [#if isServiceCharge=="0"]disabled[/#if]>
							</span>
						</td>
						<th>${message("服务费金额")}:</th>
						<td>
							<input type="text" onblur="isNum();" class="text servicePrice" name="servicePrice" id="servicePrice" value="${shipping.servicePrice}" [#if isServiceCharge== "0"]disabled[/#if]/>
						</td>
						<th>${message("服务费说明")}:</th>
						<td>
							<input type="text" class="text serviceExplain" name="serviceExplain" id="serviceExplain" value="${shipping.serviceExplain}" [#if isServiceCharge== "0"]disabled[/#if]/>
						</td>
					</tr>
					<tr>
						<th>${message("发运方式")}:</th>
						<td>
							<select id="smethodId" name="smethod" class="text">
								[#list smethods as smethod]
									<option value="${smethod.smethod.value}" [#if shipping.smethod==smethod.smethod.value]selected[/#if]>${smethod.smethod.value}</option>
								[/#list]
							</select>
						</td>
						<th>
							<span class="requiredField">*</span>
							${message("单据日期")}:
						</th>
						<td>
							<input id="startTime" name="glDate" class="text" value="${shipping.glDate}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" /> 
							<input id="endTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear" />
						</td> 
						[#if shipping.sbu.id == 3]
							<th><span class="red">*</span>${message("是否需转采购")}:</th>
							<td><select name="isPurchase" class="text">
									<option value="">${message("请选择")}</option>
									<option value="Y" [#if shipping.isPurchase=='Y']selected[/#if]>${message("是")}</option>
									<option value="N" [#if shipping.isPurchase=='N']selected[/#if]>${message("否")}</option>
							</select></td> 
					   [/#if]
					   <th>${message("区域经理")}:</th>
	                   <td>${shipping.regionalManager.name }</td>
					   [#if pageType>0]  
							<th>单据类型:</th>
							<td>
								<input type="text" class="text driverInfoCarNumber" btn-fun="clear" value="${typeName}" readonly="true"/>
							</td>
					   [/#if]
					</tr>
					<tr>
						<th>${message("客户简称")}:</th>
						<td>
							<div>${shipping.store.alias}</div>
						</td>
						<th>${message("运输方式")}:</th>
						<td>
							[#if shipping.status==0] 
								<select class="text" name="transportTypeId"> 
									[#list transportTypeList as transportType]
										<option value="${transportType.id}" [#if shipping.transportType.id==transportType.id]selected="selected"[/#if]>${transportType.value}</option>
									[/#list]
								</select> 
							[#elseif shipping.status != 0]
								${shipping.transportType.value} 
							[/#if]
						</td>
                        <th>${message("出货类型")}:</th>
                        <td>
							<input type="hidden" name="outOfTheWarehouseTypeId" class="text outOfTheWarehouseTypeId" value="${shipping.outOfTheWarehouseType.id}" btn-fun="clear" />
							<span  id="outOfTheWarehouseTypeId" >${shipping.outOfTheWarehouseType.value}</span>
                        </td>
						<th></th>
						<td></td>
					</tr>
					<tr>
						<th>${message("备注")}:</th>
						<td colspan="7">
							<textarea class="text memo"  name="memo">${shipping.memo}</textarea>
						</td>
					</tr>
					<tr>
						<td colspan="7">
							<textarea class="mefont isMemo" id="isMemo" value=""></textarea>
						</td>
					</tr>
					<tr>
						<th>
							<span class="requiredField">*</span>${message("ERP发运备注")}:
						</th>
						<td colspan="7">${shipping.erpRemark}</td>
					</tr>
				</table>
				<div class="title-style">${message("发货项")}:</div>
				<table id="table-m1"></table>
				<div class="title-style">${message("发货汇总")}</div>
				<table class="input input-edit">
					<tr>
						<!-- 2019-05-16 冯旗 壁纸隐藏箱支，重量体积 -->
						[#if sbuIds !=3 ]
							<th>${message("发货箱数")}:</th>
							<td>
								<span id="totalBoxQuantity"></span>
							</td>
							<th>${message("发货支数")}:</th>
							<td>
								<span id="totalBranchQuantity"></span>
							</td> 
						[/#if]
						<th>${message("发货数量")}:</th>
						<td>
							<span id="totalQuantity"></span>
						</td>
						[#if sbuIds !=3 ]
							<th>${message("发货重量")}:</th>
							<td>
								<span id="totalWeight">${shipping.weight!0}</span>
							</td> 
						[/#if]
					</tr>
					[#if sbuIds !=3 ]
						<tr>
							<th>${message("发货体积")}:</th>
							<td>
								<span id="totalVolume">${shipping.volume!0}</span>
							</td>
						</tr>
					[/#if]
				</table>
				<div class="title-style">${message("收货信息")}
					<div class="btns">
						<a href="javascript:;" id="addReceiveAddress" class="button">更换收货信息</a>
					</div>
				</div>
				<table class="input input-edit">
					<tr>
						<th>${message("收货人")}:</th>
						<td>
							<input type="text" class="text" name="consignee" value="${shipping.consignee}" btn-fun="clear" readonly="readonly" />
						</td>
						<th>${message("收货人电话")}:</th>
						<td>
							<input type="text" class="text" name="phone" value="${shipping.phone}" btn-fun="clear" readonly="readonly" />
						</td>
						<th>${message("收货地区邮编")}:</th>
						<td>
							<input type="text" class="text" name="zipCode" value="${shipping.zipCode}" btn-fun="clear" readonly="readonly" />
						</td>
						<th>地址外部编码:</th>
						<td>
							<input type="text" class="text" name="addressOutTradeNo" value="${shipping.addressOutTradeNo}" btn-fun="clear" readonly="readonly" />
						</td>
					</tr>
					<tr>
						<th>${message("收货地区")}:</th>
						<td colspan="3">
							<span class="search" style="position: relative"> 
								<input type="hidden" name="areaId" class="text areaId" value="${(shipping.area.id)!}" btn-fun="clear" />
								<input type="text" name="areaName" class="text areaName" value="${(shipping.area.fullName)!}" maxlength="200" onkeyup="clearSelect(this)" readonly="readonly" />
							</span>
						</td>
						<th>${message("收货地址")}:</th>
						<td colspan="3">
							<input type="text" class="text" name="address" value="${shipping.address}" btn-fun="clear" readonly="readonly" />
						</td>
					</tr>
				</table>
				<div class="title-style">
					${message("附件信息")}:
					<div class="btns">
						<a href="javascript:;" id="addAttach" class="button">添加附件</a>
					</div>
				</div>
				<table id="table-attach"></table>
				<div class="title-style">${message("全链路信息")}:</div>
				<table id="table-full"></table>
			</div>
			<div class="fixed-top">
            [#if isEdit!=0]
                <input type="hidden" id = "menuId" value="${menuId}"/>
                <input type="hidden" id="orderIndex" value="${orderIndex}"/>
                <input type="button" id="next_order" class="button "
                           value="${message("下一单")}" onclick="toEditOrder(${menuId},${orderIndex})"/>
                [#if isMember==0]
                    <input type="button" onclick="lockInfoWin(this)" class="button" style="background-color: hsl(190, 72%, 44%);" value="${message("锁库查询")}">
                [/#if]
                [#if shipping.status ==0]
                    <a href="javascript:void(0);" class="button sureButton" onclick="save(this, 0)">${message("保存")}</a>
                    <input type="button" class="button sureButton" value="${message("审核")}" onclick="check(this)" />
                    <a href="javascript:void(0);" class="iconButton" id="fenpeiButton" onclick="shippingCancel(this)">
                        <span class="fenpeiIcon">&nbsp;</span>
                        ${message("作废")}
                    </a>
                [/#if]
                [#if flag==1 && shipping.status!=0]
                    <input type="button" class="button sureButton" value="${message("保存附件")}" onclick="saveAttach(this)">
                [/#if]
            [/#if]
                <input type="button" class="button pdfButton" value="${message("查看PDF")}" onclick="check_pdf(this,${shipping.id})" />
                <input type="button" onclick="reloding()" class="button refreshButton ml15" value="${message("刷新")}">
			</div>
	</form>
	<div id="wf_area" style="width: 100%"></div>
</body>
</html>