<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("添加退货")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/productCategory.js"></script>
<script type="text/javascript" src="/resources/js/utils.js"></script>
<script type="text/javascript">

		function productOrganization(){
			var warehouseId = $("input.warehouseId").val();
			var typesystemDictFlag = $("input.typesystemDictFlag").val();
			if(isNull(typesystemDictFlag) != null){
				clearOrganizationBalance();
				$("input.productId").each(function(){
					var $this = $(this);
					var productId = $this.val();
					var $tr = $this.closest("tr");
					if(typesystemDictFlag == 0){
						$tr.find(".productOrganization").empty();
						$tr.find(".productOrganization").append("<option value="+$("input.organizationId").val()+">"+$("#organizationId").text()+"</option>");
						loadDifference($tr);
    					[#if linkStock == 1 ]
							loadAttQuantity($tr);
    					[/#if]
					}else if (typesystemDictFlag == 1){
						if(isNull(productId) != null){
							$.ajax({
								url : '/product/product/findProductOrganizationList.jhtml',
				    			type : "post",
				    			data : {id:productId},
				    			success : function(data) {
				    				var content = JSON.parse(data.content);
				    				if(content.length!=0){
				    					var html = "";
				    					var organizationId = null;
				    					var productOrganizationId = $tr.find(".productOrganization option:selected").val();
				    					var productOrganizationText = $tr.find(".productOrganization option:selected").text();
				    					var orderItem = $tr.find(".orderItem").val();
				    					var shippingItem = $tr.find(".shippingItem").val();
				    					var isTrue = true;
				    					for(var i=0; i<content.length; i++){
				    						var isDefaults = content[i].is_defaults;
				    						if(isNull(orderItem) != null ||  isNull(shippingItem) != null){
				    							if(productOrganizationId !=content[i].organization){
				    								html += '<option value="'+ content[i].organization+'">'+content[i].organization_name+'</option>';
				    							}
				    						}else{
				    							html += '<option value="'+ content[i].organization+'">'+content[i].organization_name+'</option>';
				    							if(productOrganizationId==content[i].organization){
				    								isTrue = false;
				    								organizationId = content[i].organization;
				    							}else{
					        						if(i==0){
					    								organizationId = content[i].organization;
					    							}else if(isDefaults==1 && isTrue){
					    								organizationId = content[i].organization;
					    							}
				    							}
				    						}
				    					}
				    					if(isNull(orderItem) != null ||  isNull(shippingItem) != null){
			    							if(isNull(productOrganizationId) != null){
			    								organizationId = productOrganizationId;
				    							html += '<option value="'+productOrganizationId+'">'+productOrganizationText+'</option>';
			    							}else{
			    								organizationId = '';
				    							html += '<option value="">请选择</option>';
			    							}
			    						}
				    					$tr.find(".productOrganization").empty();
				    					$tr.find(".productOrganization").html(html);
			    						$tr.find(".productOrganization option[value='"+organizationId+"']").attr("selected",true);
			    						loadDifference($tr);
				    					[#if linkStock == 1 ]
											loadAttQuantity($tr);
				    					[/#if]
				    				}
				    			}
							})
						}
					}
				});
			}
		}


		//加载库存
		function loadAttQuantity($tr){
			[#if orderStockQueryRoles ==0]
				jurisdictionStockQueryRoles($tr);
				return;
			[/#if]
			//获取产品经营组织
			var productOrganizationId = $tr.find(".productOrganization").val();
			if(isNull(productOrganizationId) != null){
				//仓库
    			var warehouseId = $("input.warehouseId").val();
    			//产品
    			var productId = $tr.find("input.productId").val();
    			//等级
    			var productGrade = $tr.find(".productGrade").val();
    			//色号
    			var colourNumber = $tr.find(".colourNumber").val();
    			if(isNull(colourNumber) == null){
    				colourNumber = "";
    			}
    			//含水率
    			var moistureContent = $tr.find(".moistureContent").val();
    			if(isNull(moistureContent) == null){
    				moistureContent = "";
    			}
    			//批次
    			var batchIds = $tr.find("input.batchIds").val();
    			if(isNull(batchIds) == null){
    				batchIds = "";
    			}else{
    				batchIds = batchIds.split(';');
    			}
    			var params='&warehouseId='+warehouseId+'&productId='+productId+'&productGrade='+productGrade
 			  		+'&organizationId='+productOrganizationId+'&colourNumber='+colourNumber+'&moistureContent='+moistureContent
 			  		+'&batchIds='+batchIds;
    				params = params.substring(1,params.length);
	    		$.ajax({
	    			url:'/stock/stock/findViewStock.jhtml?'+params,
	       			type : "post",
	       			success : function(rows) {
	       				var data= $.parseJSON(rows.content);
	                       if(data.length>0){
	                           for (var i = 0; i < data.length;i++) {
	                          	//可用库存箱数
		                       	if(isNull(data[i].totalAttQuantity2) != null) {
		                       		$tr.find(".attQuantity2BoxNum").text(data[i].totalAttQuantity2);
		                       	}else {
		                       		$tr.find(".attQuantity2BoxNum").text(0);
		                       	}
		                      	//可用库存支数
		                       	if(isNull(data[i].totalAttQuantity3) != null) {
		                       		$tr.find(".attQuantity3Num").text(data[i].totalAttQuantity3);
		                       	}else {
		                       		$tr.find(".attQuantity3Num").text(0);
		                       	}
		                      	//可用库存数量
		                       	if(isNull(data[i].totalAttQuantity1) != null) {
		                       		$tr.find(".attQuantity1Num").text(data[i].totalAttQuantity1);
		                       	}else {
		                       		$tr.find(".attQuantity1Num").text(0);
		                       	}
	                       	}
	                   	}else{
	                   		$tr.find(".attQuantity2BoxNum").text(0);
	                   		$tr.find(".attQuantity3Num").text(0);
	                   		$tr.find(".attQuantity1Num").text(0);
	                   	}
	       			}
	    		})
			}
        }

        //经营组织余额
    	function organizationBalance(storeId,sbuId){
    		ajaxSubmit('',{
    		   method:'POST',
    		   url:'/member/customer_recharge/list_data.jhtml',
    		   data:{storeId:storeId,sbuId:sbuId},
    		   callback:function(rows) {
    			   var row= $.parseJSON(rows.content);
    			   if(row.length>0){
    				   for (var i = 0; i < row.length;i++) {
    					   $m2_mmGrid.addRow(row[i],null,1);
    				   }
    			   }
    		   }
    	   });
    	}



   function select_post(e,organizationId,productionPlantId,callback){
		$(e).bindQueryBtn({
			bindClick:false,
			type:'bacth',
			title:'选择批次',
			url:'/stock/batch/select_bacth.jhtml?organizationId='+organizationId+'&productionPlantId='+productionPlantId,
			callback:function(rows){
				if(rows.length>0){
					if(callback(rows)==false){
						return false;
					}
				}
			}
		});
	}


    	//清除退货金额
    	function clearOrganizationBalance(){
    		$("input.organizationIds").each(function(){
  				 var organizationId = $(this).val();
  				 var $trs = $(this).closest("tr");
  				 if(isNull(organizationId) != null){
					 //退货金额
					 $trs.find(".returnBalance").text(currency(0,true));
  				 }
			});
    	}


		//差额
		function loadDifference($tr){
			var productOrganizationId = $tr.find(".productOrganization").val();
			if(isNull(productOrganizationId) != null){
				//订单行金额
				var amount = $tr.find(".trprice").text().substring(1);
				$("input.organizationIds").each(function(){
					 var organizationId = $(this).val();
					 var $trs = $(this).closest("tr");
					 if(isNull(organizationId) != null){
						 if(productOrganizationId == organizationId){
							//退货金额
							$trs.find(".returnBalance").text(currency(accAdd($trs.find(".returnBalance").text().substring(1),amount),true));
						 }
					 }
				})
			}
		}


		//计算差额
		function calculationDifference(){
		   clearOrganizationBalance();
		   $("input.productId").each(function(){
			   var $tr = $(this).closest("tr");
			   //订单行经营组织
			   var productOrganizationId = $tr.find(".productOrganization").val();
			   if(isNull(productOrganizationId) != null){
					//订单行金额
					var amount = $tr.find(".trprice").text().substring(1);
					$("input.organizationIds").each(function(){
						 var organizationId = $(this).val();
						 var $trs = $(this).closest("tr");
						 if(isNull(organizationId) != null){
							 if(productOrganizationId == organizationId){
								//退货金额
								$trs.find(".returnBalance").text(currency(accAdd($trs.find(".returnBalance").text().substring(1),amount),true));
							 }
						 }
					})
				}
		   });
		}

        $.validator.addClassRules({
            productId: {
                required: true
            },
            quantity: {
                required: true,
                min:0
            },
            storeName: {
                required: true
            },
            warehouseName:{
                required: true
            },
            reason:{
                required: true
            },
            glDate:{
            	 required: true
            }
        });

        function editQty(t,e){
            if($(t).attr("kid")=="quantity"){//平方
                if(extractNumber(t,6,false,e)){
                    var $tr = $(t).closest("tr");
                    var branch_quantity=0;
                    var box_quantity=0;
                    var scattered=0;

                    var quantity=$(t).val();
                    var perBranch=$tr.find(".perBranch").val();  //每支单位数
                    var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
                    var perBox=$tr.find(".perBox").val();
                    var type = productDisc(branchPerBox,perBranch,perBox);
                    if(type==2){
                        box_quantity = modulo(quantity,perBox);
                    }
                    if(type==3){
                        branch_quantity = modulo(quantity,perBranch);
                    }
                    if(perBranch!=0 && branchPerBox!=0&&perBranch!=undefined&&branchPerBox!=undefined){
                        branch_quantity=quantity/perBranch;
                        box_quantity=parseInt(branch_quantity/branchPerBox);
                        scattered=(branch_quantity%branchPerBox).toFixed(6);
                    }else{
                        box_quantity = null;
                    }

                    $tr.find(".boxQuantity").val(box_quantity);
                    $tr.find(".branchQuantity").val(branch_quantity);
                    $tr.find(".scatteredQuantityStr").html(scattered);
                    $tr.find(".scatteredQuantity").val(scattered);

                    countTotal($tr);
                    $(t).val(quantity);

                }
            }else{
                if(extractNumber(t,3,false,e)){
                    var $tr = $(t).closest("tr");

                    var branch_quantity=0;
                    var box_quantity=0;

                    if($(t).attr("kid")=="box"){//箱
                        $tr.find(".scatteredQuantityStr").html(0);
                        $tr.find(".scatteredQuantity").val(0);
                    }else if($(t).attr("kid")=="branch"){//支
                        var quantity=$(t).val();
                        var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
                        if(branchPerBox != 0 ) {
                            var box = parseInt(quantity / branchPerBox);
                            var scattered = quantity % branchPerBox;
                            $tr.find(".boxQuantity").val(box);
                            $tr.find(".scatteredQuantityStr").html(scattered);
                            $tr.find(".scatteredQuantity").val(scattered);
                        }
                    }else if($(t).attr("kid")=="quantity"){//平方
                        var quantity=$(t).val();
                        var perBranch=$tr.find(".perBranch").val();  //每支单位数
                        var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
                        var branch_quantity = quantity / perBranch;
                        if(branchPerBox != 0 ) {
                            var box_quantity = parseInt(branch_quantity / branchPerBox);
                            var scattered = branch_quantity % branchPerBox;
                            $tr.find(".boxQuantity").val(box_quantity);
                            $tr.find(".scatteredQuantityStr").html(scattered);
                            $tr.find(".scatteredQuantity").val(scattered);
                        }
                    }

                    countTotal($tr);
                }
            }
        }
        function editPrice(t,e){
            extractNumber(t,2,false,e);
        }

        function newCountTotal(){
            var totalBoxQuantity = 0;
            var totalBranchQuantity = 0;
            var $bInput = $("input.quantity");
            $bInput.each(function(){
                var $tr = $(this).closest("tr");
                var quantity=$(this).val();
                var branchPerBox=$tr.find(".branchPerBox").val();
                var perBranch=$tr.find(".perBranch").val();
                var scatteredQuantity=$tr.find(".scatteredQuantity").val();//零散支数
                var perBox = $tr.find(".perBox").val();
                var boxQuantity = $tr.find(".boxQuantity").val()
                var type = productDisc(branchPerBox,perBranch,perBox);
                if(type==2){
                    var a = accDiv(perBox,10);
                    quantity = accMul(boxQuantity,a);
                }
                if(type==3){
                    var branchQuantity = $tr.find(".branchQuantity").val();
                    if(branchQuantity!=undefined){
                        quantity = accMul(branchQuantity,perBranch);
                    }
                }
                if(type==1){
                    //quantity =
                    quantity = quantity;
                }
                if(type==0){
                    var branchQuantity=accMul(boxQuantity,branchPerBox);
                    branchQuantity=accAdd(branchQuantity,scatteredQuantity);
                    if(isNaN(branchQuantity)){
                        branchQuantity = 0;
                    }
                    totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
                    totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
                    $tr.find(".branchQuantity").val(branchQuantity);//支数
                    $tr.find(".branchQuantityStr").html(branchQuantity);
                    var quantity=accMul(branchQuantity,perBranch);
                    if(isNaN(quantity)){
                        quantity = 0;
                    }
                    $tr.find(".quantity").val(quantity);//数量
                    $tr.find(".quantityStr").html(quantity);
                }
                $tr.find(".quantityStr").html(quantity);
                $tr.find(".quantity").val(quantity);
            });
            $("#totalBoxQuantity").text(totalBoxQuantity);
            $("#totalBranchQuantity").text(totalBranchQuantity);
            var $input = $("input.quantity");
            var total = 0;
            var totalVolume = 0;
            var totalWeight = 0;
            var totalQuantity = 0;
            var b = $("#storeBalance").val();
            $input.each(function(){
                var $this = $(this);
                var $tr = $this.closest("tr");
                var $price_box = $tr.find(".price-box");
                var price;
                if($price_box.length==0 || $price_box.prop("checked")==false){
                    price = Number($tr.find("input.price").val());
                }else{
                    price = Number($tr.find("input.origMemberPrice ").val());
                }
                var volume=$tr.find(".lineVolume").val();
                var weight=$tr.find(".lineWeight").val();
                var volumeAmount=Number(accMul($this.val(),volume)).toFixed(6);
                var weightAmount=Number(accMul($this.val(),weight)).toFixed(6);
                var amount = Number($this.val())*price;
                totalQuantity=Number(accAdd(totalQuantity,Number($this.val()))).toFixed(6);
                if(isNaN(amount)){
                    amount = 0;
                }
                total = Number(total)+Number(currency(amount,false));
                $tr.find(".trprice").html(currency(amount,true));//订单行金额
                if(isNaN(volumeAmount)){
                    volumeAmount = 0;
                }
                totalVolume = accAdd(totalVolume,Number(volumeAmount).toFixed(6));
                $tr.find(".lineVolumeAmount").html(volumeAmount);//体积
                if(isNaN(weightAmount)){
                    weightAmount = 0;
                }
                totalWeight = accAdd(totalWeight,Number(weightAmount).toFixed(6));
                $tr.find(".lineWeightAmount").html(weightAmount);//重量
            });
            $("#total").text(currency(total,true));
            $("#totalVolume").text(totalVolume);
            $("#totalWeight").text(totalWeight);
            $("#totalQuantity").text(totalQuantity);
            $("input[name='amount']").val(currency(total,false));
            var storeBalance = $("#storeBalance").val()
            $("#chae").text(currency(storeBalance-total,true));
        }

        function countTotal(t,str){
        	var Str = str;
            var totalBoxQuantity = 0;
            var totalBranchQuantity = 0;
            var $bInput = $("input.boxQuantity");
            $bInput.each(function(){
                var quantity = 0;
                var $tr = $(this).closest("tr");
                var isEqual = null;
                if(t!=undefined){
	                isEqual = (t.find(".line_no").text() == $tr.find(".line_no").text());
                }
                var boxQuantity=$(this).val();
                var branchPerBox=$tr.find(".branchPerBox").val();
                var perBranch=$tr.find(".perBranch").val();
                var scatteredQuantity=$tr.find(".scatteredQuantity").val();//零散支数
                var perBox = $tr.find(".perBox").val();
                var type = productDisc(branchPerBox,perBranch,perBox);
                if(isEqual==null){
	                if(type==2){
	                    var bQuantity = $tr.find(".boxQuantity").val();
	                    var a = accDiv(perBox,10);
	                    quantity = accMul(bQuantity,a);
	                }
	                if(type==3){
	                    var branchQuantity = $tr.find(".branchQuantity").val();
	                    if(branchQuantity!=undefined){
	                        quantity = accMul(branchQuantity,perBranch);
	                    }
	                }
	                if(type==1){
	                    quantity = $tr.find(".quantity").val();
	                }
	                if(type==0){
	                    var branchQuantity=accMul(boxQuantity,branchPerBox);
	                    branchQuantity=accAdd(branchQuantity,scatteredQuantity);
	                    if(isNaN(branchQuantity)){
	                        branchQuantity = 0;
	                    }
	                    totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
	                    totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
	                    $tr.find(".branchQuantity").val(branchQuantity);//支数
	                    $tr.find(".branchQuantityStr").html(branchQuantity);
	                    var quantity=accMul(branchQuantity,perBranch);
	                    if(isNaN(quantity)){
	                        quantity = 0;
	                    }
	                    $tr.find(".quantity").val(quantity);//数量
	                    $tr.find(".quantityStr").html(quantity);
	                }
	                $tr.find(".quantityStr").html(quantity);
	                $tr.find(".quantity").val(quantity);
                }else{
                	if(type==2&&isEqual){
	                    var bQuantity = $tr.find(".boxQuantity").val();
	                    var a = accDiv(perBox,10);
	                    quantity = accMul(bQuantity,a);
	                    $tr.find(".quantityStr").html(quantity);
		                $tr.find(".quantity").val(quantity);
	                }
	                if(type==3&&isEqual){
	                    var branchQuantity = $tr.find(".branchQuantity").val();
	                    if(branchQuantity!=undefined){
	                        quantity = accMul(branchQuantity,perBranch);
	                    }
	                    $tr.find(".quantityStr").html(quantity);
		                $tr.find(".quantity").val(quantity);
	                }
	                if(type==1&&isEqual){
	                    quantity = $tr.find(".quantity").val();
	                    $tr.find(".quantityStr").html(quantity);
		                $tr.find(".quantity").val(quantity);
	                }
	                if(type==0&&isEqual){
	                    var branchQuantity=accMul(boxQuantity,branchPerBox);
	                    branchQuantity=accAdd(branchQuantity,scatteredQuantity);
	                    if(isNaN(branchQuantity)){
	                        branchQuantity = 0;
	                    }
	                    totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
	                    totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
	                    $tr.find(".branchQuantity").val(branchQuantity);//支数
	                    $tr.find(".branchQuantityStr").html(branchQuantity);
	                    var quantity=accMul(branchQuantity,perBranch);
	                    if(isNaN(quantity)){
	                        quantity = 0;
	                    }
	                    $tr.find(".quantity").val(quantity);//数量
	                    $tr.find(".quantityStr").html(quantity);
	                }
                }
            });
            $("#totalBoxQuantity").text(totalBoxQuantity);
            $("#totalBranchQuantity").text(totalBranchQuantity);
            var $input = $("input.quantity");
            var total = 0;
            var totalVolume = 0;
            var totalWeight = 0;
            var totalQuantity = 0;
            var b = $("#storeBalance").val();
            $input.each(function(){
                var $this = $(this);
                var $tr = $this.closest("tr");
                var $price_box = $tr.find(".price-box");
                var price;
                if($price_box.length==0 || $price_box.prop("checked")==false){
                    price = Number($tr.find("input.price").val());
                }else{
                    price = Number($tr.find("input.origMemberPrice ").val());
                }
                var volume=$tr.find(".lineVolume").val();
                var weight=$tr.find(".lineWeight").val();
                var volumeAmount=Number(accMul($this.val(),volume)).toFixed(6);
                var weightAmount=Number(accMul($this.val(),weight)).toFixed(6);
				var originalPrice = $tr.find(".proPriceHeadPrice").val();
				var discountPrice = $tr.find(".price").val();
				if(originalPrice == 0){
					originalPrice = price;
					$tr.find(".proPriceHeadPrice").val(originalPrice);//原价
					$tr.find(".proPriceHeadPrice").html(currency(originalPrice,true));//原价
				}
                var amount = Number($this.val())*price;
				var priceDifference = $tr.find(".priceDifference").val()
				if(typeof (Str)=="undefined" ) {
					if (priceDifference == null || priceDifference == '') {
						priceDifference = accSub(originalPrice, discountPrice);
						$tr.find(".priceDifference").val(priceDifference);//价差
						$tr.find(".priceDifference").html(currency(priceDifference, true));//价差
					}
                    if(originalPrice == 0) {
                        $tr.find(".discount").val(100);//折扣率
                        $tr.find(".discount").html(100);
                    }
				}
				var discount = $tr.find(".discount").val();
				/*if( discount == null || discount == '' ) {
					discount = Number((discountPrice / originalPrice * 100)).toFixed(2);
					$tr.find(".discount").val(discount);//折扣率
					$tr.find(".discount").html(discount);
				}*/
                totalQuantity=Number(accAdd(totalQuantity,Number($this.val()))).toFixed(6);
                if(isNaN(amount)){
                    amount = 0;
                }
                total = Number(total)+Number(currency(amount,false));
                $tr.find(".trprice").html(currency(amount,true));//订单行金额
                if(isNaN(volumeAmount)){
                    volumeAmount = 0;
                }
                totalVolume = accAdd(totalVolume,Number(volumeAmount).toFixed(6));
                $tr.find(".lineVolumeAmount").html(volumeAmount);//体积

                if(isNaN(weightAmount)){
                    weightAmount = 0;
                }
                totalWeight = accAdd(totalWeight,Number(weightAmount).toFixed(6));
                $tr.find(".lineWeightAmount").html(weightAmount);//重量
            });
            if(isNull(t) != null){
           		var productOrganizationId = t.find(".productOrganization").val();
           		if(isNull(productOrganizationId) != null){
           			calculationDifference();
           		}
            }
            $("#total").text(currency(total,true));
            $("#totalVolume").text(totalVolume);
            $("#totalWeight").text(totalWeight);
            $("#totalQuantity").text(totalQuantity);
            $("input[name='amount']").val(currency(total,false));
            var storeBalance = $("#storeBalance").val()
            $("#chae").text(currency(storeBalance-total,true));
        }


		 <!-- 2019-05-24 冯旗 长宽算数量-->
		 function editNumber(t,e){
		       if($(t).attr("kid")=="length"){
			        if(extractNumber(t,6,false,e)){
			        	 var $tr = $(t).closest("tr");
			       		 var length=$(t).val();
			       	 	 var width=$tr.find(".width").val();
			         	 var quantity = accMul(length,width);
			         	 $tr.find(".quantity").val(quantity);
			         	 $tr.find(".quantityStr").html(quantity);
			       	 	 countTotal($tr);
			       	 }
		       }else if($(t).attr("kid")=="width"){
			        if(extractNumber(t,6,false,e)){
			       		 var $tr = $(t).closest("tr");
			        	 var width=$(t).val();
			        	 var length=$tr.find(".length").val();
			             var quantity = accMul(length,width);
			             $tr.find(".quantity").val(quantity);
			           	 $tr.find(".quantityStr").html(quantity);
			             countTotal($tr);
			         }
			}
		}

        //金额查询
        function product_price(e){
            var $tr = $(e).closest("tr");
            var productGrades = $tr.find(".productGrades").val();
            var storeId = $("input[name='storeId']").val();
            var productId = $tr.find(".productId").val();
            var warehouseId = $("input[name='warehouseId']").val();//warehouseId
            var saleOrgId = $("input[name='saleOrgId']").val();//saleOrgId
            var productGrade = $tr.find(".productGrade option:selected").val();
            var sbuId = $("input[name='sbuId']").val();//sbuId
            var memberId = $(".memberRankId").val();// memberRankId
            var orderSn = $tr.find(".orderSn").val();// orderSn
			if(orderSn){
				$tr.find("#productGrade").val(productGrades);
				$.message_alert("${message("参考订单的单价不可以更改产品等级")}");
				return false;
			}
            ajaxSubmit(e,{
                method:'post',
                url:'/product/product_price_head/findProductPrice.jhtml',
                data:{storeId:storeId,productId:productId,warehouseId:warehouseId,saleOrgId:saleOrgId,
                    productGrade:productGrade,sbuId:sbuId,memberId:memberId},
                callback:function(resultMsg) {
                    var data = resultMsg.objx;
                    if (data !=null) {
						if (isNaN(data.store_member_price)) {
                            $tr.find("#productGrade").val(productGrades);
                            $.message_alert("${message("请在价格表维护该产品价格")}");
                            return false;
                        }else {
                            $tr.find("input.price").val(data.store_member_price);
                            $tr.find(".price").val(currency(data.store_member_price,true));
                            $tr.find(".price").text(currency(data.store_member_price,true));
                            $tr.find(".productGrades").val(productGrade);


							//销售价价差
							$tr.find(".priceDifference").val(0);
							$tr.find("span.priceDifference").text(currency(0,true));
							//原价
							[#if editProductPrice==1]
							$tr.find(".proPriceHeadPrice").val(data.store_member_price);
							[#else]
							$tr.find(".proPriceHeadPrice").val(data.store_member_price);
							$tr.find(".proPriceHeadPrice").text(currency(data.store_member_price,true));
							[/#if]
							//折扣
							$tr.find(".discount").val(100);

							//平台结算价（包括原平台结算价）
							[#if editSaleOrgPrice==1]
							$tr.find("input.saleOrgPrice").val(data.sale_org_price);
							$tr.find("input.reSaleOrgPrice").val(data.sale_org_price);
							[#else]
							$tr.find("input.saleOrgPrice").val(data.sale_org_price,true);
							$tr.find(".saleOrgPriceText").text(currency(data.sale_org_price,true));
							$tr.find("input.reSaleOrgPrice").val(data.sale_org_price);
							$tr.find(".reSaleOrgPriceText").text(currency(data.sale_org_price,true));
							[/#if]
							//产品部结算价（包括原产品部结算价）
							$tr.find("input.productOrgPrice").val(data.product_org_price);
							$tr.find("input.reProductOrgPrice").val(data.product_org_price);
							$tr.find(".productOrgPriceText").text(currency(data.product_org_price,true));
							$tr.find(".reProductOrgPriceText").text(currency(data.product_org_price,true));

							//结算价价差
							$tr.find(".orgPriceDifference").val(0);
							$tr.find("span.orgPriceDifference").text(currency(0,true));

							//金额
							$tr.find("span.trprice").text(currency(0,true));
                        }
                    }else {
                        $tr.find("#productGrade").val(productGrades);
                        $.message_alert("${message("请在价格表维护该产品价格")}");
                        return false;
                    }
                    countTotal();
                    [#if linkStock == 1 ]
                    	loadAttQuantity($tr);
        			[/#if]
                }
            });
        }


        var line_no = 1;
        $().ready(function() {

            var $inputForm = $("#inputForm");
            var b2bReturnsItemIndex = 0;
            var $findOrder = $("#findOrder");
            var $addProduct = $("#addProduct");
            var $findShipping =$("#findShipping");
            var $addSaleOrgs = $("#openSaleOrg");
            var cols = [
                { title:'${message("行号")}', width:40, align:'center',renderer: function(val,item,rowIndex){
                        return '<span class="line_no">'+ (rowIndex+1)+'</span>';
                }},
                { title:'操作', align:'center',  width:60, renderer: function(val,item,rowIndex){
                        return '<a href="javascript:;" class="deleteProduct btn-delete">删除</a>';
                }},
                { title:'${message("产品编码")}', align:'center', name:'vonder_code' ,width:120,  renderer: function(val,item,rowIndex,obj){
        			return '<span class="vonderCode">'+val+'</span>';
        		}},
                { title:'产品名称', align:'center',name:'name',width:100, renderer: function(val,item,rowIndex){
                     var pid=item.product;
                     if(pid==undefined){
                         pid=item.id;
                     }
                     var itemId='';
                     if(item.item_id!=undefined){
                         itemId=item.item_id;
                     }
                     var sItemId=''
                     if(item.shipping_item!=undefined){
                         sItemId=item.shipping_item;
                     }
                     return '<a href="javascript:void(0);" onClick="product_view('+item.id+')" class="red">'+item.name+'</a>'+
                             '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].product.id" class="productId productId_'+pid+'" value="'+pid+'" id="productId">'+
                             '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].orderItem.id" class="orderItem" value="'+itemId+'">'+
                             '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].shippingItem.id" class="shippingItem" value="'+sItemId+'">';
               }},
                { title:'${message("产品描述")}', name:'description' ,align:'center',width:200, renderer: function(val,item,rowIndex, obj){
                        return val;
                }},

                { title:'${message("经营组织")}',width:80, align:'center', renderer: function(val,item,rowIndex,obj){
         			var html='';
         			html +='<select name="b2bReturnsItems['+b2bReturnsItemIndex+'].productOrganization.id" class="text productOrganization">';
	         			var productOrganizationName = '';
	     				if(item.product_organization_name != null && item.product_organization_name !=''){
	     					productOrganizationName = item.product_organization_name;
	     				}
	     				if(item.product_organization_id != null && item.product_organization_id !='' && item.product_organization_id !='undefined'){
	     					html+='<option value="'+item.product_organization_id+'" selected="selected" >'+productOrganizationName+'</option> ';
	     				}
         			html+='</select>';
            		return html;
         		}},
                { title:'${message("产品等级")}', name:'level_Id' ,align:'center', width:80, renderer: function(val,item,rowIndex, obj){
                        var str='selected="selected"';
            			var html='<select name="b2bReturnsItems['+b2bReturnsItemIndex+'].productLevel.id" onchange="product_price(this)" class="text productGrade" id="productGrade">';
            				[#list productLevelList as products]
            				if(${products.id}==item.level_Id){
            					html+='<option value="${products.id}" '+str+' >${products.value}</option> ';
            				}else{
            					html+='<option value="${products.id}">${products.value}</option> ';
            				}
            				[/#list]
            				html+='</select><input type="hidden" value="'+ item.level_Id +'" class="t productGrades"/>';
            			return html;
                }},
                {hidden:'true', title:'${message("体积")}', name:'volume' ,align:'center', renderer: function(val,item,rowIndex, obj){
                        return '<span class="lineVolumeAmount"></span><input class="lineVolume" value="'+val+'" type="hidden" />';
                }},
                { hidden:'true',title:'${message("重量")}', name:'weight' ,align:'center', renderer: function(val,item,rowIndex, obj){
                        return '<span class="lineWeightAmount"></span><input class="lineWeight" value="'+val+'" type="hidden" />';
                }},
				{ title:'${message("退货件数")}',[#if sbu.id ==3 ]hidden:true,[/#if] name:'box_quantity', align:'center', width:80, renderer:function(val,item,rowIndex,obj){
                    var type = productCategory(item.branch_per_box,item.per_branch,item.per_box,item.pb);
                    var quantity = 1;

                    if(obj==undefined){
                        quantity = val;
                    }
                    if(val != ""){
                        quantity = val;
                    }
                    if(isNaN(val)){
                        quantity = 0;
                    }

                    if(type==1||type==3){
                        return '-'+
                                '<input type="hidden"  class="t boxQuantity" name="b2bReturnsItems['+b2bReturnsItemIndex+'].boxQuantity"  value="0" />';
                    }else{
                        var text = '<ul><li><div class="lh20">'+
                                '<div class="nums-input ov">'+
                              //  '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
                                '<input type="text" kid="box" class="t boxQuantity"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].boxQuantity" value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,\'\')}else{this.value=this.value.replace(/\\D/g,\'\')}"  >';
                              //  '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">';
                        if(item.pb!=undefined&&item.pb!=undefined){
                            text +='<input type="hidden"  class="perBox" value="'+item.pb+'">'
                        }
                        if(item.per_box!=0&&item.per_box!=undefined){
                            text +='<input type="hidden"  class="perBox" value="'+item.per_box+'">'
                        }
                        text += '</div></li><li><span class="attQuantity2BoxNum" >0</span></li></ul>';
                        return text;
                    }
               }},
			   { title:'${message("支数")}',[#if sbu.id ==3 ]hidden:true,[/#if] name:'branch_quantity' ,align:'center', width:80, renderer: function(val,item,rowIndex, obj){
                    var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
                    var branchQuantity='';
                    if(obj==undefined){
                        branchQuantity = val;
                    }
                    if(val != ""){
                        branchQuantity = val;
                    }
                    var branch_per_box='';
                    var  per_branch='';

                    if(type==1||type==2){
                        return '-'+'<input type=hidden  name="b2bReturnsItems['+b2bReturnsItemIndex+'].perBranch" value="0" />';
                    }else{
                        var branchPerBox = item.branch_per_box==null?"":item.branch_per_box;
                        var text = '<ul><li><div class="lh20">'+
                                '<div class="nums-input ov">'+
                               // '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
                                '<input type="text" kid="branch" class="t branchQuantity"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchQuantity" value="'+branchQuantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
                                //'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
                                '<input type=hidden class="branchPerBox" name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchPerBox" value="'+branchPerBox+'" /> '+
                                '<input type=hidden class="perBranch" name="b2bReturnsItems['+b2bReturnsItemIndex+'].perBranch" value="'+item.per_branch+'" />'+
                                '</div></div></li><li><span class="attQuantity3Num">0</span></li></ul>';
                        return text;
                    }
                }},
			    { title:'${message("零散支数")}',[#if sbu.id ==3 ]hidden:true,[/#if] name:'scattered_quantity' ,align:'center', width:50, renderer: function(val,item,rowIndex, obj){
                    var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
                        var scattered_quantity=0;
                        if(obj==undefined){
                            scattered_quantity = val;
                        }
                        if(val != ""){
                            scattered_quantity = val;
                        }

                    if(type==1||type==2||type==3){
                        return '-'+'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].scatteredQuantity" value="0" />';
                    }
                    var html='<span class="scatteredQuantityStr">'+scattered_quantity+'</span>'+
                            '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].scatteredQuantity" class="scatteredQuantity text" value="'+scattered_quantity+'" />';
                    return html;
                }},
			    { title:'${message("宽 ")}',[#if sbu.id != 3]hidden:true,[/#if]  name:'length', align:'center', width:110, renderer:function(val,item,rowIndex,obj){
			        var sbu=${sbu.id}
			        var length=0;
               		 if(item.length!='' && item.length!=undefined){
                 		 length=item.length;
              		  }
               		 if(isNaN(length)){
                		 length=0;
             		   }
                	if(item.unit=='m2' && sbu==3 && item.orderSn =='' && item.isWare==0){
            		   var hideStr = '';
   						return '<div class="lh20"><div class="priceDiv nums-input ov" '+hideStr+'>'+
						'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editNumber (this.nextSibling,event)">'+
						'<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].length" kid="length" class="length t"  value="'+length+'" minData="0" oninput="editNumber (this,event)" onpropertychange="editNumber(this,event)"/>'+
						'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editNumber (this.previousSibling,event)">'+
						'</div></div>';
					 }else if(item.unit=='m2' && sbu==3 && item.orderSn ==''){
					    var hideStr = '';
   						return '<div class="lh20"><div class="priceDiv nums-input ov" '+hideStr+'>'+
						'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editNumber (this.nextSibling,event)">'+
						'<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].length" kid="length" class="length t"  value="'+length+'" minData="0" oninput="editNumber (this,event)" onpropertychange="editNumber(this,event)"/>'+
						'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editNumber (this.previousSibling,event)">'+
						'</div></div>';

					 }else if(item.unit=='m2' && sbu==3 && item.orderSn !=''){
				      return length+'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].length" class="length text" value="'+length+'" />';

				    }else{
				     return '-'+'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].length" class="length text" value="'+length+'" />';

				    }
				}},
				{ title:'${message("高")}',[#if sbu.id != 3]hidden:true,[/#if]  name:'width', align:'center', width:110, renderer:function(val,item,rowIndex,obj){
			  		 var sbu=${sbu.id}
			  		 var width=0;
			  		 if(item.width!='' && item.width!=undefined){
                  		width=item.width;
               		 }else{

               		 }
               		 if(isNaN(width)){
               			  width=0;
                	 }
                	 if(item.unit=='m2' && sbu==3 && item.orderSn =='' && item.isWare==0){
            		   var hideStr = '';
   						return '<div class="lh20"><div class="priceDiv nums-input ov" '+hideStr+'>'+
						'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editNumber (this.nextSibling,event)">'+
						'<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].width" kid="width" class="width t"  value="'+width+'" minData="'+width+'" oninput="editNumber (this,event)" onpropertychange="editNumber(this,event)"/>'+
						'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editNumber (this.previousSibling,event)">'+
						'</div></div>';
					 }else if(item.unit=='m2' && sbu==3 && item.orderSn ==''){
					    var hideStr = '';
   						return '<div class="lh20"><div class="priceDiv nums-input ov" '+hideStr+'>'+
						'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editNumber (this.nextSibling,event)">'+
						'<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].width" kid="width" class="width t"  value="'+width+'" minData="0" oninput="editNumber (this,event)" onpropertychange="editNumber(this,event)"/>'+
						'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editNumber (this.previousSibling,event)">'+
						'</div></div>';
					 }else if(item.unit=='m2' && sbu==3 && item.orderSn !=''){
            		 	return width+'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].width" class="width text" value="'+width+'" />';
					 }else{
				      	return '-'+'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].width" class="width text" value="'+width+'" />';

				    }
				}},
                { title:'${message("数量")}', name:'quantity', align:'center', width:100, renderer:function(val,item,rowIndex,obj){
                        var quantity='';
                        var width=0;
                	  	if(item.width!='' && item.width!=undefined && item.width!=0){
                  			width=item.width;
               			}
               			if(isNaN(width)){
               			  width=0;
                		}
                		var length=0;
              			if(item.length!='' && item.length!=undefined && item.length!=0){
                 			length=item.length;
              			}
               			if(isNaN(length)){
               			  length=0;
              			}
              			if(item.length!='' && item.width!='' && item.length!=0 && item.wdith!=0){
                            quantity = accMul(width,length);
                       	}else if(obj!=undefined){
                            quantity = val;
                        }
                        if(item.length!='' && item.width!='' && item.length>0 && item.width>0 && item.unit=='m2' && item.orderSn !=''){
				 			 return '<sapn class="text  quantityStr">'+quantity+'</span>'+'<input type="hidden" kid="quantity" class="t quantity"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].quantity" value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >';
						}else{
	                        var text = '<ul><li><div class="lh20">'+
	                                '<div class="nums-input ov">'+
	                                '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
	                                '<input type="text" kid="quantity" class="t quantity"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].quantity" value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
	                                '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
	                                '<input type="hidden" class="minPriceApplyQuantity" />'+
	                                '<input type="hidden" class="maxPriceApplyQuantity" />'+
	                                '</div></div></li><li><span class="attQuantity1Num">0</span></li></ul>';
	                        return text;
                        }
                }},
                { title:'${message("原价")}',[#if hiddenAmount==0]hidden:true,[/#if] name:'proPriceHeadPrice' ,align:'center',renderer: function(val,item,rowIndex,obj) {
                        var price = item.originalPrice;
                        if(obj == undefined && item.originalPrice!=''){
                            price = item.originalPrice;
                        }else if(item.originalPrice!=''){
                            price = item.originalPrice;
                        }

                        if(price==undefined){
                            price=0;
                        }
                        var hideStr = '';
						if (item.orderSn == "" || item.orderSn == null || item.orderSn == "undefined") {
							[#if editProductPrice==1]
							return '<div class="lh20"><div class="priceDiv nums-input ov" ' + hideStr + '>' +
									'<input type="text" name="b2bReturnsItems[' + b2bReturnsItemIndex + '].proPriceHeadPrice"  kid="proPriceHeadPrice" class="proPriceHeadPrice t" oninput="editAcount (this,event)" onpropertychange="editAcount(this,event)" value="' + price + '" />' +
									'</div></div>';
							[#else]
							return '<div class="lh20"><div class="priceDiv nums-input ov" ' + hideStr + '>' +
									'<input type="hidden" min=0 name="b2bReturnsItems[' + b2bReturnsItemIndex + '].proPriceHeadPrice" kid="proPriceHeadPrice" class="proPriceHeadPrice t" value="' + price + '"  oninput="editAcount (this,event)" onpropertychange="editAcount(this,event)" />' +
									'<span class="red proPriceHeadPrice">' + currency(price, true) + '</span>' +
									'</div></div>';
							[/#if]
						}else{
							return '<div class="lh20"><div class="priceDiv nums-input ov" ' + hideStr + '>' +
									'<input type="hidden" min=0 name="b2bReturnsItems[' + b2bReturnsItemIndex + '].proPriceHeadPrice" kid="proPriceHeadPrice" class="proPriceHeadPrice t" value="' + price + '"  oninput="editAcount (this,event)" onpropertychange="editAcount(this,event)" />' +
									'<span class="red proPriceHeadPrice">' + currency(price, true) + '</span>' +
									'</div></div>';
						}
                    }},
                {title:'价格表ID', align:'center',name:'',width:60, hidden:true, renderer: function(val,item,rowIndex){
                        var priceId = item.ppriceId == null?'':item.ppriceId;

                        return '<input   type="text" readonly="readonly" name="b2bReturnsItems['+b2bReturnsItemIndex+'].priceId" value="'+priceId+'" >'
                    }},
                {title:'原产品部结算价', align:'center',name:'',[#if isMember!=0  || editProductOrgPrice != 1]hidden:true,[/#if]renderer: function(val,item,rowIndex){
						var re_product_org_price=item.re_product_org_price;
                		if (!item.orderSn) {
							var re_product_org_price=item.product_org_price;
						}
                        if(!re_product_org_price){
                            re_product_org_price = 0;
                        }
                        return '<span class="text red reProductOrgPriceText">'+currency(re_product_org_price,true)+'</span><input type="hidden"  class="t text reProductOrgPrice"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].reProductOrgPrice" value="'+re_product_org_price+'" >';
                    }},
                {title:'产品部结算价', align:'center',name:'',[#if isMember!=0  || editProductOrgPrice != 1]hidden:true,[/#if]renderer: function(val,item,rowIndex){
						var product_org_price=item.product_org_price;
                        if(!product_org_price){
                            product_org_price = 0;
                        }
                        return '<span class="text red productOrgPriceText">'+currency(product_org_price,true)+'</span><input type="hidden"  class="t text productOrgPrice"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].productOrgPrice" value="'+product_org_price+'" >';
                    }},
                {title:'原平台结算价', align:'center',name:'', [#if isMember!=0 || seeSaleOrgPrice == 0]hidden:true,[/#if]renderer: function(val,item,rowIndex){
						var re_sale_org_price = item.re_sale_org_price;
                		if (!item.orderSn) {
							var re_sale_org_price = item.sale_org_price;
						}
						if(!re_sale_org_price){
							re_sale_org_price = 0;
						}
						[#if editSaleOrgPrice == 1]
                        return '<div class="nums-input ov">'+
                            '<input type="text"  class="t text reSaleOrgPrice"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].reSaleOrgPrice" value="'+re_sale_org_price+'" minData="0" oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >'+
                            '</div>';
                        [/#if]
                        return '<span class="text red reSaleOrgPriceText">'+currency(re_sale_org_price,true)+'</span><input type="hidden"  class="t text reSaleOrgPrice"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].reSaleOrgPrice" value="'+re_sale_org_price+'" >';
                    }},
                {title:'平台结算价', align:'center',name:'', [#if isMember!=0 || seeSaleOrgPrice == 0]hidden:true,[/#if]renderer: function(val,item,rowIndex){
						var sale_org_price=item.sale_org_price;
						if(!sale_org_price){
							sale_org_price = 0;
						}
						[#if editSaleOrgPrice == 1]
                        return '<div class="nums-input ov">'+
                            '<input type="text"  class="t text saleOrgPrice"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].saleOrgPrice" value="'+sale_org_price+'" minData="0" oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >'+
                            '</div>';
                        [/#if]
                        return '<span class="text red saleOrgPriceText">'+currency(sale_org_price,true)+'</span><input type="hidden"  class="t text saleOrgPrice"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].saleOrgPrice" value="'+sale_org_price+'" >';
                    }},
                { title:'${message("结算价价差")}', [#if seeOrgPriceDifference == 0]hidden:true,[/#if] name:'' ,align:'center', width:100,renderer:function(val,item,rowIndex, obj){
                        var price = item.org_price_difference;
						var org_price_difference = item.org_price_difference;
						if(!price) price=0;
						if(!org_price_difference) org_price_difference=0;
						if (item.orderSn == "" || item.orderSn == null || item.orderSn == "undefined") {
							[#if editPriceDifference == 1]
							return  '<div class="lh20"><div class="priceDiv nums-input ov">'+
									'<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].orgPriceDifference"  kid="orgPriceDifference" class="orgPriceDifference t" oninput="editAcount (this,event)" onpropertychange="editAcount(this,event)" value="'+price+'" />'+
									'</div></div>';
							[#else]
							return    '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].orgPriceDifference"  kid="orgPriceDifference" class="orgPriceDifference t" value="'+org_price_difference+'" />'+
									'<sapn class="text red orgPriceDifference">'+currency(org_price_difference,true)+'</span>'
							[/#if]
						}else {
							return    '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].orgPriceDifference"  kid="orgPriceDifference"  class="orgPriceDifference t" value="'+org_price_difference+'" />'+
									'<sapn class="text red orgPriceDifference">'+currency(org_price_difference,true)+'</span>'
						}

                    }},
				{ title:'${message("钢扣结算价差")}', [#if seeOrgPriceDifference == 0]hidden:true,[/#if] name:'gkjsjc' ,align:'center', width:100,renderer:function(val,item,rowIndex, obj){
						var gkjsjc = item.gkjsjc == '' ? 0 : item.gkjsjc;
						if (item.orderSn == "" || item.orderSn == null || item.orderSn == "undefined") {
							[#if editPriceDifference==1]
							return '<div class="lh20"><div class="priceDiv nums-input ov">' +
									'<input type="text" name="b2bReturnsItems[' + b2bReturnsItemIndex + '].gkjsjc"  kid="gkjsjc" class="gkjsjc t" oninput="editAcount (this,event)" onpropertychange="editAcount(this,event)" value="' + gkjsjc + '" />' +
									'</div></div>';
							[#else]
							return '<input type="hidden" name="b2bReturnsItems[' + b2bReturnsItemIndex + '].gkjsjc"  kid="gkjsjc" class="gkjsjc t" value="' + gkjsjc + '" />' +
									'<sapn class="text red gkjsjc">' + currency(gkjsjc, true) + '</span>'
							[/#if]
						}else {
								return    '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].gkjsjc"  kid="gkjsjc"  class="gkjsjc t" value="'+gkjsjc+'" />'+
										'<sapn class="text red gkjsjc">'+currency(gkjsjc,true)+'</span>'
							}

					}},

				{ title:'${message("折扣率%")}',[#if hiddenAmount==0]hidden:true,[/#if] name:'discount' ,align:'center',renderer: function(val,item,rowIndex, obj) {
					var discount=100;
					if (item.discount != '' && item.discount != undefined) {
						discount = item.discount;
					}
                	if (item.orderSn == "" || item.orderSn == null || item.orderSn == "undefined") {
							[#if editDiscount == 1]
							return '<div class="lh20"><div class="priceDiv nums-input ov">' +
									'<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].discount" kid="discount" class="discount t"  value="'+discount+'" oninput="editAcount (this,event)" onpropertychange="editAcount(this,event)"/>'+
									'</div></div>';
							[#else]
							return '<input type="hidden" name="b2bReturnsItems[' + b2bReturnsItemIndex + '].discount"  kid="discount"    class="text discount" oninput="editAcount (this,event)" onpropertychange="editAcount(this,event)" /><span class="red discount" >' + discount + '</span>';
							[/#if]
						}else{
							return '<input type="hidden" name="b2bReturnsItems[' + b2bReturnsItemIndex + '].discount"    kid="discount"  class="text discount"  oninput="editAcount (this,event)" onpropertychange="editAcount(this,event)" value="'+ discount +'"/><span class="red discount" >' + discount + '</span>';
						}
					}},
                { title:'${message("单价")}',[#if hiddenAmount==0]hidden:true,[/#if] align:'center',name:'member_price', renderer: function(val,item,rowIndex){
                    var sale_org_price=item.sale_org_price;
                    if(sale_org_price==null)sale_org_price='';
						[#if editProductPrice==1]
							if(item.orderSn == "" || item.orderSn == null || item.orderSn == "undefined"){
								return '<div class="nums-input ov">'+
									   // '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
										'<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].price" kid="price" class="price t" value="'+val+'" oninput="editAcount (this,event)" onpropertychange="editAcount(this,event)">' +
										//'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
														'</div>';
							}else{
								return '<span class="red price">'+currency(val,true)+'</span>'+'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].price" kid="price" class="price t" value="'+val+'" oninput="editAcount (this,event)" onpropertychange="editAcount(this,event)">';
							}
						[#else]
							return '<span class="red price">'+currency(val,true)+'</span>'+'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].price" kid="price" class="price t" value="'+val+'" oninput="editAcount (this,event)" onpropertychange="editAcount(this,event)">';
						[/#if]
                }},
				{ title:'${message("优惠价差")}', [#if seePriceDifference == 0]hidden:true,[/#if] name:'' ,align:'center', width:60,renderer:function(val,item,rowIndex, obj){
						var price_difference = item.price_difference;
						if(price_difference == null) price_difference = 0;
						if (item.orderSn == "" || item.orderSn == null || item.orderSn == "undefined") {
							[#if editPriceDifference == 1]
							return  '<div class=" nums-input ov">'+
									'<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].priceDifference"  kid="priceDifference" class="priceDifference t" oninput="editAcount (this,event)" onpropertychange="editAcount(this,event)" value="'+price_difference+'" />'+
									'</div>';
							[#else]
							return '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].priceDifference"  kid="priceDifference"  class="priceDifference t" value="'+price_difference+'" />'+
									'<sapn class="text red priceDifference">'+currency(price_difference,true)+'</span>'
							[/#if]
						}else {
							return    '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].priceDifference"  kid="priceDifference"  class="priceDifference t" value="'+price_difference+'" />'+
									'<sapn class="text red priceDifference">'+currency(price_difference,true)+'</span>'
						}

					}},
				{ title:'${message("优惠项目说明")}',align:'center', width:100, renderer: function(val,item,rowIndex, obj){
						item.discount_project_description_id = undefined;
						var str='selected="selected"';
						if (item.orderSn == "" || item.orderSn == null || item.orderSn == "undefined") {
							var html='<select name="b2bReturnsItems['+b2bReturnsItemIndex+'].discountProjectDescription.id" class="text discountProjectDescription">';
								html+='<option value="">请选择</option> ';
							[#list discountProjectDescriptionList as discountProjectDescription]
								if(${discountProjectDescription.id}==item.discount_project_description){
									html+='<option value="${discountProjectDescription.id}" '+str+' >${discountProjectDescription.value}</option> ';
								}else{
									html+='<option value="${discountProjectDescription.id}">${discountProjectDescription.value}</option> ';
								}
							[/#list]
							html+='</select>';
							return html;
						}else {
							if(item.discount_project_description != null){
								return    '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].discountProjectDescription.id"  kid="discountProjectDescription"  class="discountProjectDescription t" value="'+item.discount_project_description+'" />'+
										'<sapn class="text ">'+item.discount_project_description_value+'</span>'
							}
						}

					}},
				[#if seeOrderPriceDifference == 1 ]
				{ title:'${message("专项钢扣价差")}', name:'zxgk' ,align:'center', width:100,renderer:function(val,item,rowIndex, obj){
						var zxgk = item.zxgk == '' ? 0 : item.zxgk;
						if (item.orderSn == "" || item.orderSn == null || item.orderSn == "undefined") {
							[#if editOrderPriceDifference == 1 ]
							return  '<div class="lh20"><div class="priceDiv nums-input ov">'+
									'<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].zxgk"  kid="zxgk" class="zxgk t" oninput="editAcount (this,event)" onpropertychange="editAcount(this,event)" value="'+zxgk+'" />'+
									'</div></div>';
							[#else]
							return   '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].zxgk"  kid="zxgk" class="zxgk t" value="'+zxgk+'" />'+
									'<sapn class="text red zxgk">'+currency(zxgk,true)+'</span>'
							[/#if]
						}else {
							return    '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].zxgk"  kid="zxgk"  class="zxgk t" value="'+zxgk+'" />'+
									'<sapn class="text red zxgk">'+currency(zxgk,true)+'</span>'
						}
					}},
				{ title:'${message("专项平台方案价差")}',  name:'zxptjc' ,align:'center', width:100,renderer:function(val,item,rowIndex, obj){
						var zxptjc = item.zxptjc == '' ? 0 : item.zxptjc;
						if (item.orderSn == "" || item.orderSn == null || item.orderSn == "undefined") {
							[#if editOrderPriceDifference == 1 ]
							return  '<div class="lh20"><div class="priceDiv nums-input ov">'+
									'<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].zxptjc"  kid="zxptjc" class="zxptjc t" oninput="editAcount (this,event)" onpropertychange="editAcount(this,event)" value="'+zxptjc+'" />'+
									'</div></div>';
							[#else]
							return   '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].zxptjc"  kid="zxptjc" class="zxptjc t" value="'+zxptjc+'" />'+
									'<sapn class="text red zxptjc">'+currency(zxptjc,true)+'</span>'
							[/#if]
						}else {
							return    '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].zxptjc"  kid="zxptjc"  class="zxptjc t" value="'+zxptjc+'" />'+
									'<sapn class="text red zxptjc">'+currency(zxptjc,true)+'</span>'
						}
					}},
				{ title:'${message("其他专项")}',  name:'qtzx' ,align:'center', width:80,renderer:function(val,item,rowIndex, obj){
						var qtzx = item.qtzx == '' ? 0 : item.qtzx;
						if (item.orderSn == "" || item.orderSn == null || item.orderSn == "undefined") {
							[#if editOrderPriceDifference == 1 ]
							return  '<div class="lh20"><div class="priceDiv nums-input ov">'+
									'<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].qtzx"  kid="qtzx" class="qtzx t" oninput="editAcount (this,event)" onpropertychange="editAcount(this,event)" value="'+qtzx+'" />'+
									'</div></div>';
							[#else]
							return   '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].qtzx"  kid="qtzx" class="qtzx t" value="'+qtzx+'" />'+
									'<sapn class="text red qtzx">'+currency(qtzx,true)+'</span>'
							[/#if]
						}else {
							return    '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].qtzx"  kid="qtzx"  class="qtzx t" value="'+qtzx+'" />'+
									'<sapn class="text red qtzx">'+currency(qtzx,true)+'</span>'
						}
					}},
				[/#if]
    			{ title:'金额',[#if hiddenAmount==0]hidden:true,[/#if] align:'center',name:'',renderer: function(val,item,rowIndex){
                    return '<sapn class="text red trprice"></span>';
                }},
                [#if hiddenBatch !=0]
		            { title:'${message("色号")}',align:'center', width:80, renderer: function(val,item,rowIndex, obj){
		            	var str='selected="selected"';
		            	var html='<select name="b2bReturnsItems['+b2bReturnsItemIndex+'].colorNumbers.id" class="text colourNumber">';
		            		html+='<option value="">请选择</option> ';
		            		[#list colorNumberList as colorNumber]
		            			if(${colorNumber.id}==item.colour_number_id){
		            				html+='<option value="${colorNumber.id}" '+str+' >${colorNumber.value}</option> ';
		            			}else{
		            				html+='<option value="${colorNumber.id}">${colorNumber.value}</option> ';
		            			}
		            		[/#list]
		            		html+='</select>';
		            	return html;
		            }},
		            { title:'${message("含水率")}',align:'center', width:80, renderer: function(val,item,rowIndex, obj){
		            	var str='selected="selected"';
		            	var html='<select name="b2bReturnsItems['+b2bReturnsItemIndex+'].moistureContents.id" class="text moistureContent">';
		            		html+='<option value="">请选择</option> ';
		            		[#list moistureContentList as  moistureContent]
		            			if(${moistureContent.id}==item.moisture_content_id){
		            				html+='<option value="${moistureContent.id}" '+str+' >${moistureContent.value}</option> ';
		            			}else{
		            				html+='<option value="${moistureContent.id}">${moistureContent.value}</option> ';
		            			}
		            		[/#list]
		            		html+='</select>';
		            	return html;
		            }},
		            { title:'批次', align:'center', width:200,name:'batch',renderer: function(val,item,rowIndex, obj){
		            	var batch = '';
						if(isNull(item.batch) != null){
							batch = item.batch;
						}
						var batch_encoding = '';
						if(isNull(item.batch_encoding) != null){
							batch_encoding = item.batch_encoding;
						}
			        	 var html = '<span class="search" style="position:relative">'
			        			 	+'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].batch" class="text batchIds" value="'+batch+'" />'
				        		    +'<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].batchEncoding" class="text batchEncodings" value="'+batch_encoding+'" readonly/>'
									+'<input type="button" class="iconSearch" value="" id="selectBatch"/>'
							+'</span>';
						 return html;
			         }},
			    [/#if]
		    { title:'备注', align:'center',name:'', width:100,renderer: function(val,item,rowIndex){
                   return '<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].memo" class="text colourNumber" value="" id="memo"/>'
            }},
			{ title:'${message("木种花色")}', align:'center',name:'wood_type_or_color',renderer: function(val,item,rowIndex){
                return val+'<input type="hidden" class="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].woodTypeOrColor" value="'+val+'">';
            }},
			{ title:'${message("产品分类")}', name:'product_category_name' ,align:'center',width:80, renderer: function(val,item,rowIndex, obj){
                return val;
            }},
			{ title:'${message("单位")}', align:'center',name:'unit'},
            { title:'产品型号', align:'center',name:'model', width:80},
            { title:'${message("产品规格")}', name:'spec' ,align:'center', hidden:'true',width:80},
            { title:'工厂/供应商',[#if sbu.id !=3||isMember==1 ]hidden:true,[/#if] align:'center',name:'manufactory_name', width:250},
            { title:'供应商型号',[#if sbu.id !=3||isMember==1 ]hidden:true,[/#if] align:'center',name:'supplier', width:100},
			{ title:'参考订单号', align:'center',name:'orderSn',width:100,renderer: function(val,item,rowIndex){
                   return val+'<input type="hidden" class="text orderSn" name="b2bReturnsItems['+b2bReturnsItemIndex+'].sn" value="'+val+'">';
            }},
            { title:'参考发货单号', align:'center',name:'shippingSn',width:100,renderer: function(val,item,rowIndex){
                     return val+'<input type="hidden" class="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].shippingSn" value="'+val+'">';
            }},
            { title:'参考erp号', align:'center',name:'erp_sn',width:100,renderer: function(val,item,rowIndex){
                     return val+'<input type="hidden" class="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].erpSn" value="'+val+'">';
            }},
            { title:'发货仓', align:'center',name:'warehouse_name',width:100,renderer: function(val,item,rowIndex){
            	 var html = val+'<input type="hidden" class="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].warehouseName" value="'+val+'">';
            	 b2bReturnsItemIndex++;
                 line_no++;
                 return html;
            }},
          ];

            $mmGrid = $('#table-m1').mmGrid({
                height:'auto',
                cols: cols,
                checkCol:false,
                url:'/aftersales/b2b_returns/select_shipping_item_data.jhtml',
                fullWidthRows: true,
                autoLoad: true,
                callback:function(){
                    countTotal();
                    editNumber();
                    productOrganization();
                }
            });

            var m2_cols = [
 				  { title:'经营组织' ,align:'center',renderer: function(val,item,rowIndex, obj){
 					  var html='<span class="organizationNames">'+item.organization_name+'</span>'+
 				      '<input type="hidden" class="organizationIds text" value="'+item.organization_id+'" />';
    				  return html;
                  }},
                  { title:'余额', align:'center',renderer: function(val,item,rowIndex, obj){
                          var amountStr = '';
                          if(isNull(item.balance) == null){
                              amountStr = currency(0,true);
                          }else{
                        	  amountStr = currency(item.balance,true);
                          }
                          return '<sapn class="red text">'+amountStr+'</span>';
                   }},
                   { title:'可用余额', align:'center',renderer: function(val,item,rowIndex, obj){
                       var amountStr = '';
                       if(isNull(item.available_balance) == null){
                           amountStr = currency(0,true);
                       }else{
                    	   amountStr = currency(item.available_balance,true);
                       }
                       return '<sapn class="availableBalance red text">'+amountStr+'</span>';
                   }},
                   { title:'退货金额', align:'center',renderer: function(val,item,rowIndex, obj){
                           return '<sapn class="returnBalance red text">'+currency(0,true)+'</span>';
                   }},
                   { title:'授信', align:'center',renderer: function(val,item,rowIndex, obj){
                   		   var amountStr = '';
                           if(isNull(item.credit_balance) == null){
                               amountStr = currency(0,true);
                           }else{
                           	   amountStr = currency(item.credit_balance,true);
                           }
                           return '<sapn class="red text">'+amountStr+'</span>';
                   }}
                ];

                $m2_mmGrid = $('#table-m2').mmGrid({
    	        		height:'auto',
    	        		cols: m2_cols,
    	        		fullWidthRows:true,
    	        		checkCol: false,
    	        		autoLoad: true,
                });

            $addProduct.click(function(){
                //打开选择产品界面
                var storeId = $(".storeId").val();
                if (isNull(storeId) == null) {
    				$.message_alert("请选客户");
    				return false;
    			}
                var warehouseId = $(".warehouseId").val();
    			if (isNull(warehouseId) == null) {
    				$.message_alert("请选仓库");
    				return false;
    			}
    			var typeSystemDictId = $(".typeSystemDictId").val();
    			if (isEmpty(typeSystemDictId)) {
    				$.message_alert("仓库类型不能为空");
    				return false;
    			}
    			var params='&typeSystemDictId='+typeSystemDictId;
    			var typesystemDictFlag = $("input.typesystemDictFlag").val();
    			if(isNull(typesystemDictFlag) != null && typesystemDictFlag == 0){
    				var organizationId = $(".organizationId").val();
	        	  	if (isNull(organizationId) == null) {
	        	  		$.message_alert('经营组织不能为空');
		      			return false;
	    	  		}
	        	  	params+='&organizationId='+organizationId;
    			}
                var sbuId = $(".sbuId").val();
                var memberRankId = $(".memberRankId").val();
                if(isNull(memberRankId) == null){
                    memberRankId = "";
                }
                $addProduct.bindQueryBtn({
                    type:'product',
                    bindClick:false,
                    title:'查询产品',
                    url:'/product/product/selectProduct_returns.jhtml?multi=2&storeId='+storeId+'&sbuId='+sbuId+'&isToOrder=true'+'&memberRankId='+memberRankId+params,
                    callback:function(rows){
                        if(rows.length>0){
                            var error = '';
                            for (var i = 0; i < rows.length;i++) {
                                var row = rows[i];
                                $mmGrid.addRow(row,null,1);
                                countTotal();
                            }
                            productOrganization();
                        }

                    }
                });
            });

      	  	$addSaleOrgs.click(function(){
    		  $addSaleOrgs.bindQueryBtn({
    		      type:'saleOrg',
    		      bindClick:false,
    		      title:'${message("查询工厂")}',
    		      url:'/aftersales/aftersale/select_factory.jhtml',
    		      callback:function(rows){
    		          if(rows.length>0){
    		          	var id = rows[0].id;
    		          	var name = rows[0].name;
    		          	var username = rows[0].sale_org;
    		          	$(".factoryId").val(id);
    		          	$(".factoryName").val(name);
    		          }
    		      }
    			});
    		});

      	  	//发货明细页面弹出
            $findShipping.click(function(){
                var storeId = $(".storeId").val();
                if (isNull(storeId) == null) {
     				$.message_alert("请选客户");
     				return false;
     			}
                var warehouseId = $(".warehouseId").val();
     			if (isNull(warehouseId) == null) {
     				$.message_alert("请选仓库");
     				return false;
     			}
     			var params = '';
    			var typesystemDictFlag = $("input.typesystemDictFlag").val();
    			if(isNull(typesystemDictFlag) != null && typesystemDictFlag == 0){
    				var organizationId = $(".organizationId").val();
	        	  	if (isNull(organizationId) == null) {
	        	  		$.message_alert('经营组织不能为空');
		      			return false;
	    	  		}
	        	  	params+='&productOrganizationId='+organizationId;
    			}
                var sbuId = $(".sbuId").val();
                $findShipping.bindQueryBtn({
                    type:'shippingItem',
                    bindClick:false,
                    title:'参考发货单',
                    url:'/aftersales/b2b_returns/selectShipping.jhtml?multi=2&storeId='+storeId+'&sbuId='+sbuId+params,
                    callback:function(rows){
                        if(rows.length>0){
                            var error = '';
                            for (var i = 0; i < rows.length;i++) {
                                var row = rows[i];
                                $mmGrid.addRow(row,null,1);
                                newCountTotal();
                            }
                            productOrganization();
                        }
                    }
                });
            });

            //订单明细页面弹出
            $findOrder.click(function(){
                var storeId = $(".storeId").val();
                if (isNull(storeId) == null) {
     				$.message_alert("请选客户");
     				return false;
     			}
                var warehouseId = $(".warehouseId").val();
     			if (isNull(warehouseId) == null) {
     				$.message_alert("请选仓库");
     				return false;
     			}
     			var params = '';
    			var typesystemDictFlag = $("input.typesystemDictFlag").val();
    			if(isNull(typesystemDictFlag) != null && typesystemDictFlag == 0){
    				var organizationId = $(".organizationId").val();
	        	  	if (isNull(organizationId) == null) {
	        	  		$.message_alert('经营组织不能为空');
		      			return false;
	    	  		}
	        	  	params+='&productOrganizationId='+organizationId;
    			}
                var sbuId = $(".sbuId").val();
                var contractId = $(".contractId").val();
                $findOrder.bindQueryBtn({
                    type:'orderItem',
                    bindClick:false,
                    title:'参考订单',
                    url:'/aftersales/b2b_returns/selectOrder.jhtml?multi=2&storeId='+storeId+'&sbuId='+sbuId+'&contractId='+contractId+params,
                    callback:function(rows){
                        if(rows.length>0){
                            var error = '';
                            for (var i = 0; i < rows.length;i++) {
                                var idH = $(".productId_"+rows[i].id).length;
                                if(idH > 0){
                                    $.message_alert('产品【'+rows[i].name+'】已添加');
                                    return false;
                                }
                            }
                            for (var i = 0; i < rows.length;i++) {
                                var row = rows[i];
                                $mmGrid.addRow(row,null,1);
								countTotal();
                            }
                            productOrganization();
                        }
                    }
                });
            });



            // 删除商品
            $("a.deleteProduct").live("click", function() {
                var $this = $(this);
                $.message_confirm('${message("您确定要删除吗？")}',function(){
                    var tr = $this.closest("tr");
                    var le = $("#addProductTable").find("tr").length;
                    if(le == 2){
                        $.message_alert('${message("至少保留一个明细")}');
                    }else{
                        tr.remove();
                        countTotal();
                        calculationDifference();
                        var line_number = 1;
                        $("span.line_no").each(function(){
                            $(this).html(line_number++);
                        });
                        line_no--;
                    }
                });
            });

            $("input[name='image1']").single_upload({
                uploadSize:"source"
            });

            $("input[name='image2']").single_upload({
                uploadSize:"source"
            });

            $("input[name='image3']").single_upload({
                uploadSize:"source"
            });

            $("input.NonNegative").live("change",function(){
                if($(this).val()=="" || $(this).val()<0){
                    $(this).val(0);
                }
            });

            /**初始化订单附件*/
            var depositAttachIndex=0;
            var cols = [
                { title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex){
                        var url = item.url;
                        var fileObj = getfileObj(item.name);
                        /**设置隐藏值*/
                        var hideValues = {};
                        hideValues['b2bReturnsAttachs['+depositAttachIndex+'].url']=url;
                        hideValues['b2bReturnsAttachs['+depositAttachIndex+'].suffix']=fileObj.suffix;

                        return createFileStr({
                            url : url,
                            fileName : fileObj.file_name,
                            name : fileObj.name,
                            suffix : fileObj.suffix,
                            time : '',
                            textName:'b2bReturnsAttachs['+depositAttachIndex+'].name',
                            hideValues:hideValues
                        });
                    }},
                { title:'${message("备注")}', name:'memo' ,width:590 ,align:'center', renderer: function(val,item,rowIndex){
                        return '<div><textarea class="text" name="b2bReturnsAttachs['+depositAttachIndex+'].memo" >'+val+'</textarea></div>';
                    }},
                { title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
                        depositAttachIndex++;
                        return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
                    }}
            ];
            var $amGrid=$('#table-attach').mmGrid({
                fullWidthRows:true,
                height:'auto',
                cols: cols,
                checkCol: false,
                autoLoad: true
            });


            var $addAttach = $("#addAttach");
            var attachIdnex = 0;
            var option1 = {
                dataType: "json",
                uploadToFileServer:true,
                uploadSize: "fileurl",
                callback : function(data){
                    var date = new Date();
                    var year = date.getFullYear();
                    var month = date.getMonth()+1;
                    var day = date.getDate();
                    var time = year+'-'+month+'-'+day;
                    for(var i=0;i<data.length;i++){
                        var row = data[i].file_info;
                        $amGrid.addRow(row,null,1);
                    }

                }
            }
            $addAttach.file_upload(option1);

            var $deleteAttachment = $(".deleteAttachment");
            $deleteAttachment.live("click", function() {
                var $this = $(this);
                $this.closest("tr").remove();
            });

            $(".orderSn").change(function(){
                if($("#orderId").val()!=''){
                    $("#addProduct").hide();
                }else{
                    $("#addProduct").show();
                }
            });

            // 表单验证
            $inputForm.validate({
                rules: {
                    amount: "required",
                    storeId: "required",
                    category: "required"
                },
                submitHandler:function(form){
                    return false;
                }

            });

            //查询客户
             $("#selectStore").click(function(){
                 var sbuId = $(".sbuId").val();
                 if(isNull(sbuId) == null){
                 	$.message_alert('sbu不能为空');
                     return false;
                 }
                 $("#selectStore").bindQueryBtn({
                     type:'store',
                     bindClick:false,
                     title:'${message("查询客户")}',
                     url:'/member/store/select_store.jhtml?type=distributor&isMember=1'+'&sbuId='+sbuId,
                     callback:function(rows){
                         if(rows.length>0){
                         	var row = rows[0];
                         	if(row.id != $("input[name='storeId']").attr("value")){
                         		//可用余额
                                 $("#bal").text(currency(0,true));
                                 $("#storeBalance").val(0);
                                 //授信
                                 $("#credit").text(currency(0,true));
                                 //差额
                                 $("#chae").text(currency(0,true));
                                 //余额
                                 $("#yue").text(currency(0,true));
                                 //仓库
                         		$(".warehouseId").val('');
                                 $(".warehouseName").val('');
                                 $mmGrid.removeRow();
                                 b2bReturnsItemIndex = 0;
                             }
                             $("input.storeId").val(row.id);
                             $("input.storeName").val(row.name);
                             //清空售后和工厂
							 $('.aftersaleId').val('');
							 $('.fourAftersaleSn').val('');
							 $("input[name='factoryId']").val('');
							 $("input[name='factoryName']").val('');

                             $("#storeAlias").text(row.alias);
                             $(".saleOrgName").val(row.sale_org_name);
                             $(".saleOrgId").val(row.sale_org_id);
                             if($("input.orderId").val()!=''){
                                 $("input[name='orderId']").val('');
                                 $("input[name='orderSn']").val('');
                                 $("input[name='name']").val('');
                                 $("input[name='mobile']").val('');
                             }
                             $("input[name='storeName']").attr("value",row.name);
                             $("input[name='consignee']").attr("value",row.store_consignee);
                             $("input[name='consigneeMobile']").attr("value",row.store_mobile);
                             $("input[name='address']").attr("value",row.store_address);
                             $("input[name='zipCode']").attr("value",row.store_zip_code);
     						$("input[name='regionalManagerId']").attr("value",row.store_member_id);
                             $("#regionalManagerName").text(row.store_member_name);
                             if(row.sale_org_region==1){
     		        			$(".region").text("南区");
     	        			}else if(row.sale_org_region==0){
     	        				$(".region").text("北区");
     	        			}else{
     	        				$(".region").text("");
     	        			}
                             ajaxSubmit($(rows[0].id),{
                                 method:'POST',
                                 url:'/member/store/select_store_address_data.jhtml',
                                 data:{storeId:rows[0].id,address:row.store_address},
                                 callback:function(resultMsg) {
                                     var data = $.parseJSON(resultMsg.content);//前端
                                     for(var i=0;i<data.content.length;i++){
                                         var storeAddress = data.content[i];
                                         $("input[name='addressOutTradeNo']").attr("value",storeAddress.out_trade_no);
                                     }
                                 }
                             })
                             $("#businessTypeId option").each(function(){ //遍历全部option
                                 if($(this).text() == row.business_type_value){
                                     $(this).attr("selected",true);
                                 }
                             });
                             var areaId = row.store_area;
                             if(areaId==null)areaId='';
                             var areaName = row.area_full_name;
                             if(areaName==null)areaName='';
                             var treePath = row.area_tree_path;
                             if(treePath==null)treePath='';
                             $("input[name='areaId']").val(areaId);
                             $("input[name='areaName']").val(areaName);
                             // 查询客户与SBU的价格类型
                             ajaxSubmit($(rows[0].id),{
                                 method:'POST',
                                 url:'/basic/member_rank/select_store_sbu_data.jhtml',
                                 data:{storeId:rows[0].id},
                                 callback:function(resultMsg) {
                                     var ymsbu = $('#sbuName').text();
                                     var data = $.parseJSON(resultMsg.content);
                                     if (data.length > 0){
                                         for(var i = 0; i < data.length; i++){
                                             // 根据客户的SBU来判断当前页面的SBU
                                             if(data[i].sbu_name == $.trim(ymsbu)){
                                                 $("#memberRank1").show();
                                                 $("#memberRank2").show();
                                                 $(".memberRankId").val(data[i].memberRankId);
                                                 $(".memberRankName").val(data[i].memberRank);
                                                 break;
                                             } else {
                                                 $(".memberRankId").val("");
                                                 $(".memberRankName").val("");
                                             }
                                         }
                                     } else {
                                         $(".memberRankId").val("");
                                         $(".memberRankName").val("");
                                         $("#memberRank1").hide();
                                         $("#memberRank2").hide();
                                     }
                                 }
                             });
                         }else {
                             $("input.storeId").val("");
                             $("input.storeName").val("");
                         }
                     }
                 });
             })

		    //查询合同
			$("#selectContract").click(function(){
		    	var sbuId = $(".sbuId").val();
		    	var storeId = $(".storeId").val();
		    	$("#openStore").bindQueryBtn({
			        type:'store',
			        bindClick:false,
			        title:'${message("查询合同")}',
			        url:'/b2b/customerContract/select_contract.jhtml?sbuId='+sbuId+'&storeId='+storeId,
			        callback:function(rows){
			            if(rows.length>0){
			                var row = rows[0];
			                $("input[name='contractId']").attr("value",row.id);
			                $("input[name='contractName']").attr("value",row.contract_name);
			                $("input[name='storeId']").attr("value",row.store_id);
			                $("input[name='storeName']").attr("value",row.store_name);
			                $("input[name='saleOrgId']").attr("value",row.sale_org_id);
		                    $("#saleOrgName").text(row.sale_org_name);
			                $("input[name='storeMemberId']").attr("value",row.store_member_id);
			                $("input[name='storeMemberName']").attr("value",row.store_member_name);
			                $("input[name='contacts']").attr("value",row.contacts);
			                $("input[name='phone']").attr("value",row.phone);
			                $("input[name='address']").attr("value",row.address);
			            }
			        }
			    });
		    })

            //查询仓库
            $("#selectWarehouse").click(function(){
                var saleOrgId = $(".saleOrgId").val();
                if(isNull(saleOrgId) == null){
                	$.message_alert('机构不能为空');
                	return false;
                }
                var sbuId = $(".sbuId").val();
                if(isNull(sbuId) == null){
                	$.message_alert('sbu不能为空');
                	return false;
                }
                var storeId = $("input[name='storeId']").val();
                if(isNull(storeId) == null){
                	$.message_alert('请选择客户');
                	return false;
                }
                $("#selectWarehouse").bindQueryBtn({
                    type:'warehouse',
                    bindClick:false,
                    title:'${message("查询仓库")}',
                    url:'/stock/warehouse/select_warehouse.jhtml?saleOrgId='+saleOrgId+'&sbuId='+sbuId,
                    callback:function(rows){
                        if(rows.length>0){
                            var row = rows[0];
                            if(row.id != $("input[name='warehouseId']").attr("value")){
                            	$m2_mmGrid.removeRow();
                            	$(".warehouseId").val(row.id);
                                $(".warehouseName").val(row.name);
                                $(".typeSystemDictId").val(row.type_system_dict_id);
                                $(".typeSystemDictValue").html(row.type_system_dict_value);
                                $(".typesystemDictFlag").val(row.type_system_dict_flag);
                                $(".productionPlantId").val(row.production_plant);
                                $("input[name='organizationId']").val(row.management_organization_id);
                                $("#organizationId").text(row.management_organization_name);
                                organizationBalance(storeId,sbuId);
                                ajaxSubmit($(row.id),{
                                    method:'post',
                                    url:'/finance/balance/get_balance.jhtml',
                                    data:{storeId:storeId,sbuId:sbuId,saleOrgId:saleOrgId,organizationId:row.management_organization_id},
                                    callback:function(resultMsg) {
                                        var data = resultMsg.objx;
                                        var balance = 0;
                                        var creditAmount = 0;
                                        if(isNull(data) != null){
                                        	balance = data.balance;
                                        	creditAmount = 	data.credit_amount;
                                        }
                                      	//可用余额
                                        $("#bal").text(currency(balance,true));
                                        $("#storeBalance").val(balance);
                                      	//授信
                                        $("#credit").text(currency(creditAmount,true));
                                        //余额
                                        $("#yue").text(currency(balance-creditAmount,true));
                                    }
                                })
                                $mmGrid.removeRow();
                                countTotal();
                            }
                        }
                    }
                });
            })

            //打开选择地址界面
            $("#addReceiveAddress").click(function(){
                var storeId = $(".storeId").val();
                if(storeId==""){
                    $.message_alert('请选择客户');
                }else{
                    select_store_address(storeId);
                }
            });

            //查询销售订单
            $("#selectOrder").bindQueryBtn({
                type:'order',
                title:'查询销售订单',
                url:'/b2b/order/select_order.jhtml?orderStatus=6&shippingStatus=1&shippingStatus=2&isReturn=1',
                callback:function(rows){
                    if(rows.length>0){
                        var row = rows[0];
                        $("input.orderId").val(row.id);
                        $("input.orderSn").val(row.sn);
                        $("input[name='storeId']").val(row.stores);
                        $("input.storeName").val(row.store_name);
                        $("input[name='name']").val(row.consignee);
                        $("input[name='mobile']").val(row.mobile);
                        $("input[name='saleOrgId']").val(row.sale_org_id);
                        $("input[name='consignee']").val(row.consignee);
                        $("input[name='consigneeMobile']").val(row.phone);
                        $("input[name='zipCode']").val(row.zip_code);
                        $("input[name='areaId']").val(row.area);
                        $("input[name='areaName']").val(row.area_name);
                        $("input[name='address']").val(row.address);
                        $("span.saleOrgName").html(row.sale_org_name);
                        $("#addProduct").hide();
                        $mmGrid.load({orderId:row.id});
                    }
                }
            });

            //查询机构
            $("#selectSaleOrg").bindQueryBtn({
                type:'saleOrg',
                title:'${message("查询机构")}',
                url:'/basic/saleOrg/select_saleOrg.jhtml',
                callback:function(rows){
                    if(rows.length>0){
                        var row = rows[0];
                        $("input.saleOrgId").val(row.id);
                        $("input.saleOrgName").val(row.name);
                        if($("input.orderId").val()!=''){
                            $mmGrid.removeRow();
                            $("input[name='orderId']").val('');
                            $("input[name='orderSn']").val('');
                            $("input[name='name']").val('');
                            $("input[name='mobile']").val('');
                            b2bReturnsItemIndex = 0;
                        }
                    }
                }
            });

            //打开选择业务员界面
            $("#selectsaleman").bindQueryBtn({
                type:'saleman',
                title:'${message("查询认领人")}',
                url:'/member/store_member/select_saleman.jhtml'
            });

            $("#openArea").bindQueryBtn({
                type:'area',
                title:'${message("查询地区")}',
                url:'/basic/area/select_area.jhtml',
                callback:function(rows){
                    if(rows.length>0){
                        var $tr =$this.closest("tr");
                        $(".areaId").val(rows[0].id);
                        $(".areaName").val(rows[0].full_name);
                        $("input[name='zipCode']").val('');
                    }
                }
            });

            $("form").bindAttribute({
                isConfirm:true,
                callback: function(resultMsg){
                    $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                        location.href= '/aftersales/b2b_returns/view/${code}.jhtml?flag=${flag}&id='+resultMsg.objx;
                    });
                }
            });

            $("#selectMemberRank").click(function(){
                var saleOrgId = $(".saleOrgId").val();
                var sbuId = $(".sbuId").val();
                //查询价格等级
                $("#selectMemberRank").bindQueryBtn({
                    type:'memberRank',
                    bindClick:false,
                    title:'${message("查询价格等级")}',
                    url:'/basic/member_rank/select_memberRank_price.jhtml?saleOrgId='+ saleOrgId + '&sbuId=' + sbuId,
                    callback:function(rows){
                        if(rows.length>0){
                            var row = rows[0];
                            $(".memberRankId").val(row.id);
                            $(".memberRankName").val(row.name);
                        }
                    }
                });
            });

            //查询售后单据
            $("#selectAftersale").click(function(){
            	var category = $('.category').val();
            	if(category=='0'||category==''||category==undefined){
            		$.message_alert('退货种类必须是售后退货才能选择售后单据！');
            		return false;
            	}
				var storeId = $(".storeId").val();
	            $(this).bindQueryBtn({
	                type:'aftersale',
	                bindClick:false,
	                title:'${message("查询售后单据")}',
	                url:'/aftersales/aftersale/select_aftersale.jhtml?multi=1&storeId='+storeId,
	                callback:function(rows){
	                    if(rows.length>0){
	                        var row = rows[0];
							console.log(row)
	                        if(row.returnStatus == null||row.returnStatus != "0"){
	                        	$.message_alert('售后单据退货状态不是未退货状态请重新选择！');
	                        }else{
		                        $('.aftersaleId').val(row.id);
		                        $('.fourAftersaleSn').val(row.sn);
		                        $("input[name='factoryId']").val(row.factoryId);
				                $("input[name='factoryName']").val(row.factoryName);
				               /* $("input[name='storeId']").val(row.store_id);
				                $('.storeName').val(row.storeName);
								$(".saleOrgId").val(row.sale_org_id);
								$(".saleOrgName").val(row.sale_org_name);*/
				                //清空产品
								//$mmGrid.removeRow();
	                        }
	                	}else {
							$('.aftersaleId').val('');
							$('.fourAftersaleSn').val('');
							$("input[name='factoryId']").val('');
							$("input[name='factoryName']").val('');
						}
	                }
	            });
            });

             //产品经营组织
	   		 $(".productOrganization").live("change", function() {
	   			 var organizationId = $(this).val();
		   		 var $tr = $(this).closest("tr");
		   		 if(isNull(organizationId) != null){
		   			 calculationDifference();
		   		 }
		   		 //批次
				 $tr.find(".batchIds").val('');
 				 $tr.find(".batchEncodings").val('');
		   		 [#if linkStock == 1 ]
		   		 	loadAttQuantity($tr);
		   		 [/#if]
	   		 })
            [#if linkStock == 1 ]
	    		 //色号
	    		 $(".colourNumber").live("change", function() {
	    			 var $tr = $(this).closest("tr");
					 loadAttQuantity($tr);
	    		 })
	    		 //含水率
	    		 $(".moistureContent").live("change", function() {
	    			 var $tr = $(this).closest("tr");
					 loadAttQuantity($tr);
	    		 })
	    	[/#if]
	    	//客户余额
            $("#storeSurplus").css('display', 'none');
            $("#storeSurplus").next().css('display', 'none');
            //经营组织余额
            [#if hiddenAmount==0]
	            $("#organizationSurplus").css('display', 'none');
	            $("#organizationSurplus").next().css('display', 'none');
            [/#if]


        	//批次
            $("#selectBatch").live("click",function(){
                var $this = $(this);
                var $tr = $this.closest("tr");
                var url = '/stock/batch/edit_post.jhtml';
            	//经营组织
                var organizationId = $tr.find(".productOrganization").val();
                if(isNull(organizationId) == null){
        			$.message_alert("单据行项的经营组织不能为空");
        			return false;
        	  	}
                url+='?organizationId='+organizationId;
               	//仓库工厂
                var productionPlantId = $("input.productionPlantId").val();
               	if(isNull(productionPlantId) != null){
               		url+='&productionPlantId='+productionPlantId;
               	}
                //批次
                var batchIds = $tr.find(".batchIds").val();
                if(isNull(batchIds) != null){
                	url+='&bacthIds='+batchIds;
                }
        		var iframeId = "iframeId" + (new Date()).valueOf() + Math.floor(Math.random() * 1000000);
        		var $dialog = $.dialog({
        			title:'查询批次',
       				width:1200,
       				height:508,
       				content: "<iframe  id='"+iframeId+"' src='"+url+"' width='100%'  height='"+420+"px'><\/iframe>",
       				onOk: function() {
       					var rows = $("#"+iframeId)[0].contentWindow.childMethod();
       					var allId = '';
       					var allName = '';
       					for(var i=0;i<rows.length;i++){
       						var row = rows[i];
       						if(isNull(row) != null){
       							if(isNull(row.id) != null){
       								if(isNull(allId) != null){
           								allId =allId +';'+ row.id;
           								allName = allName +';'+ row.batch_encoding;
           							}else{
           								allId = row.id;
           								allName = row.batch_encoding;
           							}
       							}
       						}
       					}
       					$tr.find(".batchIds").val(allId);
       					$tr.find(".batchEncodings").val(allName);
       					[#if linkStock == 1 ]
					 		loadAttQuantity($tr);
					 	[/#if]
       				}
       			});
            });

        });



        function product_view(id){
            var storeId = $('.storeId').val();
            create_iframe('/product/product/content.jhtml?id='+id+'&storeId='+storeId);
        }

        function orderclearSelect(e){
            if($(e).val()==""){
                $("#orderId").val('');
                $("#addProduct").show();
            }else{
                $("#addProduct").hide();
            }
        }

		//清空售后单据和工厂
		function cleanAfterSale(e) {
			var category = $(e).val();
			if (category==0){
				$('.aftersaleId').val('');
				$('.fourAftersaleSn').val('');
				$("input[name='factoryId']").val('');
				$("input[name='factoryName']").val('');
			}
		}

        function save(e){
            var $input = $("input.price");
            var str='您确定要保存吗？';
            var count = 0;
            var err=0;
            var saleOrgName = $("input[name='saleOrgName']").val();
            var organizationId =  $("input[name='organizationId']").val();
            var warehouseName = $("input[name='warehouseName']").val();
            $input.each(function(){
                var p = $(this).val();
                var g = $(this).closest("tr").find(".isGift").val();
                var $tr=$(this).closest("tr");
                var quantity=$tr.find(".quantity").val();
                var length = $tr.find(".length").val();
                var width = $tr.find(".width").val();
                var branchQuantity=$tr.find(".branchQuantity").val();
                var boxQuantity = $tr.find(".boxQuantity").val();
                if(branchQuantity%1!=0&&branchQuantity!=undefined){
                    str='支数不允许是小数，必须为整数';
                    err++;
                    return false;
                }
                console.log("boxQuantity : "+boxQuantity);
                if(boxQuantity%1!=0 && boxQuantity!=undefined && !isNaN(boxQuantity)){
                    str='箱数不允许是小数，必须为整数';
                    err++;
                    return false;
                }
                if(p == 0 && g == "false"){
                    count++;
                }
            })
            if(err!=0){
                $.message_alert(str);
                return false;
            }
            if(saleOrgName==""){
                str ='机构为空，请检查'
                $.message_alert(str);
                return false;
            }
            var url = '/aftersales/b2b_returns/save.jhtml';
            var $form = $("#inputForm");
            if($form.valid()){
                $.message_confirm(str,function() {
                    Mask();
                    ajaxSubmit(e,{
                        url: url,
                        data:$("#inputForm").serialize(),
                        method: "post",
						failCallback:function(resultMsg){
							// 访问地址失败，或发生异常没有正常返回
							messageAlert(resultMsg);
							UnMask();
						},
                        callback:function(resultMsg){
                            var orderId=resultMsg.objx;
                            if(resultMsg.type!="success"){
                                $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                                    location.reload(true);
                                })
                                return false;
                            }
                           // $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                                location.href= '/aftersales/b2b_returns/view/${code}.jhtml?id='+resultMsg.objx;
                           // })
                        }
                    });
                })
            }
        }

        //更换地址
        function select_store_address(storeId){
            $("#addReceiveAddress").bindQueryBtn({
                type:'store',
                bindClick:false,
                title:'${message("更换地址")}',
                url:'/member/store/select_store_address.jhtml?storeId='+storeId,
                callback:function(rows){
                    if(rows.length>0){
                        var row = rows[0];
                        $("input[name='consignee']").attr("value",row.consignee);
                        $("input[name='consigneeMobile']").attr("value",row.mobile);
                        $("input[name='address']").attr("value",row.address);
                        $("input[name='zipCode']").attr("value",row.zip_code);
                        $("input[name='addressOutTradeNo']").attr("value",row.out_trade_no);
                        $(".select_area").find(".fieldSet").empty();
                        var areaId = row.area;
                        if(areaId==null)areaId='';
                        var areaName = row.area_full_name;
                        if(areaName==null)areaName='';
                        var treePath = row.tree_path;
                        if(treePath==null)treePath='';
                        $("input[name='areaId']").val(areaId);
                        $("input[name='areaName']").val(areaName);

                    }
                }
            });
        }

        function selectInvoice(e){
            var storeId = $("input[name='storeId']").val();
            if(storeId==""){
                $.message_alert('请选择客户');
                return false;
            }
            $(e).bindQueryBtn({
                type:'supplier',
                bindClick:false,
                title:'${message("查询发票抬头")}',
                url:'/member/store/select_store_invoice.jhtml?storeId='+storeId,
                callback:function(rows){
                    if(rows.length>0){
                        $("input[name='invoiceTitle']").val(rows[0].invoice_title);
                    }
                }
            });
        }

		function editAcount(t,e){
			if($(t).attr("kid")=="discount"){
				var $tr = $(t).closest("tr");
				var discount=$(t).val();
				var price=$tr.find(".proPriceHeadPrice").val();
				var discountPrice = Number(accMul(discount,price))/100;
				var priceDifference = accSub(price,discountPrice);
				var zxgk=isNaN($tr.find(".zxgk").val()) ? 0 : $tr.find(".zxgk").val();
				var zxptjc=isNaN($tr.find(".zxptjc").val()) ? 0 : $tr.find(".zxptjc").val();
				var qtzx=isNaN($tr.find(".qtzx").val()) ? 0 : $tr.find(".qtzx").val();
				discountPrice = accSub(accSub(accSub(discountPrice,zxgk),zxptjc),qtzx);
				$tr.find(".price").val(discountPrice);
				$tr.find(".price").html(currency(discountPrice,true));
				$tr.find(".priceDifference").val(priceDifference);
				$tr.find(".priceDifference").html(currency(priceDifference,true));
				countTotal($tr,"no");
			}else if($(t).attr("kid")=="price"){
				var $tr = $(t).closest("tr");
				var price=$(t).val();
				var proPriceHeadPrice =Number($tr.find("input.proPriceHeadPrice ").val());
				var discount = Number((price/proPriceHeadPrice*100)).toFixed(2);
				var priceDifference = accSub(proPriceHeadPrice,price);
				$tr.find(".discount").val(discount);//折扣率
				$tr.find(".discount").html(discount);
				$tr.find(".priceDifference").val(priceDifference);
				$tr.find(".priceDifference").html(currency(priceDifference,true));
				countTotal($tr,"no");
			}else if($(t).attr("kid")=="proPriceHeadPrice"){
				var $tr = $(t).closest("tr");
				var proPriceHeadPrice=$(t).val();
				var discount=$tr.find(".discount").val();
				var discountPrice = Number(accMul(discount,proPriceHeadPrice))/100;
				var priceDifference = accSub(proPriceHeadPrice,discountPrice);
				$tr.find(".price").val(discountPrice);
				$tr.find(".price").html(currency(discountPrice,true));
				$tr.find(".priceDifference").val(priceDifference);
				$tr.find(".priceDifference").html(currency(priceDifference,true));
				countTotal($tr,"no");
			}else if($(t).attr("kid")=="priceDifference"){
				var $tr = $(t).closest("tr");
				var priceDifference=$(t).val();
				var proPriceHeadPrice=$tr.find(".proPriceHeadPrice").val();
				var zxgk=isNaN($tr.find(".zxgk").val()) ? 0 : $tr.find(".zxgk").val();
				var zxptjc=isNaN($tr.find(".zxptjc").val()) ? 0 : $tr.find(".zxptjc").val();
				var qtzx=isNaN($tr.find(".qtzx").val()) ? 0 : $tr.find(".qtzx").val();
				var discountPrice = accSub(accSub(accSub(accSub(proPriceHeadPrice, priceDifference),zxgk),zxptjc),qtzx);
                if(proPriceHeadPrice != 0 ){
					var discount = Number((accSub(proPriceHeadPrice, priceDifference)/proPriceHeadPrice*100)).toFixed(2);
                }else{
                    var discount = 100;
                }
				$tr.find(".discount").val(discount);//折扣率
				$tr.find(".discount").html(discount);
				$tr.find(".price").val(discountPrice);
				$tr.find(".price").html(currency(discountPrice,true));
			}else if($(t).attr("kid")=="orgPriceDifference"){
				//--计算
				var $tr = $(t).closest("tr");
				var orgPriceDifference=$(t).val();//结算价价差
				var reSaleOrgPrice=$tr.find(".reSaleOrgPrice").val();//原平台结算价价
				var reProductOrgPrice=$tr.find(".reProductOrgPrice").val();//原产品部结算价
				var gkjsjc=$tr.find(".gkjsjc").val();//钢扣结算价差
				var saleOrgPrice=accSub(accSub(reSaleOrgPrice,orgPriceDifference),gkjsjc);//平台结算价价
				var productOrgPrice=accSub(accSub(reProductOrgPrice,orgPriceDifference),gkjsjc);//产品部结算价
				//--赋值
				$tr.find(".saleOrgPriceText").html(currency(saleOrgPrice,true));//平台结算价价
				$tr.find(".saleOrgPrice").val(saleOrgPrice);//平台结算价价
				$tr.find(".productOrgPriceText").html(currency(productOrgPrice,true));//产品部结算价
				$tr.find(".productOrgPrice").val(productOrgPrice);//产品部结算价
				$tr.find(".orgPriceDifference").html(currency(orgPriceDifference,true));//结算价价差
				$tr.find(".orgPriceDifference").val(orgPriceDifference);//结算价价差
			}else if($(t).attr("kid")=="zxgk"){
				var $tr = $(t).closest("tr");
				var zxgk=$(t).val();
				var proPriceHeadPrice=$tr.find(".proPriceHeadPrice").val();
				var priceDifference=$tr.find(".priceDifference").val();
				var zxptjc=$tr.find(".zxptjc").val();
				var qtzx=$tr.find(".qtzx").val();
				var discountPrice = accSub(accSub(accSub(accSub(proPriceHeadPrice, priceDifference),zxgk),zxptjc),qtzx);

				$tr.find(".price").val(discountPrice);
				$tr.find(".price").html(currency(discountPrice,true));
				$tr.find(".gkjsjc").val(zxgk);

				var reSaleOrgPrice=$tr.find(".reSaleOrgPrice").val();//原平台结算价价
				var reProductOrgPrice=$tr.find(".reProductOrgPrice").val();//原产品部结算价
				var gkjsjc=$tr.find(".gkjsjc").val();//钢扣结算价差
				var orgPriceDifference=$tr.find(".orgPriceDifference").val();//结算价价差
				var saleOrgPrice = accSub(accSub(reSaleOrgPrice, gkjsjc),orgPriceDifference);
				var productOrgPrice = accSub(accSub(reProductOrgPrice, gkjsjc),orgPriceDifference);
				//--赋值
				$tr.find(".saleOrgPriceText").html(currency(saleOrgPrice,true));//平台结算价价
				$tr.find(".saleOrgPrice").val(saleOrgPrice);//平台结算价价
				$tr.find(".productOrgPriceText").html(currency(productOrgPrice,true));//产品部结算价
				$tr.find(".productOrgPrice").val(productOrgPrice);//产品部结算价
			}else if($(t).attr("kid")=="zxptjc"){
				var $tr = $(t).closest("tr");
				var zxptjc=$(t).val();
				var proPriceHeadPrice=$tr.find(".proPriceHeadPrice").val();
				var priceDifference=$tr.find(".priceDifference").val();
				var zxgk=$tr.find(".zxgk").val();
				var qtzx=$tr.find(".qtzx").val();
				var discountPrice = accSub(accSub(accSub(accSub(proPriceHeadPrice, priceDifference),zxgk),zxptjc),qtzx);
				$tr.find(".price").val(discountPrice);
				$tr.find(".price").html(currency(discountPrice,true));
			}else if($(t).attr("kid")=="qtzx"){
				var $tr = $(t).closest("tr");
				var qtzx=$(t).val();
				var proPriceHeadPrice=$tr.find(".proPriceHeadPrice").val();
				var priceDifference=$tr.find(".priceDifference").val();
				var zxgk=$tr.find(".zxgk").val();
				var zxptjc=$tr.find(".zxptjc").val();
				var discountPrice = accSub(accSub(accSub(accSub(proPriceHeadPrice, priceDifference),zxgk),zxptjc),qtzx);
				$tr.find(".price").val(discountPrice);
				$tr.find(".price").html(currency(discountPrice,true));
			}  else if($(t).attr("kid")=="orgPriceDifference"){
				var $tr = $(t).closest("tr");
				var orgPriceDifference=$(t).val();//结算价价差
				var quantity=$tr.find(".quantity").val();//数量
				var reSaleOrgPrice=$tr.find(".reSaleOrgPrice").val();//原平台结算价
				var reProductOrgPrice=$tr.find(".reProductOrgPrice").val();//原产品部结算价
				var gkjsjc=$tr.find(".gkjsjc").val();//钢扣结算价差
				var saleOrgPrice=accSub(accSub(reSaleOrgPrice,orgPriceDifference),gkjsjc);//平台结算价价
				var productOrgPrice=accSub(accSub(reProductOrgPrice,orgPriceDifference),gkjsjc);//产品部结算价
				$tr.find(".saleOrgPriceText").html(currency(saleOrgPrice,true));//平台结算价
				$tr.find(".saleOrgPrice").val(saleOrgPrice);//平台结算价
				$tr.find(".productOrgPriceText").html(currency(productOrgPrice,true));//产品部结算价
				$tr.find(".productOrgPrice").val(productOrgPrice);//产品部结算价
				$tr.find(".saprice").val(currency(quantity*saleOrgPrice,true));//平台结算金额
				$tr.find(".orgPriceDifference").html(currency(orgPriceDifference,true));//结算价价差
				$tr.find(".orgPriceDifference").val(orgPriceDifference);//结算价价差

			}else if($(t).attr("kid")=="gkjsjc"){
				//--计算
				var $tr = $(t).closest("tr");
				var gkjsjc=$(t).val();//钢扣结算价差
				var reSaleOrgPrice=$tr.find(".reSaleOrgPrice").val();//原平台结算价价
				var orgPriceDifference=$tr.find(".orgPriceDifference").val();//结算价价差
				var reProductOrgPrice=$tr.find(".reProductOrgPrice").val();//原产品部结算价
				var saleOrgPrice=accSub(accSub(reSaleOrgPrice,orgPriceDifference),gkjsjc);//平台结算价价
				var productOrgPrice=accSub(accSub(reProductOrgPrice,orgPriceDifference),gkjsjc);//产品部结算价
				//--赋值
				$tr.find(".saleOrgPriceText").html(currency(saleOrgPrice,true));//平台结算价价
				$tr.find(".saleOrgPrice").val(saleOrgPrice);//平台结算价价
				$tr.find(".productOrgPriceText").html(currency(productOrgPrice,true));//产品部结算价
				$tr.find(".productOrgPrice").val(productOrgPrice);//产品部结算价
			}
		}

</script>
</head>
<body>
	<div class="pathh">&nbsp; ${message("添加退货单")}</div>
	<form id="inputForm" action="/aftersales/b2b_returns/save.jhtml"
		method="post" type="ajax" validate-type="validate">
		<div class="tabContent">
			<table class="input input-edit">
				<tr>
					<th>${message("退货单号")}:</th>
					<td></td>
					<th><span class="requiredField">*</span>${message("客户")}:</th>
					<td>
						<span class="search" style="position: relative">
							<input class="storeId" type="hidden" name="storeId" value="${store.id }" maxlength="200" btn-fun="clear">
							<input class="text storeName" maxlength="200" type="text" value="${store.name}" onkeyup="clearSelect(this)" readOnly />
							<input type="button" class="iconSearch" id="selectStore">
						</span>
					</td>
					<th>${message("机构")}:</th>
					<td><span class="search" style="position: relative"> <input
							type="hidden" name="saleOrgId" class="text saleOrgId"
							btn-fun="clear"
							value="[#if store??]${store.saleOrg.id}[#else]${saleOrg.id}[/#if]" />
							<input type="text" name="saleOrgName" class="text saleOrgName"
							maxlength="200" onkeyup="clearSelect(this)"
							value="[#if store??]${store.saleOrg.name}[#else]${saleOrg.name}[/#if]"
							readOnly /> <!--<input type="button" class="iconSearch" value="" id="selectSaleOrg">-->
					</span></td>
					<th><span class="requiredField">*</span>${message("经营组织")}:</th>
					<td><span id="organizationId">${management_organization_name}</span>
						<input type="hidden" name="organizationId"
						class="text organizationId" value="${management_organization_id}" />
					</td>
				</tr>
				<tr>
					<th>
						<span class="requiredField">*</span>
						${message("仓库")}:
					</th>
					<td>
						<span class="search" style="position: relative">
							<input type="hidden" name="warehouseId" class="text warehouseId" btn-fun="clear" value="${warehouseSbuId}" />
							<input type="text" name="warehouseName" class="text warehouseName" maxlength="200" onkeyup="clearSelect(this)" value="${warehouseSbuName}" readOnly />
							<input type="hidden" class="productionPlantId" value="${production_plant}" />
							<input type="button" class="iconSearch" value="" id="selectWarehouse">
						</span>
					</td>
					<th>${message("仓库类型")}:</th>
					<td>
						<span class="typeSystemDictValue">${type_system_dict_value}</span>
						<input type="hidden" name="typeSystemDictId" class="typeSystemDictId" value="${type_system_dict_id}" />
						<input type="hidden" class="typesystemDictFlag" value="${type_system_dict_flag}" />
					</td>
					<th>${message("退货单状态")}:</th>
					<td></td>
					<th>${message("退货人姓名")}:</th>
					<td><input name="name" type="text" class="text"></td>
				</tr>
				<tr>
					<th>${message("退货人电话")}:</th>
					<td><input name="mobile" type="text" class="text"></td>
					<th>${message("发票抬头")}:</th>
					<td><span class="search" style="position: relative"> <input
							name="invoiceTitle" class="text invoiceTitle" maxlength="200"
							type="text" value="" readonly/> <input type="button"
								class="iconSearch" value="" onclick="selectInvoice(this)"></span>
					</td>
					<th>${message("业务类型")}:</th>
					<td><select id="businessTypeId" name="businessTypeId"
						class="text"> [#list businessTypes as businessType]
							<option value="${businessType.id}" [#if
								businessTypeIdj==businessType.id]selected[/#if]>${businessType.value}</option>
							[/#list]
					</select></td>
					<th>${message("Sbu")}:</th>
					<td><input type="hidden" name="sbuId" class="text sbuId"
						id="sbuId" btn-fun="clear" value="${sbu.id}" /> <span id="sbuName">
							${sbu.name} </span></td>
				<tr>
					<th><span class="requiredField">*</span>${message("发运方式")}:</th>
					<td><select id="smethod" name="smethod" class="text">
							[#list sbuItems as sbuItem]
							<option value="${sbuItem.shippingMethod.value}" [#if
								shippingMethodId==sbuItem.sbu.id]selected[/#if]>${sbuItem.shippingMethod.value}</option>
							[/#list]
					</select></td> [#if roles]
					<th><span id="memberRank1">${message("价格类型")}:</span></th>
					<td><span id="memberRank2" class="search"
						style="position: relative"> <input type="hidden"
							name="memberRankId" class="text memberRankId" id="memberRankId"
							btn-fun="clear" value="${memberRank.id}" /> <input
							class="text memberRankName" maxlength="200" type="text"
							name="memberRankName" value="${memberRank.name}"
							onkeyup="clearSelect(this)" readOnly /> <input type="button"
							class="iconSearch" value="" id="selectMemberRank">
								<div class="pupTitleName  memberRank"></div></span></td> [/#if]
					<th><span class="requiredField">*</span>${message("单据日期")}:</th>
					<td><input id="startTime" name="glDate" class="text"
						value="${GLDate}"
						onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});"
						type="text" btn-fun="clear" /> <input id="endTime" class="text"
						value=""
						onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});"
						type="hidden" btn-fun="clear" /></td>

					<th>${message("区域经理")}:</th>
					<td><input type="hidden" name="regionalManagerId" class="text storeMemberId" btn-fun="clear" value="${store.storeMember.id}" /> <span id="regionalManagerName">${store.storeMember.name} </span></td>
					[#if contractRoles==1]
								<th>${message("合同名称")}:</th>
            		<td>
            		 <span class="search" style="position: relative">
            		  <input  type="hidden" name="contractId" class="text contractId" value="" btn-fun="clear" />
            		  <input type="text" name="contractName" class="text contractName" value=""maxlength="200" onkeyup="clearSelect(this)" readOnly />
            		  <input type="button" class="iconSearch" value="" id="selectContract"></span>

            			</td>
            		[/#if]
				</tr>
				<tr>
					<th>
						<span class="requiredField">*</span>${message("退货种类")}:
					</th>
					<td>
						<select class="text category" name="category" onchange="cleanAfterSale(this)">
							<option value="0" selected>${message("其他退货")}</option>
							<option value="1" >${message("售后退货")}</option>
						</select>
					</td>
					<th>
						${message("售后单据")}:
					</th>
					<td>
						<span class="search" style="position: relative">
							[#--<input type="hidden" name="aftersaleId" class="text aftersaleId" btn-fun="clear" value="" /> --]
							<input type="text" name="fourAftersaleSn" class="text fourAftersaleSn" maxlength="200" onkeyup="clearSelect(this)" value="" readOnly />
							<input type="button" class="iconSearch" value="" id="selectAftersale">
						</span>
					</td>
					<th>
						${message("工厂")}:
					</th>
					<td>
						<span class="search" style="position:relative">
		                    <input type="hidden" name="factoryId" class="text factoryId" value="${b2bReturns.factoryId}" btn-fun="clear"/>
		                    <input type="text" name="factoryName" class="text factoryName"  value="${b2bReturns.factoryName}" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
		                   <!-- <input type="button" class="iconSearch" value="" id="openSaleOrg">-->
	                    </span>
					</td>

					<th>${message("客户简称")}:</th>
					<td>
						<span id="storeAlias">${store.alias}</span>
					</td>
				</tr>
				<tr>
					<th><span class="requiredField">*</span>${message("退货原因")}:</th>
					<td colspan="7"><textarea name="reason" class="text reason"
							 id="reason"></textarea></td>
				</tr>
			</table>

			<div class="title-style">${message("收货信息")}
				<div class="btns">
					<a href="javascript:;" id="addReceiveAddress" class="button">更换收货信息</a>
				</div>
			</div>
			<table class="input input-edit">
				<tr class="border-L1">
					<th><span class="requiredField">*</span>${message("收货人")}:</th>
					<td><input type="text" class="text" name="consignee" value=""
						btn-fun="clear" readOnly></td>
					<th><span class="requiredField">*</span>${message("收货人电话")}:</th>
					<td><input type="text" class="text" name="consigneeMobile"
						value="" btn-fun="clear" readOnly></td>
					<th>${message("收货地区邮编")}:</th>
					<td><input type="text" class="text" name="zipCode" value=""
						btn-fun="clear" readOnly></td>
					<th>地址外部编码:</th>
					<td><input type="text" class="text" name="addressOutTradeNo"
						value="" btn-fun="clear" readOnly /></td>
				</tr>
				<tr class="border-L1">
					<th><span class="requiredField">*</span>${message("收货地区")}：</th>
					<td><span class="search" style="position: relative"> <input
							type="hidden" name="areaId" class="text areaId" value=""
							btn-fun="clear" /> <input type="text" name="areaName"
							class="text areaName" value="" maxlength="200"
							onkeyup="clearSelect(this)" readOnly />
					</span>
					</td>
					<th><span class="requiredField">*</span>${message("收货地址")}:</th>
					<td colspan="3"><input type="text" class="text" name="address"
						value="" btn-fun="clear" readOnly></td>
					<th></th>
					<td></td>
				</tr>
			</table>
			<div class="title-style" id="storeSurplus">${message("客户余额")}</div>
			<table class="input input-edit">
				<tr>
					<th><span class="requiredField">*</span>${message("退款金额")}:</th>
					<td id="amount">
						<div>
							<input type="text" class="text" name="amount" value=""
								minData="0" oninput="editPrice (this,event)"
								onpropertychange="editPrice(this,event)" readOnly>
						</div>
					</td>
				</tr>
				<tr>
					<th>${message("可用余额")}:</th>
					<td><span class="red" id="bal">${currency(storeBalance,true)}</span>
						<input type="hidden" name="storeBalance" id="storeBalance"
						value="${storeBalance}" /></td>
					<th>${message("订单金额")}:</th>
					<td><span class="red" id="total"> [#if order!=null]
							${currency(order.amount, true)} [#else] ￥0.00 [/#if] </span></td>
					<th>${message("差额")}:</th>
					<td><span class="red" id="chae">${currency(chae,true)}</span>
					</td>
					<th>${message("余额")}:</th>
					<td><span class="red" id="yue">${currency(yue,true)}</span></td>
					<th>${message("授信")}:</th>
					<td><span class="red" id="credit">${currency(credit,true)}</span>
					</td>
				</tr>
			</table>
			<div class="title-style" id="organizationSurplus">
				${message("经营组织余额")}
			</div>
			<table id="table-m2"></table>
			<div class="title-style">
				${message("退货明细")}:
				<div class="btns">
					<a href="javascript:;" id="addProduct" class="button">选择产品</a>
				</div>
				[#if referenceOrder !=0]
					[#if sbu.id!=3]
						<div class="btns">
							<a href="javascript:;" id="findOrder" class="button">参考订单</a>
						</div>
					[/#if]
				[/#if]
				[#if referenceShipping !=0]
					[#if sbu.id!=1]
						<div class="btns">
							<a href="javascript:;" id="findShipping" class="button">参考发货单</a>
						</div>
					[/#if]
				[/#if]
			</div>
			<table id="table-m1"></table>
			<div class="title-style">${message("退货单汇总")}</div>
			<table class="input input-edit">
				<tr>
					<!-- 2019-05-16 冯旗 壁纸隐藏箱支，重量体积 -->
					[#if sbu.id!=3]
					<th>${message("退货单箱数")}:</th>
					<td><span id="totalBoxQuantity"></span></td>
					<th>${message("退货单支数")}:</th>
					<td><span id="totalBranchQuantity"></span></td> [/#if]
					<th>${message("退货单数量")}:</th>
					<td><span id="totalQuantity"></span></td> [#--
					<th>${message("退货单重量")}:</th>
					<td><span id="totalWeight"></span></td>--]
				</tr>
				[#--
				<tr>
					<th>${message("退货单体积")}:</th>
					<td><span id="totalVolume"></span></td>
				</tr>
				--]
			</table>
			<div class="title-style">
				${message("附件信息")}:
				<div class="btns">
					<a href="javascript:;" id="addAttach" class="button">添加附件</a>
				</div>
			</div>
			<table id="table-attach"></table>
		</div>
		<div class="fixed-top">
			<input type="button" class="button sureButton" value="${message("1013")}" onclick="save(this)" /> <input type="button"
				onclick="location.reload(true);" class="button resetButton ml15"
				value="${message("重置")}">
		</div>
	</form>
</body>
</html>