<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("查看退货")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript">
function editQty(t,e){
	var pointN=3;
	if ($(t).attr("kid")=="quantity"){
		pointN=6;
	}
	if(extractNumber(t,pointN,false,e)){
		var qy=0;
		var $tr = $(t).closest("tr");
		if($(t).attr("kid")=="box"){
			$tr.find(".scatteredQuantityStr").html(0);
			$tr.find(".scatteredQuantity").val(0);
		}else if($(t).attr("kid")=="branch"){
			var quantity=$(t).val();
			var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
			var box=parseInt(quantity/branchPerBox);
			var scattered=quantity%branchPerBox;
			$tr.find(".boxQuantity").val(box);
			$tr.find(".scatteredQuantityStr").html(scattered);
			$tr.find(".scatteredQuantity").val(scattered);
		}else if($(t).attr("kid")=="quantity"){
			var quantity=$(t).val();
			var qy=$(t).val();
			var perBranch=$tr.find(".perBranch").val();  //每支单位数
			var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
			var branch_quantity=0;
			var box_quantity=0;
			var scattered=0;
			
			if(perBranch!=0 && branchPerBox!=0){
				 branch_quantity=quantity/perBranch;
				 box_quantity=parseInt(branch_quantity/branchPerBox);
				 scattered=(branch_quantity%branchPerBox).toFixed(6);
			}
			$tr.find(".boxQuantity").val(box_quantity);
			$tr.find(".branchQuantity").val(branch_quantity);
			$tr.find(".scatteredQuantityStr").html(scattered);
			$tr.find(".scatteredQuantity").val(scattered);
		}
		
		countTotal();
		if($(t).attr("kid")=="quantity"){
			$(t).val(qy);
		}
		countVolume();
		var id = $(t).attr("itemId");
		var itemOrg = Number($(t).attr("org"));
		var qty = Number($(t).val());
		if(isNaN(qty)){
			qty = 0;
		}
		
		var $input = $("input.cq[parentId='"+id+"']");
		$input.each(function(){
			var $this = $(this);
			var org = Number($this.attr("org"));
			var value = qty*org/itemOrg;
			$this.val(value);
			$this.next(".qty-text").text(value);
		})
	}
}
function countVolume(){
	var $input = $("input.quantity");
	var totalVolume = 0;
	$input.each(function(){
		var $this = $(this);
		var $tr = $this.closest("tr");
		var volume = Number($tr.find("input.volume").val());
		var volumes = accMul(Number($this.val()),volume).toFixed(6);
		if(isNaN(volumes)){
			volumes = 0.00;
		}
		totalVolume = accAdd(totalVolume,volumes).toFixed(6);
	});
	$("span[name='volumeall']").text(totalVolume);
	$("input[name='volume']").val(totalVolume);
}
function editPrice(t,e){
	if(extractNumber(t,2,false,e)){
		countTotal();
	}
}

function newCountTotal(){
	var totalBoxQuantity = 0;
	var totalBranchQuantity = 0;
	var $bInput = $("input.quantity");
	$bInput.each(function(){
        var $tr = $(this).closest("tr");
        var quantity=$(this).val();
        var branchPerBox=$tr.find(".branchPerBox").val();
        var perBranch=$tr.find(".perBranch").val();
        var scatteredQuantity=$tr.find(".scatteredQuantity").val();//零散支数
        if(quantity!=''){
        	var branchQuantity=accDiv(quantity,perBranch)
        	var boxQuantity=accDiv(branchQuantity,branchPerBox);
        	branchQuantity=accAdd(branchQuantity,scatteredQuantity);
        	if(isNaN(branchQuantity)){
        		branchQuantity = 0;
    		}
        	totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
        	totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
        	$tr.find(".branchQuantity").val(branchQuantity);//支数
        	$tr.find(".branchQuantityStr").html(branchQuantity);
        	if(isNaN(quantity)){
        		quantity = 0;
    		}
        	$tr.find(".branchQuantity").val(branchQuantity);//数量
        	$tr.find(".boxQuantity").val(boxQuantity);//数量
        	$tr.find(".quantityStr").html(quantity);
        }
	});
	$("#totalBoxQuantity").text(totalBoxQuantity);
	$("#totalBranchQuantity").text(totalBranchQuantity);
	
	var $input = $("input.quantity");
	var total = 0;
	var totalVolume = 0;
	var totalWeight = 0;
	var totalQuantity = 0;
	
	var b = $("#storeBalance").val();
	$input.each(function(){
		var $this = $(this);
		var $tr = $this.closest("tr");
		var $price_box = $tr.find(".price-box");
		var price;
		if($price_box.length==0 || $price_box.prop("checked")==false){
			price = Number($tr.find("input.price").val());
		}else{
			price = Number($tr.find("input.origMemberPrice ").val());
		}
		
		var volume=$tr.find(".lineVolume").val();
		var weight=$tr.find(".lineWeight").val();
		
		var volumeAmount=Number(accMul($this.val(),volume)).toFixed(6);
		var weightAmount=Number(accMul($this.val(),weight)).toFixed(6);
		
		var amount = Number($this.val())*price;
		
		totalQuantity=Number(accAdd(totalQuantity,Number($this.val()))).toFixed(6);
		
		if(isNaN(amount)){
			amount = 0;
		}
		total = Number(total)+Number(currency(amount,false));
		$tr.find(".trprice").html(currency(amount,true));//订单行金额
		
		if(isNaN(volumeAmount)){
			volumeAmount = 0;
		}
		totalVolume = accAdd(totalVolume,Number(volumeAmount).toFixed(6)); 
		$tr.find(".lineVolumeAmount").html(volumeAmount);//体积
		
		if(isNaN(weightAmount)){
			weightAmount = 0;
		}
		totalWeight = accAdd(totalWeight,Number(weightAmount).toFixed(6)); 
		$tr.find(".lineWeightAmount").html(weightAmount);//重量
	});
	$("#total").text(currency(total,true));
	$("#totalVolume").text(totalVolume);
	$("#totalWeight").text(totalWeight);
	$("#totalQuantity").text(totalQuantity);
	$("input[name='amount']").val(currency(total,false));
	
	var storeBalance = $("#storeBalance").val()
	$("#chae").text(currency(storeBalance-total,true));
	
	

}
function countTotal(){
	var totalBoxQuantity = 0;
	var totalBranchQuantity = 0;
	var $bInput = $("input.boxQuantity");
	$bInput.each(function(){
        var $tr = $(this).closest("tr");
        var boxQuantity=$(this).val();
        var branchPerBox=$tr.find(".branchPerBox").val();
        var perBranch=$tr.find(".perBranch").val();
        var scatteredQuantity=$tr.find(".scatteredQuantity").val();//零散支数
        if(boxQuantity!=''  && branchPerBox!=0 && perBranch!=0){
        	var branchQuantity=accMul(boxQuantity,branchPerBox);
        	branchQuantity=accAdd(branchQuantity,scatteredQuantity);
        	if(isNaN(branchQuantity)){
        		branchQuantity = 0;
    		}
        	totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
        	totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
        	$tr.find(".branchQuantity").val(branchQuantity);//支数
        	$tr.find(".branchQuantityStr").html(branchQuantity);
        	var quantity=accMul(branchQuantity,perBranch);
        	if(isNaN(quantity)){
        		quantity = 0;
    		}
        	$tr.find(".quantity").val(quantity);//数量
        	$tr.find(".quantityStr").html(quantity);
        }
	});
	$("#totalBoxQuantity").text(totalBoxQuantity);
	$("#totalBranchQuantity").text(totalBranchQuantity);
	
	var $input = $("input.quantity");
	var total = 0;
	var orgTotal=0;
	var totalVolume = 0;
	var totalWeight = 0;
	var totalQuantity = 0;
	$input.each(function(){
		var $this = $(this);
		var $tr = $this.closest("tr");
		var price = Number($this.closest("tr").find("input.price").val());
		var saleOrgPrice = Number($this.closest("tr").find("input.saleOrgPrice").val());
		var amount = accMul(Number($this.val()),price).toFixed(2);
		if(isNaN(amount)){
			amount = 0;
		}
		total = accAdd(total,amount).toFixed(2);
		$tr.find(".trprice").html(currency(amount,true));
		
		var saleAmount = accMul(Number($this.val()),saleOrgPrice).toFixed(2);
		if(isNaN(saleAmount)){
			saleAmount = 0;
		}
		$tr.find(".saprice").html(currency(saleAmount,true));
		
		var volume=$tr.find(".lineVolume").val();
		var weight=$tr.find(".lineWeight").val();
		
		var volumeAmount=Number(accMul($this.val(),volume)).toFixed(6);
		var weightAmount=Number(accMul($this.val(),weight)).toFixed(6);
		
		totalQuantity=Number(accAdd(totalQuantity,Number($this.val()))).toFixed(6);
		
		if(isNaN(volumeAmount)){
			volumeAmount = 0;
		}
		orgTotal= accAdd(orgTotal,saleAmount).toFixed(2);
		totalVolume = accAdd(totalVolume,Number(volumeAmount).toFixed(6)); 
		$tr.find(".lineVolumeAmount").html(volumeAmount);//体积
		
		if(isNaN(weightAmount)){
			weightAmount = 0;
		}
		totalWeight = accAdd(totalWeight,Number(weightAmount).toFixed(6)); 
		$tr.find(".lineWeightAmount").html(weightAmount);//重量
		
	});
	$("#amount").text(currency(total,true));
	$("#totalVolume").text(totalVolume);
	$("#totalWeight").text(totalWeight);
	$("#totalQuantity").text(totalQuantity);
	$("input[name='amount']").val(currency(total,false));
	$("#saleAmount").text(currency(orgTotal,true));
	
	var storeBalance = $("#storeBalance").val();
	$("#chae").text(currency(storeBalance-total,true));
	
	
	[#if hiddenAmount==0]
	$("#amount").text("***");
	$("#freight").text("***");
	$(".payAmount").text("***");
	$("#balance").text("***");
	$("span.priceText").text("***");
	$(".trprice").text("***");
	$("span.saleOrgPriceText").text("***");
	$(".saprice").text("***");
	[/#if]
}

function initGrid(){
	/**初始化订单附件*/
    var depositAttach_items = ${b2bReturnsAttach_json};
    var depositAttachIndex=0;
	var cols = [				
    	{ title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex,obj){
			if(obj==undefined){
				var url = item.url;
				var fileObj = getfileObj(item.file_name , item.name, item.suffix);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['b2bReturnsAttachs['+depositAttachIndex+'].id']=item.id;
				hideValues['b2bReturnsAttachs['+depositAttachIndex+'].url']=url;
				hideValues['b2bReturnsAttachs['+depositAttachIndex+'].suffix']=fileObj.suffix;
				
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : item.create_date,
					textName:'b2bReturnsAttachs['+depositAttachIndex+'].name',
					hideValues: hideValues
				});
			}else{
				var url = item.url;
				var fileObj = getfileObj(item.name);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['b2bReturnsAttachs['+depositAttachIndex+'].url']=url;
				hideValues['b2bReturnsAttachs['+depositAttachIndex+'].suffix']=fileObj.suffix;
				
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : '',
					textName:'b2bReturnsAttachs['+depositAttachIndex+'].name',
					hideValues:hideValues
				});
			}
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:590 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text" name="b2bReturnsAttachs['+depositAttachIndex+'].memo" >'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
			depositAttachIndex++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}},
	];
	var $amGrid=$('#table-attach').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        items:depositAttach_items,
        checkCol: false,
        autoLoad: true,
        callback:function(){
        	countVolume();
        	countVolume();
        }
    });
    
    var $addAttach = $("#addAttach");
	var attachIdnex = 0;
	var option1 = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row=data[i].file_info;
				$amGrid.addRow(row,null,1);
	        }
			
        }
    }
    $addAttach.file_upload(option1);
}

var line_no = 1;
$().ready(function() {
	
	[#if flag==1]
	$("#wf_area").load("/wf/wf.jhtml?wfid=${b2bReturns.wfId}");
	[/#if]
	
	var $inputForm = $("#inputForm");
	var b2bReturnsItemIndex = 0;
	var $addProduct = $("#addProduct");
	var $findOrder = $("#findOrder");
	var $addOrderItem = $("#addOrderItem");
	var items = ${jsonStr};
	var qNum = ""
		var orderStatus = '${v.status}';
		var grade={"1":"优等品","2":"二等品","3":"二等品"};
	var cols0 = [	
		{ title:'${message("行号")}', width:40, align:'center',renderer: function(val,item,rowIndex){
			return '<span class="line_no">'+ line_no  +'</span>';
		}},
		{ title:'操作', align:'center', width:60, renderer: function(val,item,rowIndex){
			return '<a href="javascript:;" class="deleteProduct btn-delete" >删除</a>';
		}},
		{ title:'参考订单号', align:'center',name:'orderSn',width:100,renderer: function(val,item,rowIndex){
			return val+'<input type="hidden" class="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].sn" value="'+val+'">';
		}},
		{ title:'${message("12211")}', name:'vonder_code' ,align:'center',width:150, renderer: function(val,item,rowIndex, obj){
// 			var html;
// 			if(obj==undefined){
// 				if(item.product==null){
// 					html='<input type="text" name="orderItems['+stockInItemIndex+'].vonderCode" class="pVonderCode productId_'+item.vonderCode+' text" value="'+val+'" />';
// 				}else{
// 					html=val+'<input type="hidden" name="orderItems['+stockInItemIndex+'].vonderCode" value="'+val+'" />';
// 				}
// 			}
// 			else{
// 				if(item.else==1){
// 					html='<input type="text" name="orderItems['+stockInItemIndex+'].vonderCode" class="pVonderCode productId_'+item.vonderCode+' text" value="" />';
// 				}else{
// 					html='<input type="hidden" name="orderItems['+stockInItemIndex+'].vonderCode"  value="'+val+'" />'+val;
// 				}
// 			}
			return val;
		}},
		{ title:'${message("产品描述")}', name:'description' ,align:'center',width:400, renderer: function(val,item,rowIndex, obj){
			return val;
		}},
		{ title:'${message("单位")}', name:'unit' ,align:'center',width:80, renderer: function(val,item,rowIndex, obj){
			return val;
		}},
		{ title:'${message("产品分类")}', name:'product_category_name' ,align:'center',width:80},
	    {title:'${message("木种花色")}', align:'center',hidden:'true',name:'wood_type_or_color',renderer: function(val,item,rowIndex){
			return val+'<input type="hidden" class="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].woodTypeOrColor" value="'+val+'">';
		}}, 
		{ title:'产品名称', align:'center',name:'name',hidden:'true', width:250, renderer: function(val,item,rowIndex,obj){
			var pid=item.product;
			if(pid==undefined&&obj==1){
				pid=item.id;
			}
			if(obj==undefined){
				var orderItemId='';
				if(item.order_item!=null){
					orderItemId=item.order_item;
				}
				return '<a href="javascript:void(0);" onClick="product_view('+item.id+')" class="red">'+
				'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].product.id" class="productId_'+pid+'" value="'+pid+'">'+
				'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].id" value="'+item.id+'">'+item.name+'</a>'+
				'<input type="hidden" name="returnId" class="itemIds" value="'+item.id+'">'+
				'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].orderItem.id" class="orderItemId_'+orderItemId+'" value="'+orderItemId+'">';
			}
			else{
				var itemId='';
				if(item.item_id!=undefined){
					itemId=item.item_id;
				}
				return '<a href="javascript:void(0);" onClick="product_view('+item.id+')" class="red">'+
				'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].product.id" class="productId_'+pid+'" value="'+pid+'">'+item.name+'</a>'+
				'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].orderItem.id" class="orderItemId_'+itemId+'" value="'+itemId+'">';
			}
		}},
		{ title:'产品型号', align:'center',name:'model',hidden:'true', width:250},
		{ title:'${message("产品规格")}', name:'spec' ,hidden:'true',align:'center', width:110, renderer: function(val,item,rowIndex, obj){
			var html=val+'';
			if(item.else==1){
				html='<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].spec" class="pSpec productSpec_'+item.spec+' text" value="" />';
			}
			return html;
		}},									 
		{ title:'${message("产品等级")}', name:'product_grade' ,align:'center', width:100, renderer: function(val,item,rowIndex, obj){
			if( orderStatus==0 && (obj!=undefined || item.parent==null)){
				var html = '<select name="b2bReturnsItems['+b2bReturnsItemIndex+'].productGrade" class="text productGrade">'
				+'<option value="1"';
				if (val == 1) {
					html += 'selected="selected"';
				}
				html +='>优等品</option> <option value="2"';
				if (val == 2) {
				html += 'selected="selected"';
				}
				html += '>二等品</option> <option value="3"';
				if (val == 3) {
				html += 'selected="selected"';
				}
				html +='>一等品</option>';
				return html;  
			}
			var result = grade[val];
			if(result==undefined) result='';
 			var html='<span class="text productGradeText">'+result+'</span>'+'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].productGrade" class=" text" value="'+val+'" />';
 			return html;
		}},
		{ hidden:'true',title:'${message("体积")}', name:'volume' ,align:'center', renderer: function(val,item,rowIndex, obj){
			var html;
			var v = 0;
			if(obj==undefined){
				if(item.product==null){ 
					html=v;
				}else{
					if (val != '') {
						v = val;
					}
					html='<span class="lineVolumeAmount">'+v+'</span>'+
					'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].volume" class="volume lineVolume text" value="'+v+'" />';
				}
			}
			else{
				if(item.else==1){
					html = '<span class="lineVolumeAmount">'+v+'</span>';
				}else{
					if (val != '') {
						v = val;
					}
					html='<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].volume" class="volume lineVolume text"  value="'+v+'" />'+
					'<span class="lineVolumeAmount">'+v+'</span>';	
				}
			}
			return html+='<input type="hidden" value="'+v+'" />';
		}},
		{ hidden:'true',title:'${message("重量")}', name:'weight' ,align:'center', renderer: function(val,item,rowIndex, obj){
			return '<span class="lineWeightAmount"></span><input class="lineWeight" value="'+val+'" name="b2bReturnsItems['+b2bReturnsItemIndex+'].weight" type="hidden" />';
		}},
		
		
		{ title:'${message("退货箱数")}', name:'box_quantity', align:'center', width:100, renderer:function(val,item,rowIndex,obj){
			var quantity = 1;
			if(obj==undefined){
				quantity = val;
			}
			if(val != ""){
				quantity = val;
			}
			if( orderStatus==0 && (obj!=undefined || item.parent==null)){
				var str = '';
				if(obj==undefined || item.parent==null){
					str = 'itemId="'+item.id+'"';
				}
				if(item.branch_per_box==undefined || item.per_branch == undefined || item.branch_per_box==0 || item.per_branch == 0){
					quantity=0;
					return '-'+
					'<input type="hidden"  class="t boxQuantity"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].boxQuantity" value="'+quantity+'" />';
				}else{
					return '<div class="nums-input ov">'+
							'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)" />'+
			            	'<input type="text" kid="box" class="t boxQuantity" itemId="'+item.id+'" name="b2bReturnsItems['+b2bReturnsItemIndex+'].boxQuantity" value="'+quantity+'" org="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" />'+
			            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)" />'+
			        	'</div>';
				}
			}
			if( ( orderStatus==0) && (obj!=undefined || item.parent==null)){
				var str = '';
				if(obj==undefined || item.parent==null){
					str = 'itemId="'+item.id+'"'
				}
				if(item.branch_per_box==undefined || item.per_branch == undefined || item.branch_per_box==0 || item.per_branch == 0){
					quantity=0;
					return '-'+
					'<input type="hidden"  class="t boxQuantity"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].boxQuantity" value="'+quantity+'" />';
				}else{
				return '<div class="nums-input ov">'+
			            	
			            	'<input type="text"  class="t boxQuantity" itemId="'+item.id+'" name="b2bReturnsItems['+b2bReturnsItemIndex+'].boxQuantity" value="'+quantity+'" org="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" readonly="readonly" />'
			            	
			        	'</div>';
				}
			}else{
				var str = 'class="text boxQuantity"';
				if(obj == undefined || item.parent!=null){
					str = 'class="text cq boxQuantity" parentId="'+item.parent+'"';
				}
				if(item.branch_per_box==undefined || item.per_branch == undefined || item.branch_per_box==0 || item.per_branch == 0){
					quantity=0;
					return '-'+
					'<input type="hidden"  class="t boxQuantity"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].boxQuantity" value="'+quantity+'" />';
				}else{
					return '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].boxQuantity" value="'+quantity+'" org="'+quantity+'" '+str+' min="0" />'
					+'<span class="qty-text">'+quantity+'</span>';
				}
			}
			
		}},
		{ title:'${message("支数")}', name:'branch_quantity' ,align:'center', width:100, renderer: function(val,item,rowIndex, obj){
			var branchQuantity='';
			if(obj==undefined){
				branchQuantity = val;
			}
			if(val != ""){
				branchQuantity = val;
			}
			if( orderStatus==0 && (obj!=undefined || item.parent==null)){
				var str = '';
				if(obj==undefined || item.parent==null){
					str = 'itemId="'+item.id+'"'
				}
				var branch_per_box='';
				var  per_branch='';
				if(item.branch_per_box==undefined || item.per_branch == undefined || item.branch_per_box==0 || item.per_branch == 0){
					branch_per_box=0;
					per_branch=0;
					return '-'+
					       '<input type="hidden" kid="branch" class="t branchQuantity"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchQuantity" value="0"  >'+
					       '<input type=hidden class="branchPerBox" name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchPerBox" value="'+branch_per_box+'" /> '+
						   '<input type=hidden class="perBranch" name="b2bReturnsItems['+b2bReturnsItemIndex+'].perBranch" value="'+per_branch+'" />';
				}else{
					return '<div class="nums-input ov">'+
							'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
			            	'<input type="text" kid="branch" class="t branchQuantity" itemId="'+item.id+'" name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchQuantity" value="'+branchQuantity+'" org="'+branchQuantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" readonly="readonly" />'+
			            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
// 			            	'<input type=hidden class="branchPerBox" value="'+item.branch_per_box+'" /> '+
							'<input type=hidden class="branchBox" value="'+item.branch_box+'" />'+
							'<input type=hidden class="branchPerBox" name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchPerBox" value="'+item.branch_per_box+'" /> '+
							'<input type=hidden class="perBranch" name="b2bReturnsItems['+b2bReturnsItemIndex+'].perBranch" value="'+item.per_branch+'" />'+
			        	'</div>';
				}
			}
			if( (orderStatus==0) && (obj!=undefined || item.parent==null)){
				var str = '';
				if(obj==undefined || item.parent==null){
					str = 'itemId="'+item.id+'"'
				}
				var branch_per_box='';
				var  per_branch='';
				if(item.branch_per_box==undefined || item.per_branch == undefined || item.branch_per_box==0 || item.per_branch == 0){
					branch_per_box=0;
					per_branch=0;
					return '-'+
					       '<input type="hidden" kid="branch" class="t branchQuantity"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchQuantity" value="0"  >'+
					       '<input type=hidden class="branchPerBox" name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchPerBox" value="'+branch_per_box+'" /> '+
						   '<input type=hidden class="perBranch" name="b2bReturnsItems['+b2bReturnsItemIndex+'].perBranch" value="'+per_branch+'" />';
				}else{
					return '<div class="nums-input ov">'+
			            	'<input type="text" kid="branch" class="t branchQuantity" itemId="'+item.id+'" name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchQuantity" value="'+branchQuantity+'" org="'+branchQuantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" readonly="readonly" />'+
// 			            	'<input type=hidden class="branchPerBox" value="'+item.branch_per_box+'" /> '+
							'<input type=hidden class="branchBox" value="'+item.branch_box+'" />'+
							'<input type=hidden class="branchPerBox" name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchPerBox" value="'+item.branch_per_box+'" /> '+
							'<input type=hidden class="perBranch" name="b2bReturnsItems['+b2bReturnsItemIndex+'].perBranch" value="'+item.per_branch+'" />'+
			        	'</div>';
				}
			}else{
				var str = 'class="text branchQuantity"';
				if(obj == undefined || item.parent!=null){
					str = 'class="text " parentId="'+item.parent+'"';
				}
				var branch_per_box='';
				var  per_branch='';
				if(item.branch_per_box==undefined || item.per_branch == undefined || item.branch_per_box==0 || item.per_branch == 0){
					branch_per_box=0;
					per_branch=0;
					return '-'+
					       '<input type="hidden" kid="branch" class="t branchQuantity"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchQuantity" value="0"  >'+
					       '<input type=hidden class="branchPerBox" name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchPerBox" value="'+branch_per_box+'" /> '+
						   '<input type=hidden class="perBranch" name="b2bReturnsItems['+b2bReturnsItemIndex+'].perBranch" value="'+per_branch+'" />';
				}else{
					return '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchQuantity" value="'+branchQuantity+'" org="'+branchQuantity+'" '+str+' min="0">'+
					'<input type=hidden class="branchBox" name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchBox" value="'+item.branch_box+'" />'+
					'<input type=hidden class="branchPerBox" name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchPerBox" value="'+item.branch_per_box+'" /> '+
					'<input type=hidden class="perBranch" name="b2bReturnsItems['+b2bReturnsItemIndex+'].perBranch" value="'+item.per_branch+'" />'
					+'<span class="qty-text">'+branchQuantity+'</span>';
				}
			}
			
		}},
		{ title:'${message("零散支数")}', name:'scattered_quantity' ,align:'center', width:50, renderer: function(val,item,rowIndex, obj){
			var scatteredQuantity=0;
			if(obj==undefined){
				scatteredQuantity = val;
			}
			if(item.branch_per_box==undefined || item.per_branch == undefined || item.branch_per_box==0 || item.per_branch == 0){
				return '-'+
				'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].scatteredQuantity" class="scatteredQuantity text" value="0" />';
			}
			var html='<span class="scatteredQuantityStr">'+scatteredQuantity+'</span>'+
				'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].scatteredQuantity" class="scatteredQuantity text" value="'+scatteredQuantity+'" />';
			return html;
		}},
		{ title:'${message("数量")}', name:'quantity', align:'center', width:110, renderer:function(val,item,rowIndex,obj){

			var quantity='';
			if(obj==undefined){
				quantity = val;
			}
			if(val != ""){
				quantity = val;
			}
			if( orderStatus==0 && (obj!=undefined || item.parent==null)){
				var str = '';
				if(obj==undefined || item.parent==null){
					str = 'itemId="'+item.id+'"'
				}
				return '<div class="nums-input ov">'+
							'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
			            	'<input type="text" kid="quantity" class="t quantity" itemId="'+item.id+'" name="b2bReturnsItems['+b2bReturnsItemIndex+'].quantity" value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" />'+
			            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
			        	'</div>';
			}
			if( (orderStatus==0) && (obj!=undefined || item.parent==null)){
				var str = '';
				if(obj==undefined || item.parent==null){
					str = 'itemId="'+item.id+'"'
				}
				return '<div class="nums-input ov">'+
			            	'<input type="text" kid="quantity" class="t quantity" itemId="'+item.id+'" name="b2bReturnsItems['+b2bReturnsItemIndex+'].quantity" value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" readonly="readonly" />'+
			        	'</div>';
			}else{
				var str = 'class="text quantity"';
				if(obj == undefined || item.parent!=null){
					str = 'class="text quantity"';
				}
				return '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].quantity" value="'+quantity+'" '+str+' min="0">'+
				 '<span class="quantity-text">'+quantity+'</span>';
			}
			
		}},
		{ title:'${message("实退箱数")}', name:'returned_box_quantity' ,align:'center', width:50, renderer: function(val,item,rowIndex, obj){
			var returnedBoxQuantity=0;
			if(obj==undefined){
				returnedBoxQuantity = val;
			}
			var html='<span class="returnedBoxQuantityStr">'+returnedBoxQuantity+'</span>'+
				'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].returnedBoxQuantity" class="returnedBoxQuantity text" value="'+returnedBoxQuantity+'" />';
			return html;
		}},
		{ title:'${message("实退支数")}', name:'returned_branch_quantity' ,align:'center', width:50, renderer: function(val,item,rowIndex, obj){
			var returnedBranchQuantity=0;
			if(obj==undefined){
				returnedBranchQuantity = val;
			}
			var html='<span class="returnedBranchQuantityStr">'+returnedBranchQuantity+'</span>'+
				'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].returnedBranchQuantity" class="returnedBranchQuantity text" value="'+returnedBranchQuantity+'" />';
			return html;
		}},
		{ title:'${message("实退数量")}', name:'returned_quantity' ,align:'center', renderer: function(val,item,rowIndex, obj){
			var returnedQuantity=0;
			if(obj==undefined){
				returnedQuantity = val;
			}
			var html='<span class="returnedQuantityStr">'+returnedQuantity+'</span>'+
				'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].returnedQuantity" class="returnedQuantity text" value="'+returnedQuantity+'" />';
			return html;
		}},
		{ title:'${message("单价")}', name:'price' ,align:'center', width:100,renderer:function(val,item,rowIndex,obj){
// 			if(orderStatus==0 
// 				&& (obj!=undefined || item.parent==null)){
// 					if((obj!=undefined && item.else==1)||(obj==undefined && item.product==null)){
// 						return '<div class="nums-input ov">'+
// 					            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice (this.nextSibling,event)">'+
// 					            	'<input type="text"  class="t price pprice"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].price" value="'+val+'" minData="0" oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >'+
// 					            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice (this.previousSibling,event)">'+
// 					        	'</div>';
// 					}
// 						return '<input type="hidden" step="0.01" min=0 name="b2bReturnsItems['+b2bReturnsItemIndex+'].price" class="price text pprice" value="'+val+'"  oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)"/><span class="red priceText">'+currency(val,true)+'</span>';
// 				}else if(obj==undefined && item.parent!=null){
// 					if(item.product==null){
// 						return '<div class="nums-input ov">'+
// 					            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice (this.nextSibling,event)">'+
// 					            	'<input type="text"  class="t price pprice"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].price" value="'+val+'" minData="0" oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >'+
// 					            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice (this.previousSibling,event)">'+
// 					        	'</div>';
// 					}
// 					return '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].price" class="price" value="'+val+'" />-';
// 				}else{
// 					if( orderStatus==0 && (obj!=undefined || item.parent==null)){
// 						return '<div class="nums-input ov">'+
// 		            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice (this.nextSibling,event)">'+
// 		            	'<input type="text"  class="t price text pprice"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].price" value="'+val+'" minData="0" oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >'+
// 		            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice (this.previousSibling,event)">'+
// 		        		'</div>';
// 					}
// 					return '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].price" class="price pprice" value="'+val+'" /><span class="red priceText">'+currency(val,true)+'</span>';
// 				}
			var price=item.member_price;
			if (price==undefined){
				price=item.price;
			}
			if(item.orderSn == ""){
				return '<div class="nums-input ov">'+
		            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
		            	'<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].price" class="price t" value="'+val+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)">' +
		            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
//						'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].saleOrgPrice" class="saleOrgPrice" value="'+item.sale_org_price+'" />'+
						   	'</div>';	
			}else{
				return val+'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].price" class="price t" value="'+val+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)">' ;
			}
		}},
		
		{ [#if flag!=1]hidden:true,[/#if]title:'平台结算价', align:'center',name:'',renderer: function(val,item,rowIndex){
			var sale_org_price=item.sale_org_price==null?'':item.sale_org_price;
			[#if editSaleOrgPrice==1]
			return '<div class="nums-input ov">'+
		    	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice (this.nextSibling,event)">'+
		    	'<input type="text"  class="t text saleOrgPrice"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].saleOrgPrice" value="'+sale_org_price+'" minData="0" oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >'+
		    	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice (this.previousSibling,event)">'+
		    	'</div>';
			[#else]
		    	return '<span class="text red">'+currency(item.sale_org_price,true)+'</span><input type="hidden"  class="t text saleOrgPrice"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].saleOrgPrice" value="'+sale_org_price+'" >'
//		                     			return '<sapn class="text red saleOrgPriceText">'+currency(sale_org_price,true)+'</span><input type="hidden" class="saleOrgPrice" name="orderItems['+stockInItemIndex+'].saleOrgPrice" value="'+sale_org_price+'" />'
			[/#if]
		}},
		{ [#if flag!=1]hidden:true,[/#if]title:'平台结算金额', align:'center',name:'',renderer: function(val,item,rowIndex){

			var sale_org_price=item.sale_org_price==null?'':item.sale_org_price;
			return '<sapn class="text red saprice">'+currency(item.quantity*sale_org_price,true)+'</span>'
		 }},
		 { title:'备注', align:'center',name:'memo',renderer: function(val,item,rowIndex){
				
				return '<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].memo" value="'+item.memo+'" id="memo"/>';
				
			}},
			{ title:'金额', align:'center',name:'',renderer: function(val,item,rowIndex){
				b2bReturnsItemIndex++;
				line_no++;
				return '<sapn class="text red trprice">'+currency(item.quantity*item.price,true)+'</span>'
			}},
		
// 		{ title:'退货数量', align:'center', width:100, renderer: function(val,item,rowIndex){
// 			if(item.quantity == null){
// 				qNum = 1;
// 			}else{
// 				qNum = item.quantity;
// 			}
// 			return '<div class="nums-input ov">'+
// 	            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
// 	            	'<input type="text"  class="t quantity"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].quantity" value="'+qNum+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
// 	            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
// 	        	'</div>';
// 		}},
// 		{ title:'${message("产品价格")}', align:'center',name:'price', renderer: function(val,item,rowIndex){
// 				return '<div class="nums-input ov">'+
//             	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
//             	'<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].price" class="price t" value="'+val+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)">'+
//             	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
//         	'</div>';	
// 		}},
	];

	$mmGrid = $('#table-m1').mmGrid({
		height:'auto',
        cols: cols0,
        checkCol:false,
        fullWidthRows: true,
        items:items,
        autoLoad: true,
        callback:function(){
         	countTotal();
         }
    });
	$addProduct.click(function(){
		//打开选择产品界面
		var $storeId = $(".storeId").val();
		 var sbuId = $(".sbuId").val();
		 if($storeId==""){
	            $.message_alert('请选择客户');
	        }
		 else{
		$addProduct.bindQueryBtn({
			type:'product',
			bindClick:false,
			title:'查询产品',
			url:'/product/product/selectProduct.jhtml?multi=2&storeId='+$(".storeId").val()+'&sbuId='+sbuId+'&isToOrder=true',
			callback:function(rows){
				if(rows.length>0){
					if($("input[name='orderId']").val()!=''){
							$("input[name='orderId']").val('');
							$("input[name='orderSn']").val('');
						}
					var error = '';
					for (var i = 0; i < rows.length;i++) {
						var idH = $(".productId_"+rows[i].id).length;
						if(idH > 0){
							$.message_alert('产品【'+rows[i].name+'】已添加');
							return false;
						}
					}
					for (var i = 0; i < rows.length;i++) {
						var row = rows[i];
						$mmGrid.addRow(rows,null,1);
						countTotal();
						
					}
					
				}
			}
		});	
		 }
	});
	
	$addOrderItem.click(function(){
		//打开选择订单明细界面
		$addProduct.bindQueryBtn({
			type:'orderItem',
			bindClick:false,
			title:'查询订单明细',
			url:'/aftersales/b2b_returns/select_shipping_item.jhtml?multi=2&orderId='+$("input.orderId").val()+'&'+$("input.itemIds").serialize(),
			callback:function(rows){
				if(rows.length>0){
					var error = '';
					for (var i = 0; i < rows.length;i++) {
						var idH = $(".orderItemId_"+rows[i].id).length;
						if(idH > 0){
							$.message_alert('产品【'+rows[i].name+'】已添加');
							return false;
						}
					}
					for (var i = 0; i < rows.length;i++) {
						var row = rows[i];
						$mmGrid.addRow(rows,null,2);
						countTotal();
					}
					
				}
			}
		});	
	});
	//订单明细页面弹出
			$findOrder.click(function(){
				var $storeId = $(".storeId").val();
				var sbuId = $(".sbuId").val();
				 if($storeId==""){
			            $.message_alert('请选择客户');
			        }
				 else{
				$findOrder.bindQueryBtn({
					type:'orderItem',
					bindClick:false,
					title:'参考订单',
					url:'/aftersales/b2b_returns/selectOrder.jhtml?multi=2&storeId='+$(".storeId").val()+'&sbuId='+sbuId,
					callback:function(rows){
						if(rows.length>0){
							var error = '';
							for (var i = 0; i < rows.length;i++) {
								var idH = $(".productId_"+rows[i].id).length;
								if(idH > 0){
									$.message_alert('产品【'+rows[i].name+'】已添加');
									return false;
								}
							}
							for (var i = 0; i < rows.length;i++) {
								var row = rows[i];
								$mmGrid.addRow(row,null,1);
								newCountTotal();
							}
							
						}
		
					}
				});	
				 }
			});	
	// 删除商品
	$("a.deleteProduct").live("click", function() {
		var $this = $(this);
		$.message_confirm('${message("您确定要删除吗？")}',function(){
				var tr = $this.closest("tr");
				var le = $("#addProductTable").find("tr").length;
				if(le == 2){
					$.message_alert('${message("至少保留一个明细")}');
				}else{
					tr.remove();
					countTotal();
					var line_number = 1;
					$("span.line_no").each(function(){
						$(this).html(line_number++);
					});
					line_no--;
				}
		});
	});

	$("input[name='image1']").single_upload({
		uploadSize:"source"
	});

	$("input[name='image2']").single_upload({
		uploadSize:"source"
	});

	$("input[name='image3']").single_upload({
		uploadSize:"source"
	});
	
	$("input.NonNegative").live("change",function(){
		if($(this).val()=="" || $(this).val()<0){
			$(this).val(0);
		}
	});
	// 表单验证
	$inputForm.validate({
		rules: {
			amount: "required",
			reason: "required",
			warehouseName:"required"
		},
		submitHandler:function(form){
		return false;
		}
		 
	});
	//查询客户
	$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'/member/store/select_store.jhtml?type=distributor&isMember=1',
		callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
					$("input.storeId").val(row.id);
					$("input.storeName").val(row.name);
					$(".saleOrgName").val(row.sale_org_name);
					$(".saleOrgId").val(row.sale_org_id);
					if($("input.orderId").val()!=''){
						$mmGrid.removeRow();
						$("input[name='orderId']").val('');
						$("input[name='orderSn']").val('');
						b2bReturnsItemIndex = 0;
					}
					
					$("input[name='storeName']").attr("value",row.name);
	                $("input[name='consignee']").attr("value",row.store_consignee);
	                $("input[name='consigneeMobile']").attr("value",row.store_mobile);
	                $("input[name='address']").attr("value",row.store_address);
	                $("input[name='zipCode']").attr("value",row.store_zip_code);
	               $("input[name='addressOutTradeNo']").attr("value",row.out_trade_no);
	               
	               var areaId = row.store_area;
	                if(areaId==null)areaId='';
	                var areaName = row.area_full_name;
	                if(areaName==null)areaName='';
	                var treePath = row.area_tree_path;
	                if(treePath==null)treePath='';
	                $("input[name='areaId']").val(areaId);
	        	    $("input[name='areaName']").val(areaName);
	                
	        	    
	        	    $(".warehouseId").val('');
        			$(".warehouseName").val('');
        			
           			
	                //可发货余额
					var id = rows.id;
					ajaxSubmit($(id),{
						method:'post',
						url:'/finance/balance/get_balance.jhtml',
						data:{storeId:row.id},
						callback:function(resultMsg) {
							var data = resultMsg.objx;
							$("#balance").text(currency(data,true)); 
							[#if hiddenAmount==0]
							$("#balance").text("***");
							[/#if]
							$("#storeBalance").val(data); 
						}
					})
				}
	    	}
	});
	
	//查询仓库
	$("#selectWarehouse").click(function(){
        var $saleOrgId = $(".saleOrgId").val();
        var sbuId = $(".sbuId").val();
        if($saleOrgId==""){
            $.message_alert('请选择机构');
        }else{
        	$("#selectWarehouse").bindQueryBtn({
                type:'warehouse',
                bindClick:false,
				title:'${message("查询仓库")}',
				url:'/stock/warehouse/select_warehouse.jhtml?saleOrgId='+$saleOrgId+'&sbuId='+sbuId,
				callback:function(rows){
	            		if(rows.length>0){
	            			var row = rows[0];
	            			$(".warehouseId").val(row.id);
	            			$(".warehouseName").val(row.name);
	            			$(".typeSystemDictId").val(row.type_system_dict_id);
	            			$(".typeSystemDictValue").html(row.type_system_dict_value);
	            			$("input[name='organizationId']").val(row.management_organization_id);
	            			$("#organizationId").text(row.management_organization_name);
	            			
	            			var ssId = $("input[name='storeId']").val();
		            			ajaxSubmit($(row.id),{
									method:'post',
									url:'/finance/balance/get_balance.jhtml',
									data:{storeId:ssId,organizationId:row.management_organization_id},
									callback:function(resultMsg) {
										var data = resultMsg.objx;
										$("#bal").text(currency(data,true)); 
										[#if hiddenAmount==0]
										$("#bal").text("***");
										[/#if]
										$("#storeBalance").val(data);
 										//授信
 										$("#credit").text(currency(data.credit_amount,true));
 										//余额
 										$("#yue").text(currency(data.balance-data.credit_amount,true))
									}
								})
	            			
	            			$mmGrid.removeRow();
	            			countTotal();
	            		}
				}
			});
        }
	})
	
	
	//打开选择地址界面
    $("#addReceiveAddress").click(function(){
        var storeId = $(".storeId").val();
        if(storeId==""){
            $.message_alert('请选择客户');
        }else{
        		select_store_address(storeId);
        }
    });
	
	//查询销售订单
		$("#selectOrder").bindQueryBtn({
			type:'order',
			title:'查询销售订单',
			url:'/b2b/order/select_order.jhtml?orderStatus=6',
			callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
					$("input.orderId").val(row.id);
					$("input.orderSn").val(row.sn);
					$("input[name='storeId']").val(row.stores);
					$("input.storeName").val(row.store_name);
					$mmGrid.load({orderId:row.id});
				}
	    	}
		});	
	
		//查询机构
		$("#selectSaleOrg").bindQueryBtn({
			type:'saleOrg',
			title:'${message("查询机构")}',
			url:'/basic/saleOrg/select_saleOrg.jhtml',
			callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
					$("input.saleOrgId").val(row.id);
					$("input.saleOrgName").val(row.name);
					if($("input.orderId").val()!=''){
						$mmGrid.removeRow();
						$("input[name='orderId']").val('');
						$("input[name='orderSn']").val('');
						$("input[name='name']").val('');
						$("input[name='mobile']").val('');
						b2bReturnsItemIndex = 0;
					}
				}
	    	}
		});
		
		 //打开选择业务员界面
	    $("#selectsaleman").bindQueryBtn({
			type:'saleman',
			title:'${message("查询认领人")}',
			url:'/member/store_member/select_saleman.jhtml'
		});
		
		$("#openArea").bindQueryBtn({
		type:'area',
		title:'${message("查询地区")}',
		url:'/basic/area/select_area.jhtml',
		callback:function(rows){
			if(rows.length>0){
				var $tr =$this.closest("tr");
				$(".areaId").val(rows[0].id);
				$(".areaName").val(rows[0].full_name);
				//$("input[name='address']").val('');
				$("input[name='zipCode']").val('');
				}
			}
		});	
		
		var orderFullLink_items = ${fullLink_json};
	var cols = [				
    	{ title:'${message("内容")}', name:'content' ,width:300,align:'center'},
		{ title:'${message("操作人")}', name:'operator_name',width:100 ,align:'center'},
		{ title:'${message("创建日期")}', name:'create_date' ,width:150 ,align:'center'}
	];
	$('#table-full').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        items:orderFullLink_items,
        checkCol: false,
        autoLoad: true
    });
	
	//附件初始化
	initGrid();
	
	var $delProductImage1 = $(".deleteAttachment");
	$delProductImage1.live("click", function() {
		var $this = $(this);
		$this.closest("tr").remove();
	});
	
	$("form").bindAttribute({
		isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			});
	    }
	 });
	
});
$.validator.addClassRules({
	productId: {
		required: true
	},
	quantity: {
		required: true,
		min:0
	},
	storeName: {
		required: true
	},
	warehouseName:{
		required: true
	}
});

function save(e){
	var $input = $("input.pprice");
	var str='您确定要保存吗？';
	var count = 0;
	var err=0;
	$input.each(function(){
		var p = $(this).val();
		var $tr=$(this).closest("tr");
		var branchQuantity=$tr.find(".branchQuantity").val();
		if(branchQuantity%1!=0){
			str='支数不允许是小数，必须为整数';
			err++;
			return false;
		}
		if(p == '0'){
			count++;
			
		}
	})
	if(err!=0){
		$.message_alert(str);
		return false;
	}
		var url = '/aftersales/b2b_returns/update.jhtml'
		if($("#inputForm").valid()){
		ajaxSubmit(e,{
			url: url,
			data:$("#inputForm").serialize(),
			method: "post",
			isConfirm:true,
			confirmText:str,
			callback:function(resultMsg){
				if(resultMsg.type!="success"){
					$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
						location.reload(true);
					})
					return false;
				}
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.reload(true);
				})
			 }
			})
		}
}

//审核
function check(e){
	var data = $("#inputForm").serialize();
	ajaxSubmit(e,{
	    url:"/aftersales/b2b_returns/check.jhtml",
	    method:"post",
	    data:data,
	    isConfirm:true,
	    confirmText:'您确定要审核吗？',
	    callback:function(resultMsg){
		    $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
	    }
    })
}
//入库
function stockIn(e){
	if($(".storeId").attr("value")==""){
		$.message_alert('请选择客户！');
	}else{
		var $content = '<iframe name="iframePager_asign_warehouse" src="/aftersales/b2b_returns/returns_info.jhtml?id=${b2bReturns.id}" width="100%" height="420px"><\/iframe>';
		var $dialog_win = $.dialog({
			title:"${message("查看入库明细")}",
			width:1200,
			height:508,
			content: $content,
			closeIconHide:true,
			ok: "确定",
			cancel: "取消",
			onOk:function(){
				var data = $("#inputForm").serialize();
				ajaxSubmit(e,{
				    url:"/aftersales/b2b_returns/stockIn.jhtml",
				    method:"post",
				    data:data,
				    callback:function(resultMsg){
					    $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
							location.reload(true);
						})
				    }
			    })
			},
		});
	}
}
function product_view(id){
	var storeId = $('.storeId').val();
	create_iframe('/product/product/content.jhtml?id='+id+'&storeId='+storeId);
}

//更换地址
function select_store_address(storeId){
 $("#addReceiveAddress").bindQueryBtn({
    	        type:'store',
    	        bindClick:false,
    	        title:'${message("更换地址")}',
    	        url:'/member/store/select_store_address.jhtml?storeId='+storeId,
    	        callback:function(rows){
    	            if(rows.length>0){
    	            	var row = rows[0];
    	                $("input[name='consignee']").attr("value",row.consignee);
    	                $("input[name='consigneeMobile']").attr("value",row.mobile);
    	                $("input[name='address']").attr("value",row.address);
    	                $("input[name='zipCode']").attr("value",row.zip_code);
    	                $("input[name='addressOutTradeNo']").attr("value",row.out_trade_no);
    	                $(".select_area").find(".fieldSet").empty();
    	                var areaId = row.area;
    	                if(areaId==null)areaId='';
    	                var areaName = row.area_full_name;
    	                if(areaName==null)areaName='';
    	                var treePath = row.tree_path;
    	                if(treePath==null)treePath='';
    	               // $(".select_area").find(".fieldSet").append('<input type="hidden" id="areaId" name="areaId"  value="'+areaId+'" treePath="'+treePath+'" />');
    	                //地区选择
    	               // $("#areaId").lSelect();
    	                $("input[name='areaId']").val(areaId);
    	                $("input[name='areaName']").val(areaName);
    	                
    	            }
    	        }   
    	    });
 }

//作废
function cancel(e){
	var data = $("#inputForm").serialize();
	ajaxSubmit(e,{
	    url:"/aftersales/b2b_returns/cancel.jhtml",
	    method:"post",
	    data:data,
	    isConfirm:true,
	    confirmText:'您确定要作废吗？',
	    callback:function(resultMsg){
		    $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
	    }
    })
}

function check_wf(e){
	var $this = $(e);
	var $form = $("#inputForm");
	if($form.valid()){
		$.message_confirm("您确定要审批流程吗？",function(){
			var url="/aftersales/b2b_returns/check_wf.jhtml?id="+${b2bReturns.id};
			var data = $form.serialize();
			
			ajaxSubmit(e,{
				 url: '/wf/wf_obj_config/get_config.jhtml?obj_type_id=96&objid=${b2bReturns.id}',
				 method: "post",
				 callback:function(resultMsg){
				 	var rows = resultMsg.objx;
				 		var str = '';
					 	for(var i=0;i<rows.length;i++){
					 		var row = rows[i];
					 		str+='<option value="'+row.id+'">'+row.wf_temp_name+'</option>';
					 	}
					 	var content = '<table class="input input-edit" style="width:100%">'
								+'<tbody><tr><th>流程模版</th>'
								+'<td>'
									+'<select class="text" id="objConfId">'
										+str
									+'</select>'
								+'</td>'
							+'</tr></tbody></table>';
						var $dialog_check = $.dialog({
							title:"${message("退货单审核")}",
							height:'135',
							content: content,
							onOk:function(){
								var objConfId = $("#objConfId").val();
								if(objConfId=='' || objConfId == null){
									$.message_alert("请选择流程模版");
									return false;
								}
// 								url = url+'&objConfId='+objConfId;
								
// 								var url="check_wf.jhtml";
								data = data+'&objConfId='+objConfId;
								
								ajaxSubmit($this,{
									 url: url,
									 method: "post",
									 data: data,
									 callback:function(resultMsg){
										$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
											reflush_wf();
										});
									 }
								})
								
							}
						});
				 	
				 }
			})
		});
	}
}

function selectInvoice(e){
	var storeId = $("input[name='storeId']").val();
	if(storeId==""){
		$.message_alert('请选择客户');
		return false;
	}
	$(e).bindQueryBtn({
		type:'supplier',
		bindClick:false,
		title:'${message("查询发票抬头")}',
		url:'/member/store/select_store_invoice.jhtml?storeId='+storeId,
		callback:function(rows){
			if(rows.length>0){
				$("input[name='invoiceTitle']").val(rows[0].invoice_title);
			}
		}
	});
}

//查看PDF
function check_pdf(e,id){
	ajaxSubmit(e,{
		url:"/aftersales/b2b_returns/checkPdf.jhtml",
		method:"post",
		data:{id:id},
		async: false,
		callback:function(resultMsg){
			window.open(resultMsg.content);
		}
	});
}
</script>
</head>
<body>
	<div class="pathh">
		 &nbsp;${message("查看退货单")}
	</div>
	<form id="inputForm" action="/aftersales/b2b_returns/update.jhtml" method="post" type="ajax" validate-type="validate">
		<input name="id" value="${b2bReturns.id}" type="hidden">
		<div class="tabContent">
			<table class="input input-edit">
				<tr>
					<th>
						${message("退货单号")}:
					</th>
					<td>
						${b2bReturns.sn}
					</td>
					<th>
						${message("ERP单号")}:
					</th>
					<td>
						${b2bReturns.erpSn}
					</td>
					<th><span class="requiredField">*</span>${message("客户")}:</th>
					<td>
						<span class="search" style="position:relative">
		    				<input class="storeId" type="hidden" name="storeId" value="${b2bReturns.store.id}" maxlength="200" btn-fun="clear">							
							<input class="text storeName" maxlength="200" type="text" value="${b2bReturns.store.name}" onkeyup="clearSelect(this)" readOnly/>
<!-- 							<input type="button" class="iconSearch" value="" id="selectStore">					 -->
						</span>
					</td>
					<th>
						${message("机构")}:
					</th>
					<td >
						<span class="search" style="position:relative">
					<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value="${b2bReturns.saleOrg.id}"/>
					<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" value="${b2bReturns.saleOrg.name}" readOnly/>
					<!--<input type="button" class="iconSearch" value="" id="selectSaleOrg">-->
					</span>
					</td>
					<!-- <th>${message("机构")}:</th>
					<td>
						<span class="search" style="position:relative">
						<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value="${b2bReturns.saleOrg.id}"/>
						<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" value="${b2bReturns.saleOrg.name}" readOnly/>
						<input type="button" class="iconSearch" value="" id="selectSaleOrg">
						</span>
					</td> -->
				</tr>
				<tr>
				<th>
					<span class="requiredField">*</span>${message("经营组织")}:
				</th>
				<td>
					<span id="organizationId">${b2bReturns.organization.name}</span>
					<input type="hidden" name="organizationId" value="${b2bReturns.organization.id}" class="text organizationId" />
				</td>
					<th><span class="requiredField">*</span>${message("仓库")}:</th>
            	<td>
            		<span class="search" style="position:relative">
					<input type="hidden" name="warehouseId" class="text warehouseId" btn-fun="clear" value="${b2bReturns.warehouse.id}"/>
					<input type="text" name="warehouseName" class="text warehouseName" maxlength="200" onkeyup="clearSelect(this)" value="${b2bReturns.warehouse.name}" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectWarehouse">
					</span>
            	</td>
                <th>${message("仓库类型")}:</th>
            	<td><span class="typeSystemDictValue">${b2bReturns.warehouse.typeSystemDict.value}</span>
            		<input type="hidden" name="typeSystemDictId" class="typeSystemDictId" value="${b2bReturns.warehouse.typeSystemDict.id}"/>
            	</td>
				     <th>
						${message("发票抬头")}:
					</th>
					<td>
						<span class="search" style="position:relative">
							<input name="invoiceTitle" class="text invoiceTitle" maxlength="200" type="text" value="${b2bReturns.invoiceTitle}">
							<input type="button" class="iconSearch" value="" onclick="selectInvoice(this)">
						</span>
					</td>
				</tr>
				<tr>
					<th>
						${message("退货人姓名")}:
					</th>
					<td >
						<input name="name" type="text" class="text" value="${b2bReturns.name}">
					</td>
					<th>
						${message("退货人电话")}:
					</th>
					<td>
						<input name="mobile" type="text" class="text" value="${b2bReturns.mobile}">
					</td>
					<th>
						${message("退货单状态")}:
					</th>
					<td>
						[#if b2bReturns.status == 0]
							<b class="red">${message("未审核")}</b>
                    	[#elseif b2bReturns.status == 1]
                    		<b class="green">${message("已审核")}</b>
                    	[#elseif b2bReturns.status == 2]
                    		<b class="green">${message("入库中")}</b>
                    	[#elseif b2bReturns.status == 3]
                    		<b class="green">${message("已入库")}</b>
                    	[#elseif b2bReturns.status == 4]
                    		<b class="green">${message("部分退货")}</b>
                    	[#elseif b2bReturns.status == 5]
                    		<b class="green">${message("完全退货")}</b>
                    	[#elseif b2bReturns.status == 6]
                    		<b class="red">${message("已作废")}</b>
						[/#if]
					</td>
					<th>${message("流程状态")}:</th>
				<td>
					[#if b2bReturns.wfState == "0"]${message("未启动")}[/#if]
        			[#if b2bReturns.wfState == "1"]${message("审核中")}[/#if]
        			[#if b2bReturns.wfState == "2"]${message("已完成")}[/#if]
        			[#if b2bReturns.wfState == "3"]${message("驳回")}[/#if]
				</td>
				</tr>
				 <tr>      	
				   <th>      
					${message("创建人")}:
				   </th>
				   <td>
					${b2bReturns.storeMember.name}
				   </td>
				     <th>      
					${message("ERP退货时间")}:
				   </th>
				   <td>
					${b2bReturns.erpDate}
				   </td>
					<th>
						${message("ERP发运备注")}:
					</th>
					<td >
						${b2bReturns.erpRemark}
					</td>
						
					  <th>
		             <span class="requiredField">*</span>${message("发运方式")}:
		 	 	 </th>
				<td>
		 		<select id="smethod" name="smethod" class="text">
						[#list sbuItems as sbuItem]
						<option value="${sbuItem.shippingMethod.value}"[#if b2bReturns.sbu.id==sbuItem.sbu.id] selected[/#if]>${sbuItem.shippingMethod.value}</option>
						[/#list]
					</select>
				</td> 	
					
		 	
				
				</tr>
				<tr>
				<th>${message("业务类型")}:</th>
            	<td>
        		<select id="businessTypeId" name="businessTypeId" class="text">
					[#list businessTypes as businessType]
					<option value="${businessType.id}"[#if order.businessType.id==businessType.id]selected[/#if]>${businessType.value}</option>
					[/#list]
				</select>
				</td>
				 <th>${message("Sbu")}:</th>
             <td>
            	<input type="hidden" name="sbuId" class="text sbuId" id="sbuId" btn-fun="clear" value="${b2bReturns.sbu.id}"/>
            	<span   id="sbuName">
            	 ${b2bReturns.sbu.name}
            	</span>
             </td>
				<!--  <th>
	              <span class="requiredField">*</span>${message("sbu")}:
    	  </th>
   		<td>
    		<select id="sbuId" name="sbuId" class="text">
					[#list sbus as sbu]
					<option value="${sbu.sbu.id}"[#if b2bReturns.sbu.id==sbu.sbu.id] selected[/#if]>${sbu.sbu.name}</option>
					[/#list]
				</select>
    	</td>-->
				</tr>
				<tr>    
					<th>
						${message("退货原因")}:
					</th>
					<td colspan="7">
						<textarea name="reason" class="text" id="reason">${b2bReturns.reason}</textarea>
					</td>
				</tr>
				   </table>
			
			<div class="title-style">${message("收货信息")}
				<div class="btns">
				[#if b2bReturns.status==0]
					<a href="javascript:;" id="addReceiveAddress" class="button">更换收货信息</a>
				[/#if]
			</div>
			</div>
           <table class="input input-edit">
            <tr class="border-L1">
            	<th><span class="requiredField">*</span>${message("收货人")}:</th>
                <td><input type="text" class="text" name="consignee" value="${b2bReturns.consignee}" btn-fun="clear" readOnly></td>
                <th><span class="requiredField">*</span>${message("收货人电话")}:</th>
                <td><input type="text" class="text" name="consigneeMobile" value="${b2bReturns.consigneeMobile}" btn-fun="clear" readOnly></td>
                <th>${message("收货地区邮编")}:</th>
                <td><input type="text" class="text" name="zipCode" value="${b2bReturns.zipCode}" btn-fun="clear" readOnly></td>
                <th>地址外部编码:</th>
    		<td>
    			<input type="text" class="text" name="addressOutTradeNo" value="${b2bReturns.addressOutTradeNo}"  btn-fun="clear" readonly="readonly" />
    		</td>
            </tr>
             <tr class="border-L1">
             	<th><span class="requiredField">*</span>${message("收货地区")}：</th>
                <td>
                	<span class="search" style="position:relative">
                    <input type="hidden" name="areaId" class="text areaId" value="${b2bReturns.area.id}" btn-fun="clear"/>
                    <input type="text" name="areaName" class="text areaName"  value="${b2bReturns.area.name}" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
<!--                     <input type="button" class="iconSearch"  id="openArea"> -->
                    </span>
                </td>
           		<th><span class="requiredField">*</span>${message("收货地址")}:	</th>
                <td colspan="3"><input type="text" class="text" name="address" value="${b2bReturns.address}" btn-fun="clear" readOnly></td>
            	<th></th><td></td>
            </tr>
            </table>	
             <div class="title-style">${message("客户余额")}
			</div>
           <table class="input input-edit">
           <tr>
	           <th>
	           		<span class="requiredField">*</span>${message("退款金额")}:
	           </th>
	           <td>
		           <div>
		           <input type="text"  class="text"  name="amount" value="${b2bReturns.amount}" minData="0" oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" readOnly>
		           </div>						
	           </td>
           </tr>
           <tr>
           		<th>${message("可用余额")}:</th>
                <td>
                	<span class="red" id="bal">${currency(balance,true)}</span>
                	<input type="hidden" name="storeBalance" id="storeBalance" value="${balance}" />
                </td>
                <th>${message("订单金额")}:</th>
				<td><span class="red" id="amount">${currency(order.amount, true)}</span></td>
                [#if flag==1]
	                <th>
	                	${message("订单结算金额")}:
	                </th>
	                <td>
	                	<span class="red" id="saleAmount">${currency(0, true)}</span>
	                </td>
				[#else]
				 	<th>${message("差额")}:</th>
				 	<td><span class="red" id="chae">${currency(chae, true)}</span></td>
				[/#if]
				 <th>${message("余额")}:</th>
				 <td><span class="red" id="yue">${currency(yue, true)}</span></td>
           	</tr>     
           	<tr>
	           	<th>${message("授信")}:</th>
	           	<td><span class="red" id="credit">${currency(credit, true)}</span></td>				
			</tr>
				
            </table>   
				<div class="title-style">
				${message("退货明细")}:
				<div class="btns">
				[#if flag==null]
					<a href="javascript:;" id="addProduct" class="button">选择产品</a>
					[/#if]
		    	</div>
					<div class="btns">
					[#if flag==null]
					<a href="javascript:;" id="findOrder" class="button">参考订单</a>
					[/#if]
					</div>
			</div>
			<table id="table-m1"></table>
				            <div class="title-style">${message("退货单汇总")}
			</div>
           <table class="input input-edit">
	           <tr>
	           		<th>${message("退货单箱数")}:</th>
	                <td><span id="totalBoxQuantity"></span></td>
	                <th>${message("退货单支数")}:</th>
	                <td><span id="totalBranchQuantity"></span></td>
	           		<th>${message("退货单数量")}:</th>
	                <td><span id="totalQuantity"></span></td>
	                [#--<th>${message("退货单重量")}:</th>
	                <td><span id="totalWeight"></span></td>--]
	           </tr>
				[#--<tr>
	            	<th>${message("退货单体积")}:</th>
	                <td><span id="totalVolume"></span></td>
	            </tr>--]
            </table>
		
		<div class="title-style">
			${message("全链路信息")}
		</div>
		<table id="table-full"></table>
			
			<div class="title-style">
			${message("附件信息")}
			<div class="btns">
				<a href="javascript:;" id="addAttach" class="button">添加附件</a>
		</div>
		</div>
		<table id="table-attach"></table>
				
		</div>	
		<div class="fixed-top">
			[#if flag == null]
			<a href="/aftersales/b2b_returns/add/${code}.jhtml?sbuId=${b2bReturns.sbu.id}" class="iconButton" id="addButton">
				<span class="addIcon">&nbsp;</span>${message("1001")}
			</a>
			[/#if]
			[#if b2bReturns.status == 0 || b2bReturns.status == 1]
			<input type="button" class="button cancleButton" value="${message("作废")}" onclick="cancel(this)" />
			[/#if]
			[#if flag==1 && b2bReturns.status == 0]
			[#if isCheckWf]
				[#if b2bReturns.wfId==null]
				<input type="button" class="button sureButton" value="${message("12501")}" onclick="check_wf(this)" />
				[/#if]
				[#else]
			<a href="javascript:void(0);" class="iconButton" id="shengheButton" onClick="check(this)"><span class="ico-shengheIcon">&nbsp;</span>${message("审核")}</a>
			[/#if]
			[/#if]
			[#if flag == 2 && b2bReturns.status == 1]
			<a href="javascript:void(0);" class="iconButton" id="shengheButton" onClick="stockIn(this)"><span class="ico-shengheIcon">&nbsp;</span>${message("入库")}</a>
			[/#if]
			[#if b2bReturns.status == 0]
			<input type="button" class="button sureButton" onClick="save(this)" value="${message("1013")}" />
			[/#if]
			[#if b2bReturns.status == 1 || b2bReturns.status == 4 || b2bReturns.status == 5]
			<input type="button" class="button pdfButton" value="${message("查看PDF")}" onclick="check_pdf(this,'${b2bReturns.id}')" />
			[/#if]
			<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
	</form>
	<div id="wf_area" style="width:100%"></div>
</body>
</html>