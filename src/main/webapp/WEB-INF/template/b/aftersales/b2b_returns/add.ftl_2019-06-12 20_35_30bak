<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8" />
    <title>${message("添加退货")}</title>
    <link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
    <link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
    <script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
    <script type="text/javascript" src="/resources/js/base/dialog.js"></script>
    <script type="text/javascript" src="/resources/js/base/request.js"></script>
    <script type="text/javascript" src="/resources/js/base/global.js"></script>
    <script type="text/javascript" src="/resources/js/base/file.js"></script>
    <script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
    <script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
    <script type="text/javascript" src="/resources/js/productCategory.js"></script>
    <script type="text/javascript">
        $.validator.addClassRules({
            productId: {
                required: true
            },
            quantity: {
                required: true,
                min:0
            },
            storeName: {
                required: true
            },
            warehouseName:{
                required: true
            },
            reason:{
                required: true
            },
            glDate:{
            	 required: true
            }
        });
        function editQty(t,e){
            if($(t).attr("kid")=="quantity"){//平方
                if(extractNumber(t,6,false,e)){
                    var $tr = $(t).closest("tr");
                    var branch_quantity=0;
                    var box_quantity=0;
                    var scattered=0;

                    var quantity=$(t).val();
                    var perBranch=$tr.find(".perBranch").val();  //每支单位数
                    var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
                    var perBox=$tr.find(".perBox").val();
                    var type = productDisc(branchPerBox,perBranch,perBox);
                    if(type==2){
                        box_quantity = modulo(quantity,perBox);
                    }
                    if(type==3){
                        branch_quantity = modulo(quantity,perBranch);
                    }
                    if(perBranch!=0 && branchPerBox!=0&&perBranch!=undefined&&branchPerBox!=undefined){
                        branch_quantity=quantity/perBranch;
                        box_quantity=parseInt(branch_quantity/branchPerBox);
                        scattered=(branch_quantity%branchPerBox).toFixed(6);
                    }

                    $tr.find(".boxQuantity").val(box_quantity);
                    $tr.find(".branchQuantity").val(branch_quantity);
                    $tr.find(".scatteredQuantityStr").html(scattered);
                    $tr.find(".scatteredQuantity").val(scattered);

                    countTotal($tr);
                    $(t).val(quantity);

                }
            }else{
                if(extractNumber(t,3,false,e)){
                    var $tr = $(t).closest("tr");

                    var branch_quantity=0;
                    var box_quantity=0;

                    if($(t).attr("kid")=="box"){//箱
                        $tr.find(".scatteredQuantityStr").html(0);
                        $tr.find(".scatteredQuantity").val(0);
                    }else if($(t).attr("kid")=="branch"){//支
                        var quantity=$(t).val();
                        var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
                        var box=parseInt(quantity/branchPerBox);
                        var scattered=quantity%branchPerBox;
                        $tr.find(".boxQuantity").val(box);
                        $tr.find(".scatteredQuantityStr").html(scattered);
                        $tr.find(".scatteredQuantity").val(scattered);
                    }else if($(t).attr("kid")=="quantity"){//平方
                        var quantity=$(t).val();
                        var perBranch=$tr.find(".perBranch").val();  //每支单位数
                        var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
                        var branch_quantity=quantity/perBranch;
                        var box_quantity=parseInt(branch_quantity/branchPerBox);
                        var scattered=branch_quantity%branchPerBox;
                        $tr.find(".boxQuantity").val(box_quantity);
                        $tr.find(".scatteredQuantityStr").html(scattered);
                        $tr.find(".scatteredQuantity").val(scattered);
                    }

                    countTotal($tr);
                }
            }
        }
        function editPrice(t,e){
            extractNumber(t,2,false,e);
        }

        function newCountTotal(){
            var totalBoxQuantity = 0;
            var totalBranchQuantity = 0;
            var $bInput = $("input.quantity");
            $bInput.each(function(){
                var $tr = $(this).closest("tr");
                var quantity=$(this).val();
                var branchPerBox=$tr.find(".branchPerBox").val();
                var perBranch=$tr.find(".perBranch").val();
                var scatteredQuantity=$tr.find(".scatteredQuantity").val();//零散支数
                var perBox = $tr.find(".perBox").val();
                var boxQuantity = $tr.find(".boxQuantity").val()
                var type = productDisc(branchPerBox,perBranch,perBox);
                if(type==2){
//	        	var bQuantity = $tr.find(".boxQuantity").val();
                    var a = accDiv(perBox,10);
                    quantity = accMul(boxQuantity,a);
                }
                if(type==3){
                    var branchQuantity = $tr.find(".branchQuantity").val();
                    if(branchQuantity!=undefined){
                        quantity = accMul(branchQuantity,perBranch);
                    }
                }
                if(type==1){
                    //quantity =
                    quantity = quantity;
                }
                if(type==0){
                    var branchQuantity=accMul(boxQuantity,branchPerBox);
                    branchQuantity=accAdd(branchQuantity,scatteredQuantity);
                    if(isNaN(branchQuantity)){
                        branchQuantity = 0;
                    }
                    totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
                    totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
                    $tr.find(".branchQuantity").val(branchQuantity);//支数
                    $tr.find(".branchQuantityStr").html(branchQuantity);
                    var quantity=accMul(branchQuantity,perBranch);
                    if(isNaN(quantity)){
                        quantity = 0;
                    }
                    $tr.find(".quantity").val(quantity);//数量
                    $tr.find(".quantityStr").html(quantity);
                }
                $tr.find(".quantityStr").html(quantity);
                $tr.find(".quantity").val(quantity);
            });
            $("#totalBoxQuantity").text(totalBoxQuantity);
            $("#totalBranchQuantity").text(totalBranchQuantity);

            var $input = $("input.quantity");
            var total = 0;
            var totalVolume = 0;
            var totalWeight = 0;
            var totalQuantity = 0;

            var b = $("#storeBalance").val();
            $input.each(function(){
                var $this = $(this);
                var $tr = $this.closest("tr");
                var $price_box = $tr.find(".price-box");
                var price;
                if($price_box.length==0 || $price_box.prop("checked")==false){
                    price = Number($tr.find("input.price").val());
                }else{
                    price = Number($tr.find("input.origMemberPrice ").val());
                }

                var volume=$tr.find(".lineVolume").val();
                var weight=$tr.find(".lineWeight").val();

                var volumeAmount=Number(accMul($this.val(),volume)).toFixed(6);
                var weightAmount=Number(accMul($this.val(),weight)).toFixed(6);

                var amount = Number($this.val())*price;

                totalQuantity=Number(accAdd(totalQuantity,Number($this.val()))).toFixed(6);

                if(isNaN(amount)){
                    amount = 0;
                }
                total = Number(total)+Number(currency(amount,false));
                $tr.find(".trprice").html(currency(amount,true));//订单行金额

                if(isNaN(volumeAmount)){
                    volumeAmount = 0;
                }
                totalVolume = accAdd(totalVolume,Number(volumeAmount).toFixed(6));
                $tr.find(".lineVolumeAmount").html(volumeAmount);//体积

                if(isNaN(weightAmount)){
                    weightAmount = 0;
                }
                totalWeight = accAdd(totalWeight,Number(weightAmount).toFixed(6));
                $tr.find(".lineWeightAmount").html(weightAmount);//重量
            });
            $("#total").text(currency(total,true));
            $("#totalVolume").text(totalVolume);
            $("#totalWeight").text(totalWeight);
            $("#totalQuantity").text(totalQuantity);
            $("input[name='amount']").val(currency(total,false));

            var storeBalance = $("#storeBalance").val()
            $("#chae").text(currency(storeBalance-total,true));



        }

        function countTotal(t){
            var totalBoxQuantity = 0;
            var totalBranchQuantity = 0;
            var $bInput = $("input.boxQuantity");
            $bInput.each(function(){
                var quantity = 0;
                var $tr = $(this).closest("tr");
                var isEqual = null;
                if(t!=undefined){
	                isEqual = (t.find(".line_no").text() == $tr.find(".line_no").text());                	
                }
                var boxQuantity=$(this).val();
                var branchPerBox=$tr.find(".branchPerBox").val();
                var perBranch=$tr.find(".perBranch").val();
                var scatteredQuantity=$tr.find(".scatteredQuantity").val();//零散支数
                var perBox = $tr.find(".perBox").val();
                var type = productDisc(branchPerBox,perBranch,perBox);
                if(isEqual==null){
	                if(type==2){
	                    var bQuantity = $tr.find(".boxQuantity").val();
	                    var a = accDiv(perBox,10);
	                    quantity = accMul(bQuantity,a);
	                }
	                if(type==3){
	                    var branchQuantity = $tr.find(".branchQuantity").val();
	                    if(branchQuantity!=undefined){
	                        quantity = accMul(branchQuantity,perBranch);
	                    }
	                }
	                if(type==1){
	                    quantity = $tr.find(".quantity").val();
	                }
	                if(type==0){
	                    var branchQuantity=accMul(boxQuantity,branchPerBox);
	                    branchQuantity=accAdd(branchQuantity,scatteredQuantity);
	                    if(isNaN(branchQuantity)){
	                        branchQuantity = 0;
	                    }
	                    totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
	                    totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
	                    $tr.find(".branchQuantity").val(branchQuantity);//支数
	                    $tr.find(".branchQuantityStr").html(branchQuantity);
	                    var quantity=accMul(branchQuantity,perBranch);
	                    if(isNaN(quantity)){
	                        quantity = 0;
	                    }
	                    $tr.find(".quantity").val(quantity);//数量
	                    $tr.find(".quantityStr").html(quantity);
	                }
	                $tr.find(".quantityStr").html(quantity);
	                $tr.find(".quantity").val(quantity);
                }else{
                	if(type==2&&isEqual){
	                    var bQuantity = $tr.find(".boxQuantity").val();
	                    var a = accDiv(perBox,10);
	                    quantity = accMul(bQuantity,a);
	                    $tr.find(".quantityStr").html(quantity);
		                $tr.find(".quantity").val(quantity);
	                }
	                if(type==3&&isEqual){
	                    var branchQuantity = $tr.find(".branchQuantity").val();
	                    if(branchQuantity!=undefined){
	                        quantity = accMul(branchQuantity,perBranch);
	                    }
	                    $tr.find(".quantityStr").html(quantity);
		                $tr.find(".quantity").val(quantity);
	                }
	                if(type==1&&isEqual){
	                    quantity = $tr.find(".quantity").val();
	                    $tr.find(".quantityStr").html(quantity);
		                $tr.find(".quantity").val(quantity);
	                }
	                if(type==0&&isEqual){
	                    var branchQuantity=accMul(boxQuantity,branchPerBox);
	                    branchQuantity=accAdd(branchQuantity,scatteredQuantity);
	                    if(isNaN(branchQuantity)){
	                        branchQuantity = 0;
	                    }
	                    totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
	                    totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
	                    $tr.find(".branchQuantity").val(branchQuantity);//支数
	                    $tr.find(".branchQuantityStr").html(branchQuantity);
	                    var quantity=accMul(branchQuantity,perBranch);
	                    if(isNaN(quantity)){
	                        quantity = 0;
	                    }
	                    $tr.find(".quantity").val(quantity);//数量
	                    $tr.find(".quantityStr").html(quantity);
	                }
                }
            });
            $("#totalBoxQuantity").text(totalBoxQuantity);
            $("#totalBranchQuantity").text(totalBranchQuantity);

            var $input = $("input.quantity");
            var total = 0;
            var totalVolume = 0;
            var totalWeight = 0;
            var totalQuantity = 0;

            var b = $("#storeBalance").val();
            $input.each(function(){
                var $this = $(this);
                var $tr = $this.closest("tr");
                var $price_box = $tr.find(".price-box");
                var price;
                if($price_box.length==0 || $price_box.prop("checked")==false){
                    price = Number($tr.find("input.price").val());
                }else{
                    price = Number($tr.find("input.origMemberPrice ").val());
                }

                var volume=$tr.find(".lineVolume").val();
                var weight=$tr.find(".lineWeight").val();

                var volumeAmount=Number(accMul($this.val(),volume)).toFixed(6);
                var weightAmount=Number(accMul($this.val(),weight)).toFixed(6);

                var amount = Number($this.val())*price;

                totalQuantity=Number(accAdd(totalQuantity,Number($this.val()))).toFixed(6);

                if(isNaN(amount)){
                    amount = 0;
                }
                total = Number(total)+Number(currency(amount,false));
                $tr.find(".trprice").html(currency(amount,true));//订单行金额

                if(isNaN(volumeAmount)){
                    volumeAmount = 0;
                }
                totalVolume = accAdd(totalVolume,Number(volumeAmount).toFixed(6));
                $tr.find(".lineVolumeAmount").html(volumeAmount);//体积

                if(isNaN(weightAmount)){
                    weightAmount = 0;
                }
                totalWeight = accAdd(totalWeight,Number(weightAmount).toFixed(6));
                $tr.find(".lineWeightAmount").html(weightAmount);//重量
            });
            $("#total").text(currency(total,true));
            $("#totalVolume").text(totalVolume);
            $("#totalWeight").text(totalWeight);
            $("#totalQuantity").text(totalQuantity);
            $("input[name='amount']").val(currency(total,false));

            var storeBalance = $("#storeBalance").val()
            $("#chae").text(currency(storeBalance-total,true));


        }
        
        
         <!-- 2019-05-24 冯旗 长宽算数量-->    
 function editNumber(t,e){
    
        if($(t).attr("kid")=="length"){
        if(extractNumber(t,2,false,e)){
        	 var $tr = $(t).closest("tr");
       		 var length=$(t).val();
       	 	 var width=$tr.find(".width").val();
         	 var quantity = accMul(length,width).toFixed(4);
         	 $tr.find(".quantity").val(quantity);
         	  $tr.find(".quantityStr").html(quantity);
       	 		countTotal($tr);
       	 }		
        }else if($(t).attr("kid")=="width"){
        if(extractNumber(t,2,false,e)){
       		 var $tr = $(t).closest("tr");
        	 var width=$(t).val();
        	  var length=$tr.find(".length").val();
                
             var quantity = accMul(length,width).toFixed(4);
             $tr.find(".quantity").val(quantity);
           	$tr.find(".quantityStr").html(quantity);
               countTotal($tr);
         }      
        
}
                
} 

        //金额查询
        function product_price(e){

            var $tr = $(e).closest("tr");
            var productGrades = $tr.find(".productGrades").val();
            var storeId = $("input[name='storeId']").val();
            var productId = $tr.find(".productId").val();
            var warehouseId = $("input[name='warehouseId']").val();//warehouseId
            var saleOrgId = $("input[name='saleOrgId']").val();//saleOrgId
            var productGrade = $tr.find(".productGrade option:selected").val();
            var sbuId = $("input[name='sbuId']").val();//sbuId
            var memberId = $(".memberRankId").val();// memberRankId

            ajaxSubmit(e,{
                method:'post',
                url:'/product/product_price_head/findProductPrice.jhtml',
                data:{storeId:storeId,productId:productId,warehouseId:warehouseId,saleOrgId:saleOrgId,
                    productGrade:productGrade,sbuId:sbuId,memberId:memberId},
                callback:function(resultMsg) {
                    var data = resultMsg.objx;
                    if (data !=null) {
                        if (isNaN(data.store_member_price)) {
                            $tr.find("#productGrade").val(productGrades);
                            $.message_alert("${message("请在价格表维护该产品价格")}");
                            return false;
                        }else {
                            $tr.find("input.price").val(data.store_member_price);
                            $tr.find(".price_span").val(currency(data.store_member_price,true));
                            $tr.find(".price_span").text(currency(data.store_member_price,true));
                            $tr.find(".productGrades").val(productGrade);
                        }
                    }else {
                        $tr.find("#productGrade").val(productGrades);
                        $.message_alert("${message("请在价格表维护该产品价格")}");
                        return false;
                        /*  $tr.find("input.price").val(0);
                            $tr.find(".price_span").val(currency(0,true));
                            $tr.find(".price_span").text(currency(0,true));*/
                    }
                    countTotal();
                }

            });

        }


        var line_no = 1;
        $().ready(function() {
            var $inputForm = $("#inputForm");
            var b2bReturnsItemIndex = 0;
            var $findOrder = $("#findOrder");
            var $addProduct = $("#addProduct");
            var grade={"1":"优等品","2":"二等品","3":"一等品","4":"无等级"};
            var cols = [
                { title:'${message("木种花色")}', align:'center',hidden:'true',name:'wood_type_or_color',renderer: function(val,item,rowIndex){
                        return val+'<input type="hidden" class="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].woodTypeOrColor" value="'+val+'">';
                    }},
                { title:'${message("行号")}', width:40, align:'center',renderer: function(val,item,rowIndex){
                        return '<span class="line_no">'+ line_no  +'</span>';
                    }},
                { title:'操作', align:'center',  width:60, renderer: function(val,item,rowIndex){
                        return '<a href="javascript:;" class="deleteProduct btn-delete">删除</a>';
                    }},
                { title:'参考订单号', align:'center',name:'orderSn',width:100,renderer: function(val,item,rowIndex){
                        return val+'<input type="hidden" class="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].sn" value="'+val+'">';
                    }},
                { title:'${message("12211")}', align:'center',name:'vonder_code', width:250},
                { title:'产品名称', align:'center',name:'name', hidden:'true',width:250, renderer: function(val,item,rowIndex){
                        var pid=item.product;
                        if(pid==undefined){
                            pid=item.id;
                        }
                        var itemId='';
                        if(item.item_id!=undefined){
                            itemId=item.item_id;
                        }
                        return '<a href="javascript:void(0);" onClick="product_view('+item.id+')" class="red">'+item.name+'</a>'+
                                '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].product.id" class="productId productId_'+pid+'" value="'+pid+'" id="productId">'+
                                '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].orderItem.id" value="'+itemId+'">';
                    }},
                { title:'工厂/供应商',[#if sbu.id !=3||isMember==1 ]hidden:true,[/#if] align:'center',name:'manufactory_name', width:250},
                { title:'供应商型号',[#if sbu.id !=3||isMember==1 ]hidden:true,[/#if] align:'center',name:'supplier', width:100},
                { title:'产品型号', align:'center',name:'model', width:100},
                { title:'${message("产品描述")}', name:'description' ,align:'center',width:400, renderer: function(val,item,rowIndex, obj){
                        return val;
                    }},
                { title:'${message("单位")}', align:'center',name:'unit'},
                { title:'${message("产品分类")}', name:'product_category_name' ,align:'center',width:150, renderer: function(val,item,rowIndex, obj){
                        return val;
                    }},
                { title:'${message("产品规格")}', name:'spec' ,align:'center', hidden:'true',width:110},
                { title:'${message("产品等级")}', name:'product_grade' ,align:'center', width:100, renderer: function(val,item,rowIndex, obj){
                        var str1 = '';
                        var str2 = '';
                        var str3 = '';
                        var str4 = '';
                        if(val==1)str1 = 'selected';
                        if(val==2)str2 = 'selected';
                        if(val==3)str3 = 'selected';
                        if(val==4)str4 = 'selected';
                        var html = '<select name="b2bReturnsItems['+b2bReturnsItemIndex+'].productGrade" onchange="product_price(this)" class="text productGrade" id="productGrade">'
                                +'<option value="1" '+str1+'>优等品</option>'
                                +'<option value="2" '+str2+'>二等品</option>'
                                +'<option value="3" '+str3+'>一等品</option>'
                                +'<option value="4" '+str4+'>无等级</option>'
                                +'<input type="hidden" value="'+ val +'" class="t productGrades"/>';
                        return html;
                    }},
                {hidden:'true', title:'${message("体积")}', name:'volume' ,align:'center', renderer: function(val,item,rowIndex, obj){
                        return '<span class="lineVolumeAmount"></span><input class="lineVolume" value="'+val+'" type="hidden" />';
                    }},
                { hidden:'true',title:'${message("重量")}', name:'weight' ,align:'center', renderer: function(val,item,rowIndex, obj){
                        return '<span class="lineWeightAmount"></span><input class="lineWeight" value="'+val+'" type="hidden" />';
                    }},
			
			{ title:'${message("退货箱数")}',[#if sbu.id ==3 ]hidden:true,[/#if] name:'box_quantity', align:'center', width:100, renderer:function(val,item,rowIndex,obj){
                    var type = productCategory(item.branch_per_box,item.per_branch,item.per_box,item.pb);
                    var quantity = 1;

                    if(obj==undefined){
                        quantity = val;
                    }
                    if(val != ""){
                        quantity = val;
                    }

                    if(type==1||type==3){
                        return '-'+
                                '<input type="hidden"  class="t boxQuantity" name="b2bReturnsItems['+b2bReturnsItemIndex+'].boxQuantity"  value="0" />';
                    }else{
                        var text = '<div class="lh20">'+
                                '<div class="nums-input ov">'+
                                '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
                                '<input type="text" kid="box" class="t boxQuantity"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].boxQuantity" value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
                                '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">';
                        if(item.pb!=undefined&&item.pb!=undefined){
                            text +='<input type="hidden"  class="perBox" value="'+item.pb+'">'
                        }
                        if(item.per_box!=0&&item.per_box!=undefined){
                            text +='<input type="hidden"  class="perBox" value="'+item.per_box+'">'
                        }
                        text += '</div>';
                        return text;
                    }
                }},
			{ title:'${message("支数")}',[#if sbu.id ==3 ]hidden:true,[/#if] name:'branch_quantity' ,align:'center', width:100, renderer: function(val,item,rowIndex, obj){
                    var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
                    var branchQuantity='';
                    if(obj==undefined){
                        branchQuantity = val;
                    }
                    if(val != ""){
                        branchQuantity = val;
                    }
                    var branch_per_box='';
                    var  per_branch='';

                    if(type==1||type==2){
                        return '-'+'<input type=hidden  name="b2bReturnsItems['+b2bReturnsItemIndex+'].perBranch" value="0" />';
                    }else{
                        var branchPerBox = item.branch_per_box==null?"":item.branch_per_box;
                        var text = '<div class="lh20">'+
                                '<div class="nums-input ov">'+
                                '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
                                '<input type="text" kid="branch" class="t branchQuantity"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchQuantity" value="'+branchQuantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
                                '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
                                '<input type=hidden class="branchPerBox" name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchPerBox" value="'+branchPerBox+'" /> '+
                                '<input type=hidden class="perBranch" name="b2bReturnsItems['+b2bReturnsItemIndex+'].perBranch" value="'+item.per_branch+'" />'+
                                '</div></div>';
                        return text;
                    }
                }},
			{ title:'${message("零散支数")}',[#if sbu.id ==3 ]hidden:true,[/#if] name:'scattered_quantity' ,align:'center', width:50, renderer: function(val,item,rowIndex, obj){
                    var type = productCategory(item.branch_per_box,item.per_branch,item.per_box);
                    var branchQuantity='';
                    if(obj==undefined){
                        branchQuantity = val;
                    }

                    if(type==1||type==2||type==3){
                        return '-'+'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].scatteredQuantity" value="0" />';
                    }
                    var html='<span class="scatteredQuantityStr">0</span>'+
                            '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].scatteredQuantity" class="scatteredQuantity text" value="0" />';
                    return html;
                }},
			{ title:'${message("宽 ")}',[#if sbu.id != 3]hidden:true,[/#if]  name:'length', align:'center', width:110, renderer:function(val,item,rowIndex,obj){
			
			        var sbu=${sbu.id}
			        var length=0;
               		 if(item.length!='' && item.length!=undefined){
                 		 length=item.length;
              		  }
               		 if(isNaN(length)){
                		 length=0;
             		   }
                	if(item.unit=='m2' && sbu==3 && item.orderSn =='' && item.product_category_name=='墙布'){
            		
            		  
            		   var hideStr = '';
   						return '<div class="lh20"><div class="priceDiv nums-input ov" '+hideStr+'>'+
						'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editNumber (this.nextSibling,event)">'+
						'<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].length" kid="length" class="length t"  value="'+length+'" minData="0" oninput="editNumber (this,event)" onpropertychange="editNumber(this,event)"/>'+
						'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editNumber (this.previousSibling,event)">'+
						'</div></div>';
					 }else if(item.unit=='m2' && sbu==3 && item.ordderSn !='' && item.product_category_name=='墙布' ){
				      return val+'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].length" class="length text" value="'+length+'" />';
				    
				    }else{
				     return '-'+'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].length" class="length text" value="'+length+'" />';
				    
				    }
			
			 
						
			}},
			{ title:'${message("高")}',[#if sbu.id != 3]hidden:true,[/#if]  name:'width', align:'center', width:110, renderer:function(val,item,rowIndex,obj){
			
			  		var sbu=${sbu.id}
			  		 var width=0;
			  		 if(item.width!='' && item.width!=undefined){
                  		width=item.width;
               			 }
               		 if(isNaN(width)){
               			  width=0;
                	}
                	if(item.unit=='m2' && sbu==3 && item.orderSn =='' && item.product_category_name=='墙布'){
            		 
            		  
            		   var hideStr = '';
   						return '<div class="lh20"><div class="priceDiv nums-input ov" '+hideStr+'>'+
						'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editNumber (this.nextSibling,event)">'+
						'<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].width" kid="width" class="width t"  value="'+width+'" minData="'+width+'" oninput="editNumber (this,event)" onpropertychange="editNumber(this,event)"/>'+
						'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editNumber (this.previousSibling,event)">'+
						'</div></div>';
					 }else if(item.unit=='m2' && sbu==3 && item.ordderSn !='' && item.product_category_name=='墙布'){
            		 return val+'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].width" class="width text" value="'+width+'" />';
					 }else{
				      return '-'+'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].width" class="width text" value="'+width+'" />';
				    
				    }
						
			}},
                { title:'${message("数量")}', name:'quantity', align:'center', width:110, renderer:function(val,item,rowIndex,obj){

                        var quantity='';
                        var width=0;
                	  	if(item.width!='' && item.width!=undefined){
                  			width=item.width;
               			 }
               			 if(isNaN(width)){
               			  width=0;
                		}
                		   var length=0;
              			  if(item.length!='' && item.length!=undefined){
                 			 length=item.length;
              			  }
               			 if(isNaN(length)){
               			  length=0;
              			  }
                        if(obj==undefined){
                            quantity = val;
                        }
                        else if(item.length!='' && item.width!=''){
                            quantity=length * width;
                        }
                        if(item.length!='' && item.width!='' && item.length>0 && item.width>0){
				 			 return '<sapn class="text  quantityStr">'+quantity+'</span>'+'<input type="hidden" kid="quantity" class="t quantity"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].quantity" value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >';
						}else{
                        var text = '<div class="lh20">'+
                                '<div class="nums-input ov">'+
                                '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
                                '<input type="text" kid="quantity" class="t quantity"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].quantity" value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
                                '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
                                '<input type="hidden" class="minPriceApplyQuantity" />'+
                                '<input type="hidden" class="maxPriceApplyQuantity" />'+
                                '</div></div>';
                        return text;
                        }
                    }},
// 			{ title:'退货数量', align:'center', width:100, renderer: function(val,item,rowIndex){
// 				var str1=1;
// 				var str2='';
// 				if(item.r_quantity!=undefined){
// 					str1=item.r_quantity;
// 					str2='maxData="'+item.r_quantity+'"';
// 				}
// 				return '<div class="nums-input ov">'+
// 	            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
// 	            	'<input type="text"  class="t quantity"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].quantity" value="'+str1+'" minData="0" '+str2+'  oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'+
// 	            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
// 	        	'</div>';
// 			}},
                { title:'${message("单价")}', align:'center',name:'member_price', renderer: function(val,item,rowIndex){
                        var sale_org_price=item.sale_org_price;
                        if(sale_org_price==null)sale_org_price='';
                        if(item.orderSn == ""){
                            return '<div class="nums-input ov">'+
                                    '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
                                    '<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].price" class="price t" value="'+val+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)">' +
                                    '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
                                    '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].saleOrgPrice" class="saleOrgPrice" value="'+item.sale_org_price+'" />'+
                                    '</div>';
                        }else{
                            return val+'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].price" class="price t" value="'+val+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)">'+
                                    '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].saleOrgPrice" class="saleOrgPrice" value="'+item.sale_org_price+'" />';
                        }
                    }},

                { title:'备注', align:'center',name:'',renderer: function(val,item,rowIndex){

                        return '<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].memo" value="" id="memo"/>'

                    }},{ title:'金额', align:'center',name:'',renderer: function(val,item,rowIndex){
                        b2bReturnsItemIndex++;
                        line_no++;;
                        return '<sapn class="text red trprice"></span>';
                    }},

            ];

            $mmGrid = $('#table-m1').mmGrid({
                height:'auto',
                cols: cols,
                checkCol:false,
                url:'/aftersales/b2b_returns/select_shipping_item_data.jhtml',
                fullWidthRows: true,
                autoLoad: true,
                callback:function(){
                    countTotal();
                    editNumber();
                }
            });
            $addProduct.click(function(){
                //打开选择产品界面
                var $storeId = $(".storeId").val();
                var sbuId = $(".sbuId").val();
                var memberRankId = $(".memberRankId").val();
                if(memberRankId == undefined){
                    memberRankId = "";
                }

                if($storeId==""){
                    $.message_alert('请选择客户');
                }
                else{
                    $addProduct.bindQueryBtn({
                        type:'product',
                        bindClick:false,
                        title:'查询产品',
                        url:'/product/product/selectProduct_returns.jhtml?multi=2&storeId='+$(".storeId").val()+'&sbuId='+sbuId+'&isToOrder=true'+'&memberRankId=' + memberRankId,
                        callback:function(rows){
                            if(rows.length>0){
                                var error = '';
                                /*for (var i = 0; i < rows.length;i++) {
                                    var idH = $(".productId_"+rows[i].id).length;
                                    if(idH > 0){
                                        $.message_alert('产品【'+rows[i].name+'】已添加');
                                        return false;
                                    }
                                }*/
                                for (var i = 0; i < rows.length;i++) {
                                    var row = rows[i];
                                    $mmGrid.addRow(row,null,1);
                                    countTotal();                              
                                }

                            }

                        }
                    });
                }
            });

            //订单明细页面弹出
            $findOrder.click(function(){
                var $storeId = $(".storeId").val();
                var sbuId = $(".sbuId").val();
                if($storeId==""){
                    $.message_alert('请选择客户');
                }
                else{
                    $findOrder.bindQueryBtn({
                        type:'orderItem',
                        bindClick:false,
                        title:'参考订单',
                        url:'/aftersales/b2b_returns/selectOrder.jhtml?multi=2&storeId='+$(".storeId").val()+'&sbuId='+sbuId,
                        callback:function(rows){
                            if(rows.length>0){
                                var error = '';
                                for (var i = 0; i < rows.length;i++) {
                                    var idH = $(".productId_"+rows[i].id).length;
                                    if(idH > 0){
                                        $.message_alert('产品【'+rows[i].name+'】已添加');
                                        return false;
                                    }
                                }
                                for (var i = 0; i < rows.length;i++) {
                                    var row = rows[i];
                                    $mmGrid.addRow(row,null,1);
                                    newCountTotal();
                                }

                            }

                        }
                    });
                }
            });
            // 删除商品
            $("a.deleteProduct").live("click", function() {
                var $this = $(this);
                $.message_confirm('${message("您确定要删除吗？")}',function(){
                    var tr = $this.closest("tr");
                    var le = $("#addProductTable").find("tr").length;
                    if(le == 2){
                        $.message_alert('${message("至少保留一个明细")}');
                    }else{
                        tr.remove();
                        countTotal();
                        var line_number = 1;
                        $("span.line_no").each(function(){
                            $(this).html(line_number++);
                        });
                        line_no--;
                    }
                });
            });

            $("input[name='image1']").single_upload({
                uploadSize:"source"
            });

            $("input[name='image2']").single_upload({
                uploadSize:"source"
            });

            $("input[name='image3']").single_upload({
                uploadSize:"source"
            });

            $("input.NonNegative").live("change",function(){
                if($(this).val()=="" || $(this).val()<0){
                    $(this).val(0);
                }
            });

            /**初始化订单附件*/
            var depositAttachIndex=0;
            var cols = [
                { title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex){
                        var url = item.url;
                        var fileObj = getfileObj(item.name);
                        /**设置隐藏值*/
                        var hideValues = {};
                        hideValues['b2bReturnsAttachs['+depositAttachIndex+'].url']=url;
                        hideValues['b2bReturnsAttachs['+depositAttachIndex+'].suffix']=fileObj.suffix;

                        return createFileStr({
                            url : url,
                            fileName : fileObj.file_name,
                            name : fileObj.name,
                            suffix : fileObj.suffix,
                            time : '',
                            textName:'b2bReturnsAttachs['+depositAttachIndex+'].name',
                            hideValues:hideValues
                        });
                    }},
                { title:'${message("备注")}', name:'memo' ,width:590 ,align:'center', renderer: function(val,item,rowIndex){
                        return '<div><textarea class="text" name="b2bReturnsAttachs['+depositAttachIndex+'].memo" >'+val+'</textarea></div>';
                    }},
                { title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
                        depositAttachIndex++;
                        return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
                    }}
            ];
            var $amGrid=$('#table-attach').mmGrid({
                fullWidthRows:true,
                height:'auto',
                cols: cols,
                checkCol: false,
                autoLoad: true
            });


            var $addAttach = $("#addAttach");
            var attachIdnex = 0;
            var option1 = {
                dataType: "json",
                uploadToFileServer:true,
                uploadSize: "fileurl",
                callback : function(data){
                    var date = new Date();
                    var year = date.getFullYear();
                    var month = date.getMonth()+1;
                    var day = date.getDate();
                    var time = year+'-'+month+'-'+day;
                    for(var i=0;i<data.length;i++){
                        var row = data[i].file_info;
                        $amGrid.addRow(row,null,1);
                    }

                }
            }
            $addAttach.file_upload(option1);

            var $deleteAttachment = $(".deleteAttachment");
            $deleteAttachment.live("click", function() {
                var $this = $(this);
                $this.closest("tr").remove();
            });

            $(".orderSn").change(function(){
                if($("#orderId").val()!=''){
                    $("#addProduct").hide();
                }else{
                    $("#addProduct").show();
                }
            });

            // 表单验证
            $inputForm.validate({
                rules: {
                    amount: "required",
                    storeId: "required"
                },
                submitHandler:function(form){
                    return false;
                }

            });
            //查询客户
            $("#selectStore").bindQueryBtn({
                type:'store',
                title:'${message("查询客户")}',
                url:'/member/store/select_store.jhtml?type=distributor&isMember=1',
                callback:function(rows){
                    if(rows.length>0){
                        var row = rows[0];
                        $("input.storeId").val(row.id);
                        $("input.storeName").val(row.name);
                        $(".saleOrgName").val(row.sale_org_name);
                        $(".saleOrgId").val(row.sale_org_id);
                        if($("input.orderId").val()!=''){
                            $mmGrid.removeRow();
                            $("input[name='orderId']").val('');
                            $("input[name='orderSn']").val('');
                            $("input[name='name']").val('');
                            $("input[name='mobile']").val('');
                            b2bReturnsItemIndex = 0;
                        }

                        $("input[name='storeName']").attr("value",row.name);
                        $("input[name='consignee']").attr("value",row.store_consignee);
                        $("input[name='consigneeMobile']").attr("value",row.store_mobile);
                        $("input[name='address']").attr("value",row.store_address);
                        $("input[name='zipCode']").attr("value",row.store_zip_code);
// 		               $("input[name='addressOutTradeNo']").attr("value",row.out_trade_no);

                        ajaxSubmit($(rows[0].id),{
                            method:'POST',
                            url:'/member/store/select_store_address_data.jhtml',
                            data:{storeId:rows[0].id,address:row.store_address},
                            callback:function(resultMsg) {
                                var data = $.parseJSON(resultMsg.content);//前端
                                for(var i=0;i<data.content.length;i++){
                                    var storeAddress = data.content[i];
                                    $("input[name='addressOutTradeNo']").attr("value",storeAddress.out_trade_no);

                                }

                            }
                        })

                        $("#businessTypeId option").each(function(){ //遍历全部option

                            if($(this).text() == row.business_type_value){
                                $(this).attr("selected",true);
                            }
                        });

                        var areaId = row.store_area;
                        if(areaId==null)areaId='';
                        var areaName = row.area_full_name;
                        if(areaName==null)areaName='';
                        var treePath = row.area_tree_path;
                        if(treePath==null)treePath='';
                        $("input[name='areaId']").val(areaId);
                        $("input[name='areaName']").val(areaName);


//		        	    $(".warehouseId").val('');
//            			$(".warehouseName").val('');

                        //可用余额
                        $("#bal").text(currency(0,true));
                        $("#storeBalance").val(0);
                        //授信
                        $("#credit").text(currency(0,true));
                        //差额
                        $("#chae").text(currency(0,true));
                        //余额
                        $("#yue").text(currency(0,true));

//    	                //可发货余额
//						var id = rows.id;
//						ajaxSubmit($(id),{
//							method:'post',
//							url:'/finance/balance/get_balance.jhtml',
//							data:{storeId:row.id},
//							callback:function(resultMsg) {
//								var data = resultMsg.objx;
//								//可用余额
//								$("#balance").text(currency(data.balance,true));
//								$("#storeBalance").val(data.balance);
//								//授信
//								$("#credit").text(currency(data.credit,true));
//								//差额
//								$("#chae").text(currency(data.chae,true));
//								//订单余额
//								$("#amount").text(currency(data.amount,true));
////								$("#balance").text(currency(data,true));
////								[#if hiddenAmount==0]
////								$("#balance").text("***");
////								[/#if]
////								$("#storeBalance").val(data);
//							}
//						})

                        // 查询客户与SBU的价格类型
                        ajaxSubmit($(rows[0].id),{
                            method:'POST',
                            url:'/basic/member_rank/select_store_sbu_data.jhtml',
                            data:{storeId:rows[0].id},
                            callback:function(resultMsg) {
                                var ymsbu = $('#sbuName').text();
                                var data = $.parseJSON(resultMsg.content);
                                if (data.length > 0){
                                    for(var i = 0; i < data.length; i++){
                                        // 根据客户的SBU来判断当前页面的SBU
                                        if(data[i].sbu_name == $.trim(ymsbu)){
                                            $("#memberRank1").show();
                                            $("#memberRank2").show();
                                            $(".memberRankId").val(data[i].memberRankId);
                                            $(".memberRankName").val(data[i].memberRank);
                                            break;
                                        } else {
                                            $(".memberRankId").val("");
                                            $(".memberRankName").val("");
                                        }
                                    }

                                } else {
                                    $(".memberRankId").val("");
                                    $(".memberRankName").val("");
                                    $("#memberRank1").hide();
                                    $("#memberRank2").hide();
                                }

                            }
                        });


                    }
                }

            });

            //查询仓库
            $("#selectWarehouse").click(function(){
                var $saleOrgId = $(".saleOrgId").val();
                var sbuId = $(".sbuId").val();
                if($saleOrgId==""){
                    $.message_alert('请选择机构');
                }else{
                    $("#selectWarehouse").bindQueryBtn({
                        type:'warehouse',
                        bindClick:false,
                        title:'${message("查询仓库")}',
                        url:'/stock/warehouse/select_warehouse.jhtml?saleOrgId='+$saleOrgId+'&sbuId='+sbuId,
                        callback:function(rows){
                            if(rows.length>0){
                                var row = rows[0];
                                $(".warehouseId").val(row.id);
                                $(".warehouseName").val(row.name);
                                $(".typeSystemDictId").val(row.type_system_dict_id);
                                $(".typeSystemDictValue").html(row.type_system_dict_value);
                                $("input[name='organizationId']").val(row.management_organization_id);
                                $("#organizationId").text(row.management_organization_name);

                                var ssId = $("input[name='storeId']").val();
                                var sbuId = $("#sbuId").val();
                                var saleOrgId = $("input[name='saleOrgId']").val();
                                ajaxSubmit($(row.id),{
                                    method:'post',
                                    url:'/finance/balance/get_balance.jhtml',
                                    data:{storeId:ssId,sbuId:sbuId,saleOrgId:saleOrgId,organizationId:row.management_organization_id},
                                    callback:function(resultMsg) {
                                        var data = resultMsg.objx;
                                        //可用余额
                                        if(data!=null){
                                            if(data.balance==undefined){
                                                $("#bal").text(currency(0,true));
                                                $("#storeBalance").val(0);
                                            }else{
                                                $("#bal").text(currency(data.balance,true));
                                                $("#storeBalance").val(data.balance);
                                            }


                                            //授信
                                            $("#credit").text(currency(data.credit_amount,true));
                                            //余额
                                            $("#yue").text(currency(data.balance-data.credit_amount,true));

                                        }else{
                                            $.message_alert('请在余额中维护！');
                                        }

                                    }
                                })

                                $mmGrid.removeRow();
                                countTotal();
                            }
                        }
                    });
                }
            })

            //打开选择地址界面
            $("#addReceiveAddress").click(function(){
                var storeId = $(".storeId").val();
                if(storeId==""){
                    $.message_alert('请选择客户');
                }else{
                    select_store_address(storeId);
                }
            });

            //查询销售订单
            $("#selectOrder").bindQueryBtn({
                type:'order',
                title:'查询销售订单',
                url:'/b2b/order/select_order.jhtml?orderStatus=6&shippingStatus=1&shippingStatus=2&isReturn=1',
                callback:function(rows){
                    if(rows.length>0){
                        var row = rows[0];
                        $("input.orderId").val(row.id);
                        $("input.orderSn").val(row.sn);
                        $("input[name='storeId']").val(row.stores);
                        $("input.storeName").val(row.store_name);
                        $("input[name='name']").val(row.consignee);
                        $("input[name='mobile']").val(row.mobile);
                        $("input[name='saleOrgId']").val(row.sale_org_id);
                        $("input[name='consignee']").val(row.consignee);
                        $("input[name='consigneeMobile']").val(row.phone);
                        $("input[name='zipCode']").val(row.zip_code);
                        $("input[name='areaId']").val(row.area);
                        $("input[name='areaName']").val(row.area_name);
                        $("input[name='address']").val(row.address);
                        $("span.saleOrgName").html(row.sale_org_name);
                        $("#addProduct").hide();
                        $mmGrid.load({orderId:row.id});


                    }
                }
            });

            //查询机构
            $("#selectSaleOrg").bindQueryBtn({
                type:'saleOrg',
                title:'${message("查询机构")}',
                url:'/basic/saleOrg/select_saleOrg.jhtml',
                callback:function(rows){
                    if(rows.length>0){
                        var row = rows[0];
                        $("input.saleOrgId").val(row.id);
                        $("input.saleOrgName").val(row.name);
                        if($("input.orderId").val()!=''){
                            $mmGrid.removeRow();
                            $("input[name='orderId']").val('');
                            $("input[name='orderSn']").val('');
                            $("input[name='name']").val('');
                            $("input[name='mobile']").val('');
                            b2bReturnsItemIndex = 0;
                        }
                    }
                }
            });

            //打开选择业务员界面
            $("#selectsaleman").bindQueryBtn({
                type:'saleman',
                title:'${message("查询认领人")}',
                url:'/member/store_member/select_saleman.jhtml'
            });

            $("#openArea").bindQueryBtn({
                type:'area',
                title:'${message("查询地区")}',
                url:'/basic/area/select_area.jhtml',
                callback:function(rows){
                    if(rows.length>0){
                        var $tr =$this.closest("tr");
                        $(".areaId").val(rows[0].id);
                        $(".areaName").val(rows[0].full_name);
                        //$("input[name='address']").val('');
                        $("input[name='zipCode']").val('');
                    }
                }
            });

            $("form").bindAttribute({
                isConfirm:true,
                callback: function(resultMsg){
                    $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                        location.href= '/aftersales/b2b_returns/view/${code}.jhtml?flag=${flag}&id='+resultMsg.objx;
                    });
                }
            });

            $("#selectMemberRank").click(function(){
                var saleOrgId = $(".saleOrgId").val();
                var sbuId = $(".sbuId").val();
                //查询价格等级
                $("#selectMemberRank").bindQueryBtn({
                    type:'memberRank',
                    bindClick:false,
                    title:'${message("查询价格等级")}',
                    url:'/basic/member_rank/select_memberRank_price.jhtml?saleOrgId='+ saleOrgId + '&sbuId=' + sbuId,
                    callback:function(rows){
                        if(rows.length>0){
                            var row = rows[0];
                            $(".memberRankId").val(row.id);
                            $(".memberRankName").val(row.name);
                        }
                    }
                });

            });

        });
        function product_view(id){
            var storeId = $('.storeId').val();
            create_iframe('/product/product/content.jhtml?id='+id+'&storeId='+storeId);
        }
        function orderclearSelect(e){
            if($(e).val()==""){
                $("#orderId").val('');
                $("#addProduct").show();
            }else{
                $("#addProduct").hide();
            }
        }
        function save(e){
            var $input = $("input.price");
            var str='您确定要保存吗？';
            var count = 0;
            var err=0;
            var saleOrgName = $("input[name='saleOrgName']").val();
            var organizationId =  $("input[name='organizationId']").val();
            var warehouseName = $("input[name='warehouseName']").val();
            $input.each(function(){
                var p = $(this).val();
                var g = $(this).closest("tr").find(".isGift").val();

                var $tr=$(this).closest("tr");
                var quantity=$tr.find(".quantity").val();
                var length = $tr.find(".length").val();
                var width = $tr.find(".width").val();
                var branchQuantity=$tr.find(".branchQuantity").val();
                var boxQuantity = $tr.find(".boxQuantity").val();
                if(branchQuantity%1!=0&&branchQuantity!=undefined){
                    str='支数不允许是小数，必须为整数';
                    err++;
                    return false;
                }
                if(boxQuantity%1!=0&&boxQuantity!=undefined){
                    str='箱数不允许是小数，必须为整数';
                    err++;
                    return false;
                }
                if(p == 0 && g == "false"){
                    count++;
                }
                 if(length!='' && length!=0){
              
                   if(length.toString().split(".")[1] ==undefined || length.toString().split(".")[1].length <2){
                     $.message_alert("订单明细长必须为两位小数，请补齐");
                      count++;
              			 return false;
                   }
                }
                
                 if(width!='' && width!=0){
                   if(width.toString().split(".")[1] ==undefined || width.toString().split(".")[1].length <2){
                     $.message_alert("订单明细宽必须为两位小数，请补齐");
                      count++;
               			 return false;
                  }
                }
            })
            if(err!=0){
                $.message_alert(str);
                return false;
            }
            if(saleOrgName==""){
                str ='机构为空，请检查'
                $.message_alert(str);
                return false;
            }

            var url = '/aftersales/b2b_returns/save.jhtml';
            var $form = $("#inputForm");
            if($form.valid()){
                ajaxSubmit(e,{
                    url: url,
                    data:$("#inputForm").serialize(),
                    method: "post",
                    isConfirm:true,
                    confirmText:str,
                    callback:function(resultMsg){
                        var orderId=resultMsg.objx;
                        if(resultMsg.type!="success"){
                            $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                                location.reload(true);
                            })
                            return false;
                        }
                        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                            location.href= '/aftersales/b2b_returns/view/${code}.jhtml?id='+resultMsg.objx;
                        })

                    }
                })
            }
        }

        //更换地址
        function select_store_address(storeId){
            $("#addReceiveAddress").bindQueryBtn({
                type:'store',
                bindClick:false,
                title:'${message("更换地址")}',
                url:'/member/store/select_store_address.jhtml?storeId='+storeId,
                callback:function(rows){
                    if(rows.length>0){
                        var row = rows[0];
                        $("input[name='consignee']").attr("value",row.consignee);
                        $("input[name='consigneeMobile']").attr("value",row.mobile);
                        $("input[name='address']").attr("value",row.address);
                        $("input[name='zipCode']").attr("value",row.zip_code);
                        $("input[name='addressOutTradeNo']").attr("value",row.out_trade_no);
                        $(".select_area").find(".fieldSet").empty();
                        var areaId = row.area;
                        if(areaId==null)areaId='';
                        var areaName = row.area_full_name;
                        if(areaName==null)areaName='';
                        var treePath = row.tree_path;
                        if(treePath==null)treePath='';
                        // $(".select_area").find(".fieldSet").append('<input type="hidden" id="areaId" name="areaId"  value="'+areaId+'" treePath="'+treePath+'" />');
                        //地区选择
                        // $("#areaId").lSelect();
                        $("input[name='areaId']").val(areaId);
                        $("input[name='areaName']").val(areaName);

                    }
                }
            });
        }

        function selectInvoice(e){
            var storeId = $("input[name='storeId']").val();
            if(storeId==""){
                $.message_alert('请选择客户');
                return false;
            }
            $(e).bindQueryBtn({
                type:'supplier',
                bindClick:false,
                title:'${message("查询发票抬头")}',
                url:'/member/store/select_store_invoice.jhtml?storeId='+storeId,
                callback:function(rows){
                    if(rows.length>0){
                        $("input[name='invoiceTitle']").val(rows[0].invoice_title);
                    }
                }
            });
        }
    </script>
</head>
<body>
<div class="pathh">
    &nbsp; ${message("添加退货单")}
</div>
<form id="inputForm" action="/aftersales/b2b_returns/save.jhtml" method="post" type="ajax" validate-type="validate">
    <div class="tabContent">
        <table class="input input-edit">
            <tr>
                <th>
				${message("退货单号")}:
                </th>
                <td>
                </td>
                <th><span class="requiredField">*</span>${message("客户")}:</th>
                <td>
						<span class="search" style="position:relative">
		    				<input class="storeId" type="hidden" name="storeId" value="${store.id }" maxlength="200" btn-fun="clear">
							<input class="text storeName" maxlength="200" type="text" value="${store.name}" onkeyup="clearSelect(this)" readOnly/>
							<input type="button" class="iconSearch" id="selectStore">
						</span>
                </td>
                <th>${message("机构")}:</th>
                <td>
					<span class="search" style="position:relative">
					<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value="[#if store??]${store.saleOrg.id}[#else]${saleOrg.id}[/#if]"/>
					<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" value="[#if store??]${store.saleOrg.name}[#else]${saleOrg.name}[/#if]" readOnly/>
                        <!--<input type="button" class="iconSearch" value="" id="selectSaleOrg">-->
					</span>
                </td>
                <th>
                    <span class="requiredField">*</span>${message("经营组织")}:
                </th>
                <td>
                    <span id="organizationId">${management_organization_name}</span>
                    <input type="hidden" name="organizationId" class="text organizationId" value="${management_organization_id}" />
                </td>
            </tr>
            <tr>
                <th><span class="requiredField">*</span>${message("仓库")}:</th>
                <td>
            		<span class="search" style="position:relative">
					<input type="hidden" name="warehouseId" class="text warehouseId" btn-fun="clear" value="${warehouseSbuId}"/>
					<input type="text" name="warehouseName" class="text warehouseName" maxlength="200" onkeyup="clearSelect(this)" value="${warehouseSbuName}" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectWarehouse">
					</span>
                </td>

                <th>${message("仓库类型")}:</th>
                <td><span class="typeSystemDictValue">${type_system_dict_value}</span><input type="hidden" name="typeSystemDictId" class="typeSystemDictId"  value="${type_system_dict_id}"/></td>
                <th>${message("退货单状态")}:</th>
                <td></td>
                <th>
				${message("退货人姓名")}:
                </th>
                <td >
                    <input name="name" type="text" class="text">
                </td>
            </tr>
            <tr>
                <th>
				${message("退货人电话")}:
                </th>
                <td>
                    <input name="mobile" type="text" class="text">
                </td>
                <th>
				${message("发票抬头")}:
                </th>
                <td>
						<span class="search" style="position:relative">
							<input name="invoiceTitle" class="text invoiceTitle" maxlength="200" type="text" value="">
							<input type="button" class="iconSearch" value="" onclick="selectInvoice(this)">
						</span>
                </td>
                <th>${message("业务类型")}:</th>
                <td>
                    <select id="businessTypeId" name="businessTypeId" class="text">
						[#list businessTypes as businessType]
                            <option value="${businessType.id}"[#if businessTypeIdj==businessType.id]selected[/#if]>${businessType.value}</option>
						[/#list]
                    </select>
                </td>
                <th>${message("Sbu")}:</th>
                <td>
                    <input type="hidden" name="sbuId" class="text sbuId" id="sbuId" btn-fun="clear" value="${sbu.id}"/>
                    <span   id="sbuName">
					${sbu.name}
                    </span>
                </td>
                <!--  <th>
		             <span class="requiredField">*</span>${message("sbu")}:
		 	  </th>
				<td>
		 		<select id="sbuId" name="sbuId" class="text">
						[#list sbus as sbu]
						<option value="${sbu.sbu.id}"[#if sbuIds==sbu.sbu.id] selected[/#if]>${sbu.sbu.name}</option>
						[/#list]
					</select>
		 	</td> -->
            </tr>
            <!-- <tr>
					<th>${message("业务员")}:</th>
	           		<td>
	            		<span class="search" style="position:relative">
		                    <input type="hidden" name="salemanId" class="text salemanId" value="" btn-fun="clear"/>
		                    <input type="text" name="salemanName" class="text salemanName"  value="" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
		                    <input type="button" class="iconSearch" value="" id="selectsaleman">
	                    </span>
                	</td>
				</tr> -->
            <tr>
                <th>
                    <span class="requiredField">*</span>${message("发运方式")}:
                </th>
                <td>
                    <select id="smethod" name="smethod" class="text">
						[#list sbuItems as sbuItem]
                            <option value="${sbuItem.shippingMethod.value}"[#if shippingMethodId==sbuItem.sbu.id] selected[/#if]>${sbuItem.shippingMethod.value}</option>
						[/#list]
                    </select>
                </td>

                [#if roles]
				<th><span id="memberRank1">${message("价格类型")}:</span></th>
				<td>
					<span id="memberRank2" class="search" style="position:relative">
						<input type="hidden" name="memberRankId" class="text memberRankId" id="memberRankId" btn-fun="clear" value="${memberRank.id}"/>
						<input class="text memberRankName" maxlength="200" type="text" name="memberRankName" value="${memberRank.name}" onkeyup="clearSelect(this)" readOnly/>
						<input type="button" class="iconSearch" value="" id="selectMemberRank">
						<div class="pupTitleName  memberRank"></div>
					</span>
                </td>
				[/#if]
				<th><span class="requiredField">*</span>${message("GL时间")}:</th>
				<td>
					<input id="startTime" name="glDate" class="text" value="${GLDate}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
					<input id="endTime"  class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear"/>
				</td>
                
                <th></th><td></td>


            </tr>
            <tr>
                <th>
                    <span class="requiredField">*</span>${message("退货原因")}:
                </th>
                <td colspan="7">
                    <textarea name="reason" class="text reason" maxlength="80" id="reason"></textarea>
                </td>
            </tr>
        </table>

        <div class="title-style">${message("收货信息")}
            <div class="btns">
                <a href="javascript:;" id="addReceiveAddress" class="button">更换收货信息</a>
            </div>
        </div>
        <table class="input input-edit">
            <tr class="border-L1">
                <th><span class="requiredField">*</span>${message("收货人")}:</th>
                <td><input type="text" class="text" name="consignee" value="" btn-fun="clear" readOnly></td>
                <th><span class="requiredField">*</span>${message("收货人电话")}:</th>
                <td><input type="text" class="text" name="consigneeMobile" value="" btn-fun="clear" readOnly></td>
                <th>${message("收货地区邮编")}:</th>
                <td><input type="text" class="text" name="zipCode" value="" btn-fun="clear" readOnly></td>
                <th>地址外部编码:</th>
                <td>
                    <input type="text" class="text" name="addressOutTradeNo" value=""  btn-fun="clear" readOnly/>
                </td>
            </tr>
            <tr class="border-L1">
                <th><span class="requiredField">*</span>${message("收货地区")}：</th>
                <td>
                	<span class="search" style="position:relative">
                    <input type="hidden" name="areaId" class="text areaId" value="" btn-fun="clear"/>
                    <input type="text" name="areaName" class="text areaName"  value="" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
                        <!--                     <input type="button" class="iconSearch" value="" id="openArea"> -->
                    </span>
                    <!--<span class="fieldSet">
						<input type="hidden" id="areaId" name="areaId"  value="${(store.area.id)!}" treePath="${(store.area.treePath)!}" />
					</span>
					 <input type="button" class="iconSearch" value="" id="addReceiveAddress">-->
                </td>
                <th><span class="requiredField">*</span>${message("收货地址")}:	</th>
                <td colspan="3"><input type="text" class="text" name="address" value="" btn-fun="clear" readOnly></td>
                <th></th><td></td>
            </tr>
        </table>
        <div class="title-style">${message("客户余额")}
        </div>
        <table class="input input-edit">
            <tr>
                <th><span class="requiredField">*</span>${message("退款金额")}:</th>
                <td id="amount">
                    <div>
                        <input type="text"  class="text"  name="amount" value="" minData="0" oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" readOnly>
                    </div>
                </td>
            </tr>
            <tr>
                <th>${message("可用余额")}:</th>
                <td>
                    <span class="red" id="bal">${currency(storeBalance,true)}</span>
                    <input type="hidden" name="storeBalance" id="storeBalance" value="${storeBalance}"/>
                </td>
                <th>${message("订单金额")}:</th>
                <td>
		           		<span class="red" id="total">
			        	[#if order!=null]
							${currency(order.amount, true)}
						[#else]
			        		￥0.00
						[/#if]
                        </span>
                </td>
                <th>${message("差额")}:</th>
                <td>
                    <span class="red" id="chae">${currency(chae,true)}</span>
                </td>
                <th>${message("余额")}:</th>
                <td>
                    <span class="red" id="yue">${currency(yue,true)}</span>
                </td>
                <th>${message("授信")}:</th>
                <td>
                    <span class="red" id="credit">${currency(credit,true)}</span>
                </td>
            </tr>
        </table>
        <div class="title-style">
		${message("退货明细")}:
            <div class="btns">
                <a href="javascript:;" id="addProduct" class="button">选择产品</a>
            </div>
            <div class="btns">
                <a href="javascript:;" id="findOrder" class="button">参考订单</a>
            </div>
        </div>
        <table id="table-m1"></table>

        <div class="title-style">${message("退货单汇总")}
        </div>
        <table class="input input-edit">
            <tr>
            <!-- 2019-05-16 冯旗 壁纸隐藏箱支，重量体积 -->
            [#if sbu.id!=3]
                <th>${message("退货单箱数")}:</th>
                <td><span id="totalBoxQuantity"></span></td>
                <th>${message("退货单支数")}:</th>
                <td><span id="totalBranchQuantity"></span></td>
            [/#if]
                <th>${message("退货单数量")}:</th>
                <td><span id="totalQuantity"></span></td>
			[#--<th>${message("退货单重量")}:</th>
            <td><span id="totalWeight"></span></td>--]
            </tr>
		[#--<tr>
            <th>${message("退货单体积")}:</th>
            <td><span id="totalVolume"></span></td>
        </tr>--]
        </table>

        <div class="title-style">
		${message("附件信息")}:
            <div class="btns">
                <a href="javascript:;" id="addAttach" class="button">添加附件</a>
            </div>
        </div>
        <table id="table-attach"></table>
    </div>
    <div class="fixed-top">
        <input type="button" class="button sureButton" value="${message("1013")}" onclick="save(this)" />
        <input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
    </div>
</form>
</body>
</html>