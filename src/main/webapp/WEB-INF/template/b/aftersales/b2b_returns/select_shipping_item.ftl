<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("订单查询")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
$().ready(function() {
	var order_sttuss = {'0':'未确认', '1':'已确认', '2':'已完成', '3':'已作废', '4':'已删除','5':'未审核','6':'已审核','7':'已保存','8':'已接受'};
	var shipping_sttuss = {'0':'未发货', '1':'部分发货', '2':'已发货', '3':'确认收货'};
	var payment_sttuss = {'0':'未支付', '1':'部分支付', '2':'完全支付'};
	var cols = [
		{ title:'${message("产品名称")}', name:'name' , align:'center'},
		{ title:'${message("产品编码")}', name:'vonder_code' ,align:'center'},
		{ title:'${message("产品型号")}', name:'model' , align:'center'},
		{ title:'${message("价格")}', name:'price' , align:'center',renderer: function(val){
			return '<span class="red">'+currency(val,true)+'</span>';		
		}},
		{ title:'${message("数量")}', name:'quantity' , align:'center'},
	];
	[#if multi==2]
		var multiSelect = true;
	[#else]
		var multiSelect = false;
	[/#if]
	$mmGrid = $('#table-m1').mmGrid({
        cols: cols,
        fullWidthRows:true,
        autoLoad: true,
        url: 'select_shipping_item_data.jhtml',
        lineRoot:"shipping_items",
        rowCursorPointer:true,
        checkByClickTd:true,
        multiSelect:multiSelect,
        method: 'post',
        params:function(){
        	return $("#listForm").serializeObject();
        },
        root: 'content',
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });

	$("input[name='ids']").live("click", function(){
		$("input[name='ids']:checkbox").attr("checked",false);
		$(this).attr("checked",true);
	});
	$(".obj").live("click", function(){
		$("input[name='ids']:checkbox").attr("checked",false);
		$(this).find("input[name='ids']:checkbox").attr("checked",true);
	});
});
</script>
</head>
<body style="min-width:0px;">
<form id="listForm" action="select_order.jhtml" method="get">
	<input class="hidden" name="paymentStatus" value="${paymentStatus}" />
	<input class="hidden" name="orderId" value="${orderId}" />
	[#list returnId as rid]<input class="hidden" name="returnId" value="${rid}" />[/#list]
	<div class="bar">
		<div class="buttonWrap">
		</div>
		<div id="searchDiv">
			<div id="search-content" >
			</div>
			<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
		</div>
	</div>
	<div class="table-responsive">
		<table id="table-m1"></table>
        <div id="body-paginator">
            <div id="paginator"></div>
        </div>
	</div>
</form>
<script type="text/javascript">
//子框方法 返回选中的物料的ids字符串
function childMethod(){
   return $mmGrid.selectedRows();
};
</script>
</body>
</html>