<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("退货列表")} </title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
function initWfStates(){
	wfStates = {};
	[#list wfStates as wfState]
		wfStates['${wfState}'] = '${message('22222222'+wfState)}';
	[/#list]
}
function add(){
	parent.change_tab(0,'/aftersales/b2b_returns/add/${code}.jhtml?sbuId=${sbuId}');
}
$().ready(function() {
	
	
	
	/**初始化多选的下拉框*/
	initMultipleSelect();
	initWfStates();
	var status = {'0':'<span class="green">${message("未审核")}</span>','1':'<span class="blue">${message("已审核")}</span>',
			'2':'<span class="green">${message("入库中")}</span>','3':'<span class="blue">${message("已入库")}</span>',
			'4':'<span class="green">${message("部分退货")}</span>','5':'<span class="blue">${message("完全退货")}</span>',
			'6':'<span class="red">${message("已作废")}</span>'};
	var cols = [
		{ title:'${message("退货单号")}', name:'sn' ,align:'center',renderer:function(val,item){
			[#if flag??]
				if(item.status!=0 && item.status!=2){
					return '<a href="javascript:void(0);" onclick="check_pdf(this,'+item.id+')" title="查看PDF"><img style="width: 20px;" src="/resources/images/ic_pdf.png"></a>&nbsp;<a href="javascript:void(0);" onClick="parent.change_tab(0,\'/aftersales/b2b_returns/view/${code}.jhtml?flag=${flag}&id='+item.id+'\')" class="red">'+val+'</a>';
				}
				else{
					return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'/aftersales/b2b_returns/view/${code}.jhtml?flag=${flag}&id='+item.id+'\')" class="red">'+val+'</a>';
				}
			[#else]
				if(item.status == 0) {
					return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'/aftersales/b2b_returns/view/${code}.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';
				}
				else if (item.status == 6){
					return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'/aftersales/b2b_returns/view/${code}.jhtml?flag=0&id='+item.id+'\')" class="red">'+val+'</a>';
				}
				else {
					return '<a href="javascript:void(0);" onclick="check_pdf(this,'+item.id+')" title="查看PDF"><img style="width: 20px;" src="/resources/images/ic_pdf.png"><a href="javascript:void(0);" onClick="parent.change_tab(0,\'/aftersales/b2b_returns/view/${code}.jhtml?flag=0&id='+item.id+'\')" class="red">'+val+'</a>';
				}
			[/#if]
		}},
		{ title:'${message("ERP单号")}', name:'erp_sn' ,align:'center' },
		{ title:'${message("客户")}', name:'store_name' ,align:'center' },
		{ title:'${message("机构")}', name:'sale_org_name' ,align:'center' },
		{ title:'${message("sbu")}', name:'sbu_name' ,align:'center' },
		{ title:'${message("退货状态")}', name:'status' ,align:'center',renderer:function(val){
			var result = status[val];
			if(result!=undefined)return result;
		}},
		{ title:'${message("流程状态")}', name:'' ,align:'center',renderer:function(val,item){
			var result = wfStates[item.wf_state];
			if(result!=undefined)return result;
		}},
		{ title:'${message("退款金额")}', name:'amount' ,align:'center',renderer:function(val){
			return '<span class="red">'+currency(val,true)+'</span>';
		}},
		{ title:'${message("实退款金额")}', name:'return_amount_item' ,align:'center',renderer:function(val){
			return '<span class="red">'+currency(val,true)+'</span>';
		}},
		{ title:'${message("退货人姓名")}', name:'name' ,align:'center' },
		{ title:'${message("退货人电话")}', name:'mobile' ,align:'center' },
		{ title:'${message("退货原因")}', name:'reason' ,align:'center' },
		//{ title:'${message("备注")}', name:'memo' ,align:'center' },
		{ title:'${message("创建日期")}', name:'create_date' ,align:'center' },
		{ title:'明细' ,align: 'center', cols: [
   		{ title:'${message("产品名称")}', name:'name' ,width:150, isLines:true, align:'center'},
   		{ title:'${message("产品型号")}', name:'model' ,width:80, isLines:true, align:'center'},
   		{ title:'${message("箱数")}', name:'box_quantity' ,width:60, isLines:true, align:'center'},
   		{ title:'${message("支数")}', name:'branch_quantity' ,width:60, isLines:true, align:'center'},
   		{ title:'${message("数量")}', name:'quantity' ,width:40, isLines:true, align:'center',renderer:function(val,item){
   			return '<input type="hidden" name="suliang" value="'+val+'">'+val;
   		}},
   		{ title:'${message("产品描述")}', name:'description' , isLines:true, align:'center'},
   		{ title:'${message("12211")}', name:'vonder_code' ,width:80, isLines:true, align:'center'},
   		{ title:'${message("单价")}', name:'price' ,width:70, isLines:true, align:'center', renderer: function(val,item){
   			if(item.parent == null){
   				[#if hiddenAmount==0]
   				return '<span class="red">***</span>';
   				[/#if]
   				return '<span class="red">'+currency(val,true)+'</span>';
   			}else{
   				return '-';
   			}
   		}},
   	{ title:'${message("回传数量")}', name:'returned_quantity' ,width:60, isLines:true, align:'center'},
       ]}
	];

	$mmGrid = $('#table-m1').mmGrid({
        cols: cols,
        fullWidthRows:true,
        autoLoad: true,
        url: '/aftersales/b2b_returns/list_data.jhtml',
        lineRoot:"returns_items",
        params:function(){
        	return $("#listForm").serializeObject();
        },
		plugins : [
            $('#paginator').mmPaginator()
        ]
    });
	
	$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'/member/store/select_store.jhtml?multi=2&isSelect=0',
		callback:function(rows){
			if(rows.length>0){
				var mhtml="";
				if($("input[name='storeName']").val() == null){
					var all= "";
				}else{
					var all= $("input[name='storeName']").val();
				}
				
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".storeId_"+rows[i].id).length;
					if(idH > 0){
						$.message_alert('客户【'+rows[i].name+'】已添加');
						return false;
					}
				}
				for (var i = 0; i < rows.length;i++) {
					all =all +','+ rows[i].name;
					mhtml = '<div><input name="storeId" class="text storeId storeId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newclosePro(this)"></i></div>';
					$(".store").append(mhtml);
				}
				$("input[name='storeName']").attr("value",all);
			}
		}
	});
	
	
	//查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml'
	});
	
});
function newclosePro(e){
	$(e).closest("div").remove();
	var allName2 = '';
	$(".store > div").each(function(){
		allName2 = allName2 +','+  $(this).find("p").html();
	})
	$("input[name='storeName']").attr("value",allName2)
}



//条件导出		    
function segmentedExport(e){
	var needConditions = true;//至少一个条件
	var page_url = '/aftersales/b2b_returns/to_condition_export.jhtml';//分页导出统计页面
	var url = '/aftersales/b2b_returns/condition_export.jhtml';//导出的方法
	conditions_export(e,{needConditions:needConditions,page_url:page_url,url:url});
}

//选择导出
function exportExcel(t){
	var param = $mmGrid.serializeSelectedIds();//参数
	var tip = '${message("请选择导出的退货单！")}';//提示
	var url = '/aftersales/b2b_returns/selected_export.jhtml';//导出的方法
	select_export(t,{tip:tip, param:param, url:url});
}

//查看PDF
function check_pdf(e,id){
	ajaxSubmit(e,{
		url:"/aftersales/b2b_returns/checkPdf.jhtml",
		method:"post",
		data:{id:id},
		async: false,
		callback:function(resultMsg){
			window.open(resultMsg.content);
		}
	});
}
	
</script>
</head>
<body>
	<form id="listForm" action="/aftersales/b2b_returns/list/${code}.jhtml" method="get">
		<input type="hidden" name="flag" value="${flag}">
			<div class="bar">
				<div class="buttonWrap">
				<div class="flag-wrap flagImp-wrap">
					<a href="javascript:void(0);" class="iconButton" id="export1Button">
						<span class="impIcon">&nbsp;</span>导入导出
					</a>
					<ul class="flag-list">
						<li><a href="javascript:void(0)" onclick="exportExcel(this)"><i class="flag-imp02"></i>${message("选择导出")}</a></li>
						<li><a href="javascript:void(0)" onclick="segmentedExport(this)"><i class="flag-imp02"></i>${message("条件导出")}</a></li>
					</ul>
				</div>
	        [#if flag != 1 && flag != 2]
				<a href="javascript:add();" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
			[/#if]
			</div>
			<div id="searchDiv">
        	<div id="search-content" >
        		<dl><dd><input type="hidden" name="sbuId" value="${sbuId}"></dd></dl> 
        		<dl>
        			<dt><p>${message("退货单号")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="sn" value =""  btn-fun="clear" />
        			</dd>
        		</dl>
        		<dl>
        			<dt><p>${message("ERP单号")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="erpSn" value =""  btn-fun="clear" />
        			</dd>
        		</dl>
        		<dl>
					<dt><p>${message("客户名称")}:</p></dt>
    				<dd>
    					<span style="position:relative">
							<input class="text storeName" maxlength="200" type="text" name="storeName" value="" onkeyup="clearSelect(this)">
							<input type="button" class="iconSearch" value="" id="selectStore">
							<div class="pupTitleName  store"></div>
						</span>
    				</dd>
    			</dl>
        		<dl>
        			<dt><p>${message("退货状态")}:</p></dt>
        			<dd>
        				<div class="checkbox-style">
							<a href="javascript:void(0);" onclick="clearText(this)" class="deleteText close"></a>
					       	<input type="text" class="text pointer doStatus" value="[#if flag==1]${message("未审核")}[/#if]" autocomplete="off" />
				       		<div class="statusList cs-box" data-value="off">
				       			[#if flag==null || flag==1]<label><input  class="check js-iname" name="status" value="0" type="checkbox" [#if flag==1]checked[/#if]/>${message("未审核")}</label>[/#if]
				       			<label><input  class="check js-iname" name="status" value="1" type="checkbox"/>${message("已审核")}</label>
				       			<label><input  class="check js-iname" name="status" value="4" type="checkbox"/>${message("部分退货")}</label>
				       			<label><input  class="check js-iname" name="status" value="5" type="checkbox"/>${message("完全退货")}</label>
				       			<label><input  class="check js-iname" name="status" value="6" type="checkbox"/>${message("已作废")}</label>
				       			<!-- <label><input  class="check js-iname" name="status" value="2" type="checkbox"/>${message("入库中")}</label>
				       			<label><input  class="check js-iname" name="status" value="3" type="checkbox"/>${message("已入库")}</label> -->
				      		</div>
						</div>
        			</dd>
        		</dl>
        		<dl>
						  <dt><p>${message("流程状态")}:</p></dt>
						  <dd>
								<div class="checkbox-style">
								<a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
						       	<input type="text" class="text pointer doStatus" value="${message("未启动")}" autocomplete="off" />
						       		<div class="statusList cs-box" data-value="off">
						       			<label><input  class="check js-iname" name="wfState" value="0" type="checkbox"  checked />${message("未启动")}</label>
						       			<label><input  class="check js-iname" name="wfState" value="1" type="checkbox"  />${message("审核中")}</label>
						       			<label><input  class="check js-iname" name="wfState" value="2" type="checkbox"  />${message("已完成")}</label>
						       			<label><input  class="check js-iname" name="wfState" value="3" type="checkbox" />${message("驳回")}</label>
						       		</div>
						       	</div>
				    		</dd>
					</dl>
       			<dl>
       			<dt><p>${message("机构名称")}：</p></dt>
       			<dd >
       				<span class="search" style="position:relative">
						<input type="hidden" name="saleOrgId" class="text saleOrgId" id="saleOrgId" btn-fun="clear" />
						<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" id="saleOrgName" />
						<input type="button" class="iconSearch" value="" id="selectSaleOrg">
					</span>
       			</dd>
       			</dl>
        		<dl>
					<dt><p>${message("创建时间")}:</p></dt>
					<dd class="date-wrap">
						<input id="startTime" name="firstTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
						<div class="fl">--</div>
						<input id="endTime" name="lastTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
					</dd>
				</dl>
				
			</div>
			<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
		</div>
		</div>
			<div class="table-responsive">
				<table id="table-m1"></table>
	        	<div id="body-paginator" style="text-align:left;">
	            	<div id="paginator"></div>
	        	</div>
			</div>
		</form>
	</body>
</html>