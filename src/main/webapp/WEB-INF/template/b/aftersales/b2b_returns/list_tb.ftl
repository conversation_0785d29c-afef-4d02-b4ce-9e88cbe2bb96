<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript">

$().ready(function() {
	var h = $(window).height();
	$("iframe").height(h-$("#tab").outerHeight());

	var flag = '${flag}';
	var objid = '${objid}';
	var sbuId='${sbuId}';
	if(objid!=''){
		var index = 0;
		var url ='/aftersales/b2b_returns/view/${code}.jhtml?flag=${flag}&id='+objid;
		change_tab(index,url);
	}else if(flag=='1'){
		var index = 1;
		var url ='/aftersales/b2b_returns/list/${code}.jhtml?flag=1&menuId=${menuId}&sbuId='+sbuId;
		change_tab(index,url);
	}else if(flag=='2'){
		var index = 0;
		var url ='in_add.jhtml?flag=2';
		change_tab(index,url);
	}else{
		var index = 0;
		var url ='/aftersales/b2b_returns/add/${code}.jhtml?sbuId='+sbuId;
		change_tab(index,url);
	}
	
});

function change_tab(index,url,flag){
	$("#tab input").removeClass("current").eq(index).addClass("current");
	var $tabContent = $(".tabContent").hide();
	var $iframe = $tabContent.eq(index).show().find("iframe");
	if(flag==undefined){
		$iframe.attr("src",url);
	}else{
		 if($iframe.attr("src")==""){
			$iframe.attr("src",url);
		}
	}
}

</script>
</head>
<body style="overflow:hidden;">
	<div> 
		<ul id="tab" class="tab tab-first">
	        <li>
	            <input type="button" value="${message("常规")}" [#if flag!=1 && flag!=2]onclick="change_tab(0,'/aftersales/b2b_returns/add/${code}.jhtml',1)"[#elseif flag==2]onclick="change_tab(0,'in_add.jhtml',1)"[#else]onclick="change_tab(0,'',1)"[/#if]>
	        </li>
	        <li [#if objid!=null]style="display:none;"[/#if]>
	            <input type="button" value="${message("列表")}" [#if flag==2]onclick="change_tab(1,'in_list.jhtml?sbuId='${sbuId }'&menuId=${menuId}&flag='${flag}',1)"[#else]onclick="change_tab(1,'/aftersales/b2b_returns/list/${code}.jhtml?flag=${flag}&menuId=${menuId}&sbuId=${sbuId }',1)"[/#if]>
	        </li>
	    </ul>
	    <div class="tabContent" style="display:none;">
			<iframe src="" style="width:100%;"></iframe>
		</div>
		<div class="tabContent" style="display:none;" >
			<iframe src="" style="width:100%;"></iframe>
		</div>
	</div>
</body>
</html>