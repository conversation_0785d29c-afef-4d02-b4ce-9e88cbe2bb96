<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("查看退货")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/productCategory.js"></script>
<script type="text/javascript">

function productOrganization(){
	
	var warehouseId = $("input.warehouseId").val();
	$("input.productId").each(function(){
		var $this = $(this);
		var productId = $this.val();
		var $tr = $this.closest("tr");
		[#if organizationIsNoWarehouse == 0 ] 
			$tr.find(".productOrganizationName").text($("#organizationId").text());
			$tr.find("input.productOrganizationId").val($("input.organizationId").val());
		[#elseif organizationIsNoWarehouse == 1 ] 
			if(productId!=null && productId!='' && productId!='undefined'){
				$.ajax({
					url : '/product/product/findProductOrganizationList.jhtml',
	    			type : "post",
	    			data : {id:productId},
	    			success : function(data) {
	    				var content = JSON.parse(data.content);
	    				if(content.length!=0){
	    					var html = "";
	    					var organizationId = null;
	    					var b2bReturnsItem = $tr.find(".b2bReturnsItem").val();
	    					var orderItem = $tr.find(".orderItem").val();
	    					var shippingItem = $tr.find(".shippingItem").val();
	    					var productOrganizationId = $tr.find(".productOrganization option:selected").val();
	    					var productOrganizationText = $tr.find(".productOrganization option:selected").text();
	    					var isTrue = true;
	    					for(var i=0; i<content.length; i++){
	    						var isDefaults = content[i].is_defaults;
	    						if(b2bReturnsItem !='undefined' && b2bReturnsItem != null && b2bReturnsItem !=''){
	    							if(productOrganizationId !=content[i].organization){
	    								html += '<option value="'+ content[i].organization+'">'+content[i].organization_name+'</option>';
	    							}
	    						}else{
	    							if((orderItem !='undefined' && orderItem !=null && orderItem !='') 
		    								|| (shippingItem !='undefined' && shippingItem !=null && shippingItem !='') ){
		    							if(productOrganizationId !=content[i].organization){
		    								html += '<option value="'+ content[i].organization+'">'+content[i].organization_name+'</option>';
		    							}
		    						}else{
		    							html += '<option value="'+ content[i].organization+'">'+content[i].organization_name+'</option>';    
		    							if(productOrganizationId==content[i].organization){
		    								isTrue = false;
		    								organizationId = content[i].organization;
		    							}else{
			        						if(i==0){
			    								organizationId = content[i].organization;
			    							}else if(isDefaults==1 && isTrue){
			    								organizationId = content[i].organization;
			    							}
		    							}
		    						}
	    						}
	    					}
	    					
	    					if((b2bReturnsItem !='undefined' && b2bReturnsItem !=null && b2bReturnsItem !='') 
	    							|| (orderItem !='undefined' && orderItem !=null && orderItem !='') 
    								|| (shippingItem !='undefined' && shippingItem !=null && shippingItem !='')){
    							if(productOrganizationId != null && productOrganizationId != '' && productOrganizationId !='undefined'){
    								organizationId = productOrganizationId;
	    							html += '<option value="'+productOrganizationId+'">'+productOrganizationText+'</option>';
    							}else{
    								organizationId = '';
	    							html += '<option value="">请选择</option>';
    							}	
    						}
	    					$tr.find(".productOrganization").empty();
	    					$tr.find(".productOrganization").html(html);
    						$tr.find(".productOrganization option[value='"+organizationId+"']").attr("selected",true);
	    					[#if linkStock == 1 ] 
		    					if(organizationId!=null && organizationId!=''){
		    						//等级
		    						var productGrade = $tr.find(".productGrade").val();
		    						//编码
		    						var vonderCode = $tr.find(".vonderCode").text();
		    						//色号
		    						var colourNumber = $tr.find(".colourNumber").val();
		    						if(colourNumber=='undefined' || colourNumber.length == 0){
		    							colourNumber = "";
		    						}
		    						//含水率
		    						var moistureContent = $tr.find(".moistureContent").val();
		    						if(moistureContent=='undefined' || moistureContent.length == 0){
		    							moistureContent = "";
		    						}
		    						var params='&productGrade='+productGrade+'&vonderCode='+vonderCode+'&colourNumber='+colourNumber
		    		    			   +'&moistureContent='+moistureContent+'&organizationId='+organizationId+'&warehouseId='+warehouseId;
		    							params = params.substring(1,params.length);
	    							$.ajax({
	    								url:'/stock/stock/findViewStock.jhtml?'+params,
	    				    			type : "post",
	    				    			success : function(rows) {
	    				    				var data= $.parseJSON(rows.content);
    					                    if(data.length>0){
    					                        for (var i = 0; i < data.length;i++) {
    					                        	if(data[i].totalOnhandQuantity1 !=null && data[i].totalOnhandQuantity1 !=''){
    					                        		$tr.find(".onhandQuantity").text(data[i].totalOnhandQuantity1);
    					                        	}else{
    					                        		$tr.find(".onhandQuantity").text(0);
    					                        	}
    					                        	if(data[i].totalAttQuantity1 !=null && data[i].totalAttQuantity1 !=''){
    					                        		$tr.find(".attQuantity").text(data[i].totalAttQuantity1);
    					                        	}else{
    					                        		$tr.find(".attQuantity").text(0);
    					                        	} 
    					                    	}
    					                	}else{
    					                		$tr.find(".onhandQuantity").text(0);
    					                		$tr.find(".attQuantity").text(0);
    					                	}
	    				    			}
	    							})    
		    					}
	    					[/#if]
	    				}
	    			}
				})
			}
		[/#if]
	});
}



//审核完成后修改订单明细平台结算价
function apdateSaleOrgPrice(e){
	var data = $("#inputForm").serialize();
	ajaxSubmit(e,{
	    url:"/aftersales/b2b_returns/apdate_SaleOrgPrice.jhtml",
	    method:"post",
	    data:data,
	    isConfirm:true,
	    confirmText:'您确定要保存吗？',
	    callback:function(resultMsg){
		    $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
	    }
    })
}

function editQty(t,e){
	var pointN=3;
	if ($(t).attr("kid")=="quantity"){
		pointN=6;
	}
	if(extractNumber(t,pointN,false,e)){
		var qy=0;
		var $tr = $(t).closest("tr");
		if($(t).attr("kid")=="box"){
			$tr.find(".scatteredQuantityStr").html(0);
			$tr.find(".scatteredQuantity").val(0);
		}else if($(t).attr("kid")=="branch"){
			var quantity=$(t).val();
			var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
			var box=parseInt(quantity/branchPerBox);
			var scattered=quantity%branchPerBox;
			$tr.find(".boxQuantity").val(box);
			$tr.find(".scatteredQuantityStr").html(scattered);
			$tr.find(".scatteredQuantity").val(scattered);
		}else if($(t).attr("kid")=="quantity"){
			var branch_quantity=0;
			var box_quantity=0;
			var scattered=0;
			var quantity=$(t).val();
			var qy=$(t).val();
			var perBranch=$tr.find(".perBranch").val();  //每支单位数
			var branchPerBox=$tr.find(".branchPerBox").val();  //每箱支数
			var perBox=$tr.find(".perBox").val();
			var type = productDisc(branchPerBox,perBranch,perBox);
			if(perBranch!=0 && branchPerBox!=0){
				 branch_quantity=quantity/perBranch;
				 box_quantity=parseInt(branch_quantity/branchPerBox);
				 scattered=(branch_quantity%branchPerBox).toFixed(6);
			}
			 if(type==2){
                 perbox = accDiv(perBox,10);
                 box_quantity = modulo(quantity,perbox);
             }
             if(type==3){
             	branch_quantity = modulo(quantity,perBranch);
             }
			$tr.find(".boxQuantity").val(box_quantity);
			$tr.find(".branchQuantity").val(branch_quantity);
			$tr.find(".scatteredQuantityStr").html(scattered);
			$tr.find(".scatteredQuantity").val(scattered);
		}
		
		countTotal($tr);
		if($(t).attr("kid")=="quantity"){
			$(t).val(qy);
		}
		countVolume();
		var id = $(t).attr("itemId");
		var itemOrg = Number($(t).attr("org"));
		var qty = Number($(t).val());
		if(isNaN(qty)){
			qty = 0;
		}
		
		var $input = $("input.cq[parentId='"+id+"']");
		$input.each(function(){
			var $this = $(this);
			var org = Number($this.attr("org"));
			var value = qty*org/itemOrg;
			$this.val(value);
			$this.next(".qty-text").text(value);
		})
	}
}
function countVolume(){
	var $input = $("input.quantity");
	var totalVolume = 0;
	$input.each(function(){
		var $this = $(this);
		var $tr = $this.closest("tr");
		var volume = Number($tr.find("input.volume").val());
		var volumes = accMul(Number($this.val()),volume).toFixed(6);
		if(isNaN(volumes)){
			volumes = 0.00;
		}
		totalVolume = accAdd(totalVolume,volumes).toFixed(6);
	});
	$("span[name='volumeall']").text(totalVolume);
	$("input[name='volume']").val(totalVolume);
}
function editPrice(t,e){
	if(extractNumber(t,2,false,e)){
		countTotal();
	}
}

function newCountTotal(){
	var totalBoxQuantity = 0;
	var totalBranchQuantity = 0;
	var $bInput = $("input.quantity");
	$bInput.each(function(){
        var $tr = $(this).closest("tr");
        var quantity=$(this).val();
        var branchPerBox=$tr.find(".branchPerBox").val();
        var perBranch=$tr.find(".perBranch").val();
        var scatteredQuantity=$tr.find(".scatteredQuantity").val();//零散支数
        var perBox = $tr.find(".perBox").val();
        var type = productDisc(branchPerBox,perBranch,perBox);
        if(type==3){
        	var branchQuantity = $tr.find(".branchQuantity").val();
        	var quantity = accMul(perBranch,branchQuantity);
        	$tr.find(".quantity").val(quantity);//数量
        	$tr.find(".quantityStr").html(quantity);
        }
      	//辅料逻辑处理
        if(type==2){
        	var bQuantity = $tr.find(".boxQuantity").val();
        	var a = accDiv(perBox,10);
        	var quantity = accMul(bQuantity,a);
        	
        	$tr.find(".quantityStr").html(quantity);
        	$tr.find(".quantity").val(quantity);
        }else if(quantity!=''){
        	var branchQuantity=accDiv(quantity,perBranch)
        	var boxQuantity=accDiv(branchQuantity,branchPerBox);
        	branchQuantity=accAdd(branchQuantity,scatteredQuantity);
        	if(isNaN(branchQuantity)){
        		branchQuantity = 0;
    		}
        	totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
        	totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
        	$tr.find(".branchQuantity").val(branchQuantity);//支数
        	$tr.find(".branchQuantityStr").html(branchQuantity);
        	if(isNaN(quantity)){
        		quantity = 0;
    		}
        	$tr.find(".branchQuantity").val(branchQuantity);//数量
        	$tr.find(".boxQuantity").val(boxQuantity);//数量
        	$tr.find(".quantityStr").html(quantity);
        }
	});
	$("#totalBoxQuantity").text(totalBoxQuantity);
	$("#totalBranchQuantity").text(totalBranchQuantity);
	
	var $input = $("input.quantity");
	var total = 0;
	var totalVolume = 0;
	var totalWeight = 0;
	var totalQuantity = 0;
	
	var b = $("#storeBalance").val();
	$input.each(function(){
		var $this = $(this);
		var $tr = $this.closest("tr");
		var $price_box = $tr.find(".price-box");
		var price;
		if($price_box.length==0 || $price_box.prop("checked")==false){
			price = Number($tr.find("input.price").val());
		}else{
			price = Number($tr.find("input.origMemberPrice ").val());
		}
		
		var volume=$tr.find(".lineVolume").val();
		var weight=$tr.find(".lineWeight").val();
		
		var volumeAmount=Number(accMul($this.val(),volume)).toFixed(6);
		var weightAmount=Number(accMul($this.val(),weight)).toFixed(6);
		
		var amount = Number($this.val())*price;
		
		totalQuantity=Number(accAdd(totalQuantity,Number($this.val()))).toFixed(6);
		
		if(isNaN(amount)){
			amount = 0;
		}
		total = Number(total)+Number(currency(amount,false));
		$tr.find(".trprice").html(currency(amount,true));//订单行金额
		
		if(isNaN(volumeAmount)){
			volumeAmount = 0;
		}
		totalVolume = accAdd(totalVolume,Number(volumeAmount).toFixed(6)); 
		$tr.find(".lineVolumeAmount").html(volumeAmount);//体积
		
		if(isNaN(weightAmount)){
			weightAmount = 0;
		}
		totalWeight = accAdd(totalWeight,Number(weightAmount).toFixed(6)); 
		$tr.find(".lineWeightAmount").html(weightAmount);//重量
	});
	$("#total").text(currency(total,true));
	$("#totalVolume").text(totalVolume);
	$("#totalWeight").text(totalWeight);
	$("#totalQuantity").text(totalQuantity);
	$("input[name='amount']").val(currency(total,false));
	
	var storeBalance = $("#storeBalance").val()
	$("#chae").text(currency(storeBalance-total,true));
	
	

}
function countTotal(t){
	var totalBoxQuantity = 0;
	var totalBranchQuantity = 0;
	var $bInput = $("input.boxQuantity");
	$bInput.each(function(){
        var $tr = $(this).closest("tr");
        var isEqual = null;
        if(t!=undefined){
            isEqual = (t.find(".line_no").text() == $tr.find(".line_no").text());                	
        }
        var boxQuantity=$(this).val();
        var branchPerBox=$tr.find(".branchPerBox").val();
        var perBranch=$tr.find(".perBranch").val();
        var scatteredQuantity=$tr.find(".scatteredQuantity").val();//零散支数
        var perBox = $tr.find(".perBox").val();
        var type = productDisc(branchPerBox,perBranch,perBox);
        if(isEqual==null){
	        if(type==3){
	        	var branchQuantity = $tr.find(".branchQuantity").val();
	        	var quantity = accMul(perBranch,branchQuantity);
	        	$tr.find(".quantity").val(quantity);//数量
	        	$tr.find(".quantityStr").html(quantity);
	        }
	        //辅料逻辑处理
	        if(type==2){
	        	var bQuantity = $tr.find(".boxQuantity").val();
	        	var a = accDiv(perBox,10);
	        	var quantity = accMul(bQuantity,a);
	        	
	        	$tr.find(".quantityStr").html(quantity);
	        	$tr.find(".quantity").val(quantity);
	        }else if(type==0){
	        	var branchQuantity=accMul(boxQuantity,branchPerBox);
	        	branchQuantity=accAdd(branchQuantity,scatteredQuantity);
	        	if(isNaN(branchQuantity)){
	        		branchQuantity = 0;
	    		}
	        	totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
	        	totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
	        	$tr.find(".branchQuantity").val(branchQuantity);//支数
	        	$tr.find(".branchQuantityStr").html(branchQuantity);
	        	var quantity=accMul(branchQuantity,perBranch);
	        	if(isNaN(quantity)){
	        		quantity = 0;
	    		}
	        	$tr.find(".quantity").val(quantity);//数量
	        	$tr.find(".quantityStr").html(quantity);
	        }
        }else{
	        if(type==3&&isEqual){
	        	var branchQuantity = $tr.find(".branchQuantity").val();
	        	var quantity = accMul(perBranch,branchQuantity);
	        	$tr.find(".quantity").val(quantity);//数量
	        	$tr.find(".quantityStr").html(quantity);
	        }
	        //辅料逻辑处理
	        if(type==2&&isEqual){
	        	var bQuantity = $tr.find(".boxQuantity").val();
	        	var a = accDiv(perBox,10);
	        	var quantity = accMul(bQuantity,a);
	        	
	        	$tr.find(".quantityStr").html(quantity);
	        	$tr.find(".quantity").val(quantity);
	        }else if(type==0&&isEqual){
	        	var branchQuantity=accMul(boxQuantity,branchPerBox);
	        	branchQuantity=accAdd(branchQuantity,scatteredQuantity);
	        	if(isNaN(branchQuantity)){
	        		branchQuantity = 0;
	    		}
	        	totalBoxQuantity=accAdd(totalBoxQuantity, boxQuantity);
	        	totalBranchQuantity=accAdd(totalBranchQuantity,branchQuantity);
	        	$tr.find(".branchQuantity").val(branchQuantity);//支数
	        	$tr.find(".branchQuantityStr").html(branchQuantity);
	        	var quantity=accMul(branchQuantity,perBranch);
	        	if(isNaN(quantity)){
	        		quantity = 0;
	    		}
	        	$tr.find(".quantity").val(quantity);//数量
	        	$tr.find(".quantityStr").html(quantity);
	        }
        }
	});
	$("#totalBoxQuantity").text(totalBoxQuantity);
	$("#totalBranchQuantity").text(totalBranchQuantity);
	
	var $input = $("input.quantity");
	var total = 0;
	var orgTotal=0;
	var totalVolume = 0;
	var totalWeight = 0;
	var totalQuantity = 0;
	$input.each(function(){
		var $this = $(this);
		var $tr = $this.closest("tr");
		var price = Number($this.closest("tr").find("input.price").val());
		var saleOrgPrice = Number($this.closest("tr").find("input.saleOrgPrice").val());
		var quantity = $this.val();
		var amount = accMul(Number($this.val()),price).toFixed(2);
		if(isNaN(amount)){
			amount = 0;
		}
		total = accAdd(total,amount).toFixed(2);
		$tr.find(".trprice").html(currency(amount,true));
		
		var saleAmount = accMul(Number($this.val()),saleOrgPrice).toFixed(2);
		if(isNaN(saleAmount)){
			saleAmount = 0;
		}
		$tr.find(".saprice").html(currency(saleAmount,true));
		
		var volume=$tr.find(".lineVolume").val();
		var weight=$tr.find(".lineWeight").val();
		
		var volumeAmount=Number(accMul($this.val(),volume)).toFixed(6);
		var weightAmount=Number(accMul($this.val(),weight)).toFixed(6);
		
		totalQuantity=Number(accAdd(totalQuantity,Number($this.val()))).toFixed(6);
		
		if(isNaN(volumeAmount)){
			volumeAmount = 0;
		}
		orgTotal= accAdd(orgTotal,saleAmount).toFixed(2);
		totalVolume = accAdd(totalVolume,Number(volumeAmount).toFixed(6)); 
		$tr.find(".lineVolumeAmount").html(volumeAmount);//体积
		
		if(isNaN(weightAmount)){
			weightAmount = 0;
		}
		totalWeight = accAdd(totalWeight,Number(weightAmount).toFixed(6)); 
		$tr.find(".lineWeightAmount").html(weightAmount);//重量
		
	});
	$("#amount").text(currency(total,true));
	$("#totalVolume").text(totalVolume);
	$("#totalWeight").text(totalWeight);
	$("#totalQuantity").text(totalQuantity);
	$("input[name='amount']").val(currency(total,false));
	$("#saleAmount").text(currency(orgTotal,true));
	
	var storeBalance = $("#storeBalance").val();
	$("#chae").text(currency(storeBalance-total,true));
	
	
	[#if hiddenAmount==0]
	$("#amount").text("***");
	$("#freight").text("***");
	$(".payAmount").text("***");
	$("#balance").text("***");
	$("span.priceText").text("***");
	$(".trprice").text("***");
	$("span.saleOrgPriceText").text("***");
	$(".saprice").text("***");
	[/#if]
}


         <!-- 2019-05-24 冯旗 长宽算数量-->    
 function editNumber(t,e){
    
        if($(t).attr("kid")=="length"){
        if(extractNumber(t,6,false,e)){
        	 var $tr = $(t).closest("tr");
       		 var length=$(t).val();
       	 	 var width=$tr.find(".width").val();
         	 var quantity = accMul(length,width);
         	 $tr.find(".quantity").val(quantity);
         	  $tr.find(".quantity-text").html(quantity);
       	 		countTotal($tr);
       	 }		
        }else if($(t).attr("kid")=="width"){
       	 if(extractNumber(t,6,false,e)){
       		 var $tr = $(t).closest("tr");
        	 var width=$(t).val();
        	  var length=$tr.find(".length").val();
                
             var quantity = accMul(length,width);
             $tr.find(".quantity").val(quantity);
           	$tr.find(".quantity-text").html(quantity);
               countTotal($tr);
         }      
        
}
                
} 

//金额查询
function product_price(e){
    var $tr = $(e).closest("tr");
    var productGrades = $tr.find(".productGrades").val();
    var storeId = $(".storeId").val();
    var productId = $tr.find(".productId").val();
    var warehouseId = $(".warehouseId").val();//warehouseId
    var saleOrgId = $("input[name='saleOrgId']").val();//saleOrgId
    var productGrade = $tr.find(".productGrade option:selected").val();
    var sbuId = $("input[name='sbuId']").val();//sbuId
    var memberId = $(".memberRankId").val();// memberRankId
    ajaxSubmit(e,{
        method:'post',
        url:'/product/product_price_head/findProductPrice.jhtml',
        data:{storeId:storeId,productId:productId,warehouseId:warehouseId,saleOrgId:saleOrgId,
            productGrade:productGrade,sbuId:sbuId,memberId:memberId},
        callback:function(resultMsg) {
            var data = resultMsg.objx;
            if (data !=null) {
                if (isNaN(data.store_member_price)) {
                    $tr.find("#productGrade").val(productGrades);
                    $.message_alert("${message("请在价格表维护该产品价格")}");
                    return false;
                }else {
                    $tr.find("input.price").val(data.store_member_price);
                    $tr.find(".price_span").val(currency(data.store_member_price,true));
                    $tr.find(".price_span").text(currency(data.store_member_price,true));
                    $tr.find(".productGrades").val(productGrade);
                }
            }else {
               	$tr.find("#productGrade").val(productGrades);
                $.message_alert("${message("请在价格表维护该产品价格")}");
                return false;
            }
            countTotal();
            [#if linkStock == 1 ] 
				productOrganizationChange(e);
			[/#if]
        }
    });
}

function initGrid(){
	/**初始化订单附件*/
    var depositAttach_items = ${b2bReturnsAttach_json};
    var depositAttachIndex=0;
	var cols = [				
    	{ title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex,obj){
			if(obj==undefined){
				var url = item.url;
				var fileObj = getfileObj(item.file_name , item.name, item.suffix);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['b2bReturnsAttachs['+depositAttachIndex+'].id']=item.id;
				hideValues['b2bReturnsAttachs['+depositAttachIndex+'].url']=url;
				hideValues['b2bReturnsAttachs['+depositAttachIndex+'].suffix']=fileObj.suffix;
				
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : item.create_date,
					textName:'b2bReturnsAttachs['+depositAttachIndex+'].name',
					hideValues: hideValues
				});
			}else{
				var url = item.url;
				var fileObj = getfileObj(item.name);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['b2bReturnsAttachs['+depositAttachIndex+'].url']=url;
				hideValues['b2bReturnsAttachs['+depositAttachIndex+'].suffix']=fileObj.suffix;
				
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : '',
					textName:'b2bReturnsAttachs['+depositAttachIndex+'].name',
					hideValues:hideValues
				});
			}
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:590 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text" name="b2bReturnsAttachs['+depositAttachIndex+'].memo" >'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
			depositAttachIndex++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}},
	];
	var $amGrid=$('#table-attach').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        items:depositAttach_items,
        checkCol: false,
        autoLoad: true,
        callback:function(){
        	countVolume();
        	countVolume();
        }
    });
    
    var $addAttach = $("#addAttach");
	var attachIdnex = 0;
	var option1 = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row=data[i].file_info;
				$amGrid.addRow(row,null,1);
	        }
			
        }
    }
    $addAttach.file_upload(option1);
}

var line_no = 1;
$().ready(function() {
	
	[#if flag==1]
		$("#wf_area").load("/act/wf/wf.jhtml?wfid=${b2bReturns.wfId}");
	[/#if]
	$("#wf_area").load("/act/wf/wf.jhtml?wfid=${b2bReturns.wfId}");
	var $inputForm = $("#inputForm");
	var b2bReturnsItemIndex = 0;
	var $addProduct = $("#addProduct");
	var $findOrder = $("#findOrder");
	var $findShipping = $("#findShipping");
	var $addOrderItem = $("#addOrderItem");
	var $addSaleOrgs = $("#openSaleOrg");
	var items = ${jsonStr};
	var qNum = ""
		var orderStatus = '${v.status}';
	var cols0 = [	
		{ title:'${message("行号")}', width:40, align:'center',renderer: function(val,item,rowIndex){
			return '<span class="line_no">'+ line_no  +'</span><input type="hidden"  class="sign text" value="'+b2bReturnsItemIndex+'" />';
		}},
		{ title:'操作', align:'center', width:60, renderer: function(val,item,rowIndex){
			return '<a href="javascript:;" class="deleteProduct btn-delete" >删除</a>';
		}},
		{ title:'参考订单号', align:'center',name:'orderSn',width:100,renderer: function(val,item,rowIndex){
			return val+'<input type="hidden" class="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].sn" value="'+val+'">';
		}},
		{ title:'参考发货单号', align:'center',name:'shipping_sn',width:100,renderer: function(val,item,rowIndex){
                        return val+'<input type="hidden" class="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].shippingSn" value="'+val+'">';
        }},
        { title:'参考erp号', align:'center',name:'erp_sn',width:100,renderer: function(val,item,rowIndex){
                        return val+'<input type="hidden" class="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].erpSn" value="'+val+'">';
        }},
        { title:'发货仓', align:'center',name:'warehouse_name',width:100,renderer: function(val,item,rowIndex){
                        return val+'<input type="hidden" class="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].warehouseName" value="'+val+'">';
        }},
		{ title:'${message("产品编码")}', name:'vonder_code' ,align:'center',width:150, renderer: function(val,item,rowIndex, obj){
			return '<span class="vonderCode">'+val+'</span>';
		}},
		{ title:'${message("产品描述")}', name:'description' ,align:'center',width:400, renderer: function(val,item,rowIndex, obj){
			return val;
		}},
		{ title:'${message("单位")}', name:'unit' ,align:'center',width:80, renderer: function(val,item,rowIndex, obj){
			return val;
		}},
		{ title:'${message("产品分类")}', name:'product_category_name' ,align:'center',width:80},
	    {title:'${message("木种花色")}', align:'center',hidden:'true',name:'wood_type_or_color',renderer: function(val,item,rowIndex){
			return val+'<input type="hidden" class="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].woodTypeOrColor" value="'+val+'">';
		}}, 
		{ title:'产品名称', align:'center',name:'name',/* hidden:'true', */ width:250, renderer: function(val,item,rowIndex,obj){
			var pid=item.product;
			if(pid==undefined&&obj==1){
				pid=item.id;
			}
			if(obj==undefined){
				var orderItemId='';
				if(item.order_item!=null){
					orderItemId=item.order_item;
				}
				var shippingItemId=''
				if(item.shipping_item != null){
					shippingItemId=item.shipping_item;
				}
				return '<a href="javascript:void(0);" onClick="product_view('+item.id+')" class="red">'+
				'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].product.id" class="productId productId_'+pid+'" value="'+pid+'" id="productId">'+
				'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].id" class="b2bReturnsItem" value="'+item.id+'">'+item.name+'</a>'+
				'<input type="hidden" name="returnId" class="itemIds" value="'+item.id+'">'+
				'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].orderItem.id" class="orderItemId_'+orderItemId+'" value="'+orderItemId+'">'+
				'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].shippingItem.id" class="shippingItemId_'+shippingItemId+'" value="'+shippingItemId+'">';
			}else{
				var itemId='';
				if(item.item_id!=undefined){
					itemId=item.item_id;
				}
				var sItemId=''
                if(item.shipping_item!=undefined){
                    sItemId=item.shipping_item;
                }
				return '<a href="javascript:void(0);" onClick="product_view('+item.id+')" class="red">'+
				'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].product.id" id="productId" class="productId productId_'+pid+'" value="'+pid+'">'+item.name+'</a>'+
				'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].shippingItem.id" class="shippingItem shippingItemId_'+sItemId+'" value="'+sItemId+'">'+
				'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].orderItem.id" class="orderItem orderItemId_'+itemId+'" value="'+itemId+'">';
			}
		}},
		{ title:'${message("工厂/供应商")}',[#if b2bReturns.sbu.id !=3||isMember==1]hidden:true,[/#if] align:'center',name:'manufactory_name', width:250},
		{ title:'${message("供应商型号")}',[#if b2bReturns.sbu.id !=3||isMember==1]hidden:true,[/#if] align:'center',name:'supplier', width:100},
		{ title:'${message("产品型号")}', align:'center',name:'model', width:100},
		{ title:'${message("产品规格")}', name:'spec' ,hidden:'true',align:'center', width:110, renderer: function(val,item,rowIndex, obj){
			var html=val+'';
			if(item.else==1){
				html='<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].spec" class="pSpec productSpec_'+item.spec+' text" value="" />';
			}
			return html;
		}},				
		{ title:'${message("产品经营组织")}',width:220, align:'center', renderer: function(val,item,rowIndex,obj){
 			var html='';
 			[#if organizationIsNoWarehouse == 0 ] 
 				html +='<span class="productOrganizationName"></span>'+
 				       '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].productOrganization.id" class="productOrganizationId text" value="" />';
 			[#elseif organizationIsNoWarehouse == 1]  
 				html +='<select name="b2bReturnsItems['+b2bReturnsItemIndex+'].productOrganization.id" class="text productOrganization">';
	 				var productOrganizationName = '';
					if(item.product_organization_name != null && item.product_organization_name !=''){
						productOrganizationName = item.product_organization_name;
					}
					if(item.product_organization_id != null && item.product_organization_id !='' && item.product_organization_id !='undefined'){
						html+='<option value="'+item.product_organization_id+'" selected="selected" >'+productOrganizationName+'</option> ';
					}
 				html+='</select>';
 			[/#if]
    		return html;
 		}},
		{ title:'${message("产品等级")}', name:'level_Id' ,align:'center', width:100, renderer: function(val,item,rowIndex, obj){
			if( orderStatus==0 && (obj!=undefined || item.parent==null)){
				var str='selected="selected"';
				var html = '<input type="hidden" value="'+item.level_Id+'" class="t productGrades"/>';
     			    html+='<select name="b2bReturnsItems['+b2bReturnsItemIndex+'].productLevel.id" class="text productGrade" id="productGrade" onchange="product_price(this)">';
     				[#list productLevelList as products]
     				if(${products.id}==item.level_Id){
     					html+='<option value="${products.id}" '+str+' >${products.value}</option> ';
     				}else{
     					html+='<option value="${products.id}">${products.value}</option> ';
     				}
     				[/#list]
     				html+='</select>';
     			return html; 
			}
 			var result ='';
			[#list productLevelList as products]
				if(${products.id}==item.level_Id){
					result = ${products.value};
				}
			[/#list]
 			var html='<span class="text productGradeText">'+result+'</span>'+'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].productLevel.id" class=" text" value="'+item.level_Id+'" />';
 			return html;
		}},
		{ hidden:'true',title:'${message("体积")}', name:'volume' ,align:'center', renderer: function(val,item,rowIndex, obj){
			var html;
			var v = 0;
			if(obj==undefined){
				if(item.product==null){ 
					html=v;
				}else{
					if (val != '') {
						v = val;
					}
					html='<span class="lineVolumeAmount">'+v+'</span>'+
					'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].volume" class="volume lineVolume text" value="'+v+'" />';
				}
			}
			else{
				if(item.else==1){
					html = '<span class="lineVolumeAmount">'+v+'</span>';
				}else{
					if (val != '') {
						v = val;
					}
					html='<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].volume" class="volume lineVolume text"  value="'+v+'" />'+
					'<span class="lineVolumeAmount">'+v+'</span>';	
				}
			}
			return html+='<input type="hidden" value="'+v+'" />';
		}},
		{ hidden:'true',title:'${message("重量")}', name:'weight' ,align:'center', renderer: function(val,item,rowIndex, obj){
			return '<span class="lineWeightAmount"></span><input class="lineWeight" value="'+val+'" name="b2bReturnsItems['+b2bReturnsItemIndex+'].weight" type="hidden" />';
		}},
		
		{ title:'${message("退货箱数")}',[#if b2bReturns.sbu.id ==3]hidden:true,[/#if] name:'box_quantity', align:'center', width:100, renderer:function(val,item,rowIndex,obj){
			var quantity = 1;
			var isQuantity = true;
			var text = "";
			if(obj==undefined){
				quantity = val;
			}
			if(val != ""){
				quantity = val;
			}
			
			if((item.pb!=0&&item.pb!=undefined&&(item.per_branch==0&&item.branch_per_box==0||item.per_branch==null&&item.branch_per_box==null))||(item.per_box!=0&&item.per_box!=undefined&&(item.per_branch==0&&item.branch_per_box==0||item.per_branch==null&&item.branch_per_box==null))){
				isQuantity = false;
				if(item.pb!=undefined&&item.pb!=undefined){
					text = '<input type="hidden" class="perBox" value="'+item.pb+'">';			
				}
				if(item.per_box!=0&&item.per_box!=undefined){
					text = '<input type="hidden" class="perBox" value="'+item.per_box+'">';	
				}
			}
			if( orderStatus==0 && (obj!=undefined || item.parent==null)){
				var str = '';
				if(obj==undefined || item.parent==null){
					str = 'itemId="'+item.id+'"';
				}
				if(isQuantity&&(item.branch_per_box==undefined || item.per_branch == undefined || item.branch_per_box==0 || item.per_branch == 0)){
					quantity=0;
					return '-'+
					'<input type="hidden"  class="t boxQuantity"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].boxQuantity" value="'+quantity+'" />';
				}else{
					return '<div class="nums-input ov">'+
							'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)" />'+
			            	'<input type="text" kid="box" class="t boxQuantity" itemId="'+item.id+'" name="b2bReturnsItems['+b2bReturnsItemIndex+'].boxQuantity" value="'+quantity+'" org="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" />'+
			            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)" />'+
			            	text+
			        	'</div>';
				}
			}
			if( ( orderStatus==0) && (obj!=undefined || item.parent==null)){
				var str = '';
				if(obj==undefined || item.parent==null){
					str = 'itemId="'+item.id+'"'
				}
				if(isQuantity&&(item.branch_per_box==undefined || item.per_branch == undefined || item.branch_per_box==0 || item.per_branch == 0)){
					quantity=0;
					return '-'+
					'<input type="hidden"  class="t boxQuantity"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].boxQuantity" value="'+quantity+'" />';
				}else{
				return '<div class="nums-input ov">'+
			            	'<input type="text"  class="t boxQuantity" itemId="'+item.id+'" name="b2bReturnsItems['+b2bReturnsItemIndex+'].boxQuantity" value="'+quantity+'" org="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" readonly="readonly" />'+
			            	text+
			        	'</div>';
				}
			}else{
				var str = 'class="text boxQuantity"';
				if(obj == undefined || item.parent!=null){
					str = 'class="text cq boxQuantity" parentId="'+item.parent+'"';
				}
				if(isQuantity&&(item.branch_per_box==undefined || item.per_branch == undefined || item.branch_per_box==0 || item.per_branch == 0)){
					quantity=0;
					return '-'+
					'<input type="hidden"  class="t boxQuantity"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].boxQuantity" value="'+quantity+'" />';
				}else{
					return '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].boxQuantity" value="'+quantity+'" org="'+quantity+'" '+str+' min="0" />'
					text+
					+'<span class="qty-text">'+quantity+'</span>';
				}
			}
			
		}},
		{ title:'${message("支数")}',[#if b2bReturns.sbu.id ==3]hidden:true,[/#if] name:'branch_quantity' ,align:'center', width:100, renderer: function(val,item,rowIndex, obj){
			var type = productCategory(item.branch_per_box,item.per_branch,item.per_box,item.pb);
			var branchQuantity='';
			if(obj==undefined){
				branchQuantity = val;
			}
			if(val != ""){
				branchQuantity = val;
			}
			if( orderStatus==0 && (obj!=undefined || item.parent==null)){
				var str = '';
				if(obj==undefined || item.parent==null){
					str = 'itemId="'+item.id+'"'
				}
				var branch_per_box='';
				var  per_branch='';
				if(type==1||type==2){
					branch_per_box=0;
					per_branch=0;
					return '-'+
					       '<input type="hidden" kid="branch" class="t branchQuantity"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchQuantity" value="0"  >'+
					       '<input type=hidden class="branchPerBox" name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchPerBox" value="'+branch_per_box+'" /> '+
						   '<input type=hidden class="perBranch" name="b2bReturnsItems['+b2bReturnsItemIndex+'].perBranch" value="'+per_branch+'" />';
				}else{
					return '<div class="nums-input ov">'+
							'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
			            	'<input type="text" kid="branch" class="t branchQuantity" itemId="'+item.id+'" name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchQuantity" value="'+branchQuantity+'" org="'+branchQuantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" readonly="readonly" />'+
			            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
							'<input type=hidden class="branchBox" value="'+item.branch_box+'" />'+
							'<input type=hidden class="branchPerBox" name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchPerBox" value="'+item.branch_per_box+'" /> '+
							'<input type=hidden class="perBranch" name="b2bReturnsItems['+b2bReturnsItemIndex+'].perBranch" value="'+item.per_branch+'" />'+
			        	'</div>';
				}
			}
			if( (orderStatus==0) && (obj!=undefined || item.parent==null)){
				var str = '';
				if(obj==undefined || item.parent==null){
					str = 'itemId="'+item.id+'"'
				}
				var branch_per_box='';
				var  per_branch='';
				if(type==1||type==2){
					branch_per_box=0;
					per_branch=0;
					return '-'+
					       '<input type="hidden" kid="branch" class="t branchQuantity"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchQuantity" value="0"  >'+
					       '<input type=hidden class="branchPerBox" name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchPerBox" value="'+branch_per_box+'" /> '+
						   '<input type=hidden class="perBranch" name="b2bReturnsItems['+b2bReturnsItemIndex+'].perBranch" value="'+per_branch+'" />';
				}else{
					return '<div class="nums-input ov">'+
			            	'<input type="text" kid="branch" class="t branchQuantity" itemId="'+item.id+'" name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchQuantity" value="'+branchQuantity+'" org="'+branchQuantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" readonly="readonly" />'+
							'<input type=hidden class="branchBox" value="'+item.branch_box+'" />'+
							'<input type=hidden class="branchPerBox" name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchPerBox" value="'+item.branch_per_box+'" /> '+
							'<input type=hidden class="perBranch" name="b2bReturnsItems['+b2bReturnsItemIndex+'].perBranch" value="'+item.per_branch+'" />'+
			        	'</div>';
				}
			}else{
				var str = 'class="text branchQuantity"';
				if(obj == undefined || item.parent!=null){
					str = 'class="text " parentId="'+item.parent+'"';
				}
				var branch_per_box='';
				var  per_branch='';
				if(type==1||type==2){
					branch_per_box=0;
					per_branch=0;
					return '-'+
					       '<input type="hidden" kid="branch" class="t branchQuantity"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchQuantity" value="0"  >'+
					       '<input type=hidden class="branchPerBox" name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchPerBox" value="'+branch_per_box+'" /> '+
						   '<input type=hidden class="perBranch" name="b2bReturnsItems['+b2bReturnsItemIndex+'].perBranch" value="'+per_branch+'" />';
				}else{
					return '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchQuantity" value="'+branchQuantity+'" org="'+branchQuantity+'" '+str+' min="0">'+
					'<input type=hidden class="branchBox" name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchBox" value="'+item.branch_box+'" />'+
					'<input type=hidden class="branchPerBox" name="b2bReturnsItems['+b2bReturnsItemIndex+'].branchPerBox" value="'+item.branch_per_box+'" /> '+
					'<input type=hidden class="perBranch" name="b2bReturnsItems['+b2bReturnsItemIndex+'].perBranch" value="'+item.per_branch+'" />'
					+'<span class="qty-text">'+branchQuantity+'</span>';
				}
			}
			
		}},
		{ title:'${message("零散支数")}',[#if b2bReturns.sbu.id ==3]hidden:true,[/#if] name:'scattered_quantity' ,align:'center', width:50, renderer: function(val,item,rowIndex, obj){
			var type = productCategory(item.branch_per_box,item.per_branch,item.per_box,item.pb);
			var scatteredQuantity=0;
			if(obj==undefined){
				scatteredQuantity = val;
			}
			if(type==1||type==2||type==3){
				return '-'+
				'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].scatteredQuantity" class="scatteredQuantity text" value="0" />';
			}
			var html='<span class="scatteredQuantityStr">'+scatteredQuantity+'</span>'+
				'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].scatteredQuantity" class="scatteredQuantity text" value="'+scatteredQuantity+'" />';
			return html;
		}},
		{ title:'${message("宽")}',[#if b2bReturns.sbu.id != 3]hidden:true,[/#if]  name:'length', align:'center', width:110, renderer:function(val,item,rowIndex,obj){
			    var sbu=${b2bReturns.sbu.id}
			    var length=0;
              	if(item.length!='' && item.length!=undefined){
                  	length=item.length;
               	}
               	if(isNaN(length)){
                 	length=0;
               	}
               	if(item.unit=='m2' && sbu==3 && item.orderSn ==''){
           		   var hideStr = '';
  						return '<div class="lh20"><div class="priceDiv nums-input ov" '+hideStr+'>'+
					'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editNumber (this.nextSibling,event)">'+
					'<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].length" kid="length" class="length t"  value="'+length+'" minData="0" oninput="editNumber (this,event)" onpropertychange="editNumber(this,event)"/>'+
					'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editNumber (this.previousSibling,event)">'+
					'</div></div>';
				 }else if(item.unit=='m2' && sbu==3 && item.orderSn ==''){
				    var hideStr = '';
  						return '<div class="lh20"><div class="priceDiv nums-input ov" '+hideStr+'>'+
					'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editNumber (this.nextSibling,event)">'+
					'<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].length" kid="length" class="length t"  value="'+length+'" minData="0" oninput="editNumber (this,event)" onpropertychange="editNumber(this,event)"/>'+
					'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editNumber (this.previousSibling,event)">'+
					'</div></div>';
				 }else if(item.unit=='m2' && sbu==3 && item.ordderSn !='' ){
			      	return length+'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].length" class="length text" value="'+length+'" />';
			     }else{
			     	return '-'+'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].length" class="length text" value="0" />';
			     }		
			}},
			{ title:'${message("高")}',[#if b2bReturns.sbu.id != 3]hidden:true,[/#if]  name:'width', align:'center', width:110, renderer:function(val,item,rowIndex,obj){
			  		var sbu=${b2bReturns.sbu.id}
			  		var width=0;
                	if(item.width!='' && item.width!=undefined){
                  		width=item.width;
               		}
               		if(isNaN(width)){
               			  width=0;
                	}
                	if(item.unit=='m2' && sbu==3 && item.orderSn ==''){
            		   var hideStr = '';
   						return '<div class="lh20"><div class="priceDiv nums-input ov" '+hideStr+'>'+
						'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editNumber (this.nextSibling,event)">'+
						'<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].width" kid="width" class="width t"  value="'+width+'" minData="0" oninput="editNumber (this,event)" onpropertychange="editNumber(this,event)"/>'+
						'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editNumber (this.previousSibling,event)">'+
						'</div></div>';
					}else if(item.unit=='m2' && sbu==3 && item.orderSn ==''){
					    var hideStr = '';
   						return '<div class="lh20"><div class="priceDiv nums-input ov" '+hideStr+'>'+
						'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editNumber (this.nextSibling,event)">'+
						'<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].width" kid="width" class="width t"  value="'+width+'" minData="0" oninput="editNumber (this,event)" onpropertychange="editNumber(this,event)"/>'+
						'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editNumber (this.previousSibling,event)">'+
						'</div></div>';
					 
					 }else if(item.unit=='m2' && sbu==3 && item.ordderSn !=''){
            		 		return width+'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].width" class="width text" value="'+width+'" />';
					 }else{
				     	 	return '-'+'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].width" class="width text" value="0" />';
				     }	
			}},
			{ title:'${message("数量")}', name:'quantity', align:'center', width:110, renderer:function(val,item,rowIndex,obj){
					var quantity='';
					if(obj==undefined){
						quantity = val;
					}else{
		              quantity = accMul(item.width,item.length);;
		            }
					var sbu=${b2bReturns.sbu.id}
		            var unit = item.unit;
					if(unit=='m2' && sbu==3 && item.orderSn!=''){
		          		  quantity=Number(quantity);
						  return '<span class="quantity-text">'+quantity+'</span>'+
						   		 '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].quantity" class="t quantity" value="'+quantity+'"  min="0">';
		            }
					if( orderStatus==0 && (obj!=undefined || item.parent==null)){
						var str = '';
						if(obj==undefined || item.parent==null){
							str = 'itemId="'+item.id+'"'
						}
						return '<div class="nums-input ov">'+
									'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
					            	'<input type="text" kid="quantity" class="t quantity" itemId="'+item.id+'" name="b2bReturnsItems['+b2bReturnsItemIndex+'].quantity" value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" />'+
					            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
					        	'</div>';
					}
					if( (orderStatus==0) && (obj!=undefined || item.parent==null)){
						var str = '';
						if(obj==undefined || item.parent==null){
							str = 'itemId="'+item.id+'"'
						}
						return '<div class="nums-input ov">'+
					            	'<input type="text" kid="quantity" class="t quantity" itemId="'+item.id+'" name="b2bReturnsItems['+b2bReturnsItemIndex+'].quantity" value="'+quantity+'" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" readonly="readonly" />'+
					        	'</div>';
					}else{
						var str = 'class="text quantity"';
						if(obj == undefined || item.parent!=null){
							str = 'class="text quantity"';
						}
						return '<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].quantity" value="'+quantity+'" '+str+' min="0">'+
						 '<span class="quantity-text">'+quantity+'</span>';
					}
		}},
		{ title:'${message("实退箱数")}',[#if b2bReturns.sbu.id ==3]hidden:true,[/#if] name:'returned_box_quantity' ,align:'center', width:50, renderer: function(val,item,rowIndex, obj){
			var returnedBoxQuantity=0;
			if(obj==undefined){
				returnedBoxQuantity = val;
			}
			var html='<span class="returnedBoxQuantityStr">'+returnedBoxQuantity+'</span>'+
				'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].returnedBoxQuantity" class="returnedBoxQuantity text" value="'+returnedBoxQuantity+'" />';
			return html;
		}},
		{ title:'${message("实退支数")}',[#if b2bReturns.sbu.id ==3]hidden:true,[/#if] name:'returned_branch_quantity' ,align:'center', width:50, renderer: function(val,item,rowIndex, obj){
			var returnedBranchQuantity=0;
			if(obj==undefined){
				returnedBranchQuantity = val;
			}
			var html='<span class="returnedBranchQuantityStr">'+returnedBranchQuantity+'</span>'+
				'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].returnedBranchQuantity" class="returnedBranchQuantity text" value="'+returnedBranchQuantity+'" />';
			return html;
		}},
		{ title:'${message("实退数量")}', name:'returned_quantity' ,align:'center', renderer: function(val,item,rowIndex, obj){
			var returnedQuantity=0;
			if(obj==undefined){
				returnedQuantity = val;
			}
			var html='<span class="returnedQuantityStr">'+returnedQuantity+'</span>'+
				'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].returnedQuantity" class="returnedQuantity text" value="'+returnedQuantity+'" />';
			return html;
		}},
		{ title:'${message("单价")}', name:'price' ,align:'center', width:100,renderer:function(val,item,rowIndex,obj){
			var price=item.member_price;
			if (price==undefined){
				price=item.price;
			}
			val = price;
			if(item.orderSn == ""){
				return '<div class="nums-input ov">'+
		            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editQty (this.nextSibling,event)">'+
		            	'<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].price" class="price t" value="'+val+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)">' +
		            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty (this.previousSibling,event)">'+
						   	'</div>';	
			}else{
				return val+'<input type="hidden" name="b2bReturnsItems['+b2bReturnsItemIndex+'].price" class="price t" value="'+val+'" oninput="editQty (this,event)" onpropertychange="editQty(this,event)">' ;
			}
		}},
		
		{ [#if flag!=1]hidden:true,[/#if]title:'平台结算价', align:'center',name:'',renderer: function(val,item,rowIndex){
			var sale_org_price=item.sale_org_price==null?'':item.sale_org_price;
			[#if editSaleOrgPrice==1]
			return '<div class="nums-input ov">'+
		    	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice (this.nextSibling,event)">'+
		    	'<input type="text"  class="t text saleOrgPrice"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].saleOrgPrice" value="'+sale_org_price+'" minData="0" oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >'+
		    	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice (this.previousSibling,event)">'+
		    	'</div>';
			[#else]
		    	return '<span class="text red">'+currency(item.sale_org_price,true)+'</span><input type="hidden"  class="t text saleOrgPrice"  name="b2bReturnsItems['+b2bReturnsItemIndex+'].saleOrgPrice" value="'+sale_org_price+'" >';
			[/#if]
		}},
		{ [#if flag!=1]hidden:true,[/#if]title:'平台结算金额', align:'center',name:'',renderer: function(val,item,rowIndex){

			var sale_org_price=item.sale_org_price==null?'':item.sale_org_price;
			return '<sapn class="text red saprice">'+currency(item.quantity*sale_org_price,true)+'</span>'
		 }},
		 { title:'备注', align:'center',name:'memo',renderer: function(val,item,rowIndex){
				
			return '<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].memo" value="'+item.memo+'" id="memo"/>';
			
		}},
		[#if hiddenBatch !=0]     
		    { title:'${message("色号")}', align:'center',name:'colour_number',renderer: function(val,item,rowIndex, obj){
		    	var colourNumber='<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].colourNumber" class="text colourNumber" value="'+item.colour_number+'" />';
				return colourNumber;
		    }},    
		    { title:'${message("含水率")}', align:'center',name:'moisture_content',renderer: function(val,item,rowIndex, obj){
		    	var moistureContent='<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].moistureContent" class="text moistureContent" value="'+item.moisture_content+'" />';
				return moistureContent;
		    }},
		    { title:'${message("批次")}', align:'center',name:'batch',renderer: function(val,item,rowIndex, obj){
		    	var batch='<input type="text" name="b2bReturnsItems['+b2bReturnsItemIndex+'].batch" class="text" value="'+item.batch+'" />';
				return batch;
		    }},
		[/#if] 
		{ title:'${message("库存数量")}',align:'center', width:110, renderer: function(val,item,rowIndex, obj){
	 			return '<span class="onhandQuantity">0</span>';
	  	}},
	  	{ title:'${message("可用数量")}',align:'center', width:110, renderer: function(val,item,rowIndex, obj){
		 	return '<span class="attQuantity">0</span>';
	    }},   
		{ title:'金额', align:'center',name:'',renderer: function(val,item,rowIndex){
			b2bReturnsItemIndex++;
			line_no++;
			return '<sapn class="text red trprice">'+currency(item.quantity*item.price,true)+'</span>'
		}}
	];

	$mmGrid = $('#table-m1').mmGrid({
		height:'auto',
        cols: cols0,
        checkCol:false,
        fullWidthRows: true,
        items:items,
        autoLoad: true,
        callback:function(){
         	countTotal();
       		editNumber();
       		productOrganization();
       		
         }
    });
	$addProduct.click(function(){
		//打开选择产品界面
		var $storeId = $(".storeId").val();
		if ($storeId==undefined || $storeId.length == 0) {
			$.message_alert("请选客户！");
			return false;
		}
		var warehouseId = $(".warehouseId").val();
		if (warehouseId==undefined || warehouseId.length == 0) {
			$.message_alert("请选仓库！");
			return false;
		}
		/* var params='';
  	    [#if organizationIsNoWarehouse == 0 ] 
      	  	var organizationId = $(".organizationId").val();
    	  	if (organizationId=='undefined' || organizationId.length == 0) {
    	  		$.message_alert('经营组织不能为空');
      			return false;
	  		}
    	  	params+='&organizationId='+organizationId;
	    [/#if] */
		var sbuId = $(".sbuId").val();
        var memberRankId = $(".memberRankId").val();
        if(memberRankId == undefined){
            memberRankId = "";
        }
		$addProduct.bindQueryBtn({
			type:'product',
			bindClick:false,
			title:'查询产品',
			url:'/product/product/selectProduct_returns.jhtml?multi=2&storeId='+$(".storeId").val()+'&sbuId='+sbuId+'&isToOrder=true'+'&memberRankId='+memberRankId,
			callback:function(rows){
				if(rows.length>0){
					if($("input[name='orderId']").val()!=''){
						$("input[name='orderId']").val('');
						$("input[name='orderSn']").val('');
				    }
					var error = '';
					for (var i = 0; i < rows.length;i++) {
						var row = rows[i];
						$mmGrid.addRow(rows,null,1);
						countTotal();
					}
					productOrganization();
				}
			}
		});	
	});
	
	
  	$addSaleOrgs.click(function(){
	  $addSaleOrgs.bindQueryBtn({
	      type:'saleOrg',
	      bindClick:false,
	      title:'${message("查询工厂")}',
	      url:'/aftersales/aftersale/select_factory.jhtml',
	      callback:function(rows){
	          if(rows.length>0){
	          	var id = rows[0].id;
	          	var name = rows[0].factory_name;
	          	var username = rows[0].sale_org;
	          	$(".factoryId").val(id);
	          	$(".factoryName").val(name);
	          }
	      }
		});
	});
  	
    //发货明细页面弹出
    $findShipping.click(function(){
    	var $storeId = $(".storeId").val();
        if ($storeId==undefined || $storeId.length == 0) {
			$.message_alert("请选客户！");
			return false;
		}
        var warehouseId = $(".warehouseId").val();
		if (warehouseId==undefined || warehouseId.length == 0) {
			$.message_alert("请选仓库！");
			return false;
		}
		/* var params='';
  	    [#if organizationIsNoWarehouse == 0 ] 
      	  	var organizationId = $(".organizationId").val();
    	  	if (organizationId=='undefined' || organizationId.length == 0) {
    	  		$.message_alert('经营组织不能为空');
      			return false;
	  		}
    	  	params+='&productOrganizationId='+organizationId;
	    [/#if] */
        var sbuId = $(".sbuId").val();
        $findShipping.bindQueryBtn({
            type:'shippingItem',
            bindClick:false,
            title:'参考发货单',
            url:'/aftersales/b2b_returns/selectShipping.jhtml?multi=2&storeId='+$(".storeId").val()+'&sbuId='+sbuId,
            callback:function(rows){
                if(rows.length>0){
                    var error = '';
                    for (var i = 0; i < rows.length;i++) {
                        var row = rows[i];
                        $mmGrid.addRow(row,null,1);
                        newCountTotal();
                    }
                    productOrganization();
                }
            }
        });
    });
            
    //查询合同
	$("#selectContract").click(function(){
    	var sbuId = $(".sbuId").val();
    	var storeId = $(".storeId").val();
    	$("#openStore").bindQueryBtn({
	        type:'store',
	        bindClick:false,
	        title:'${message("查询合同")}',
	        url:'/b2b/customerContract/select_contract.jhtml?sbuId='+sbuId+'&storeId='+storeId,
	        callback:function(rows){
	            if(rows.length>0){
	                var row = rows[0];
	                $("input[name='contractId']").attr("value",row.id);
	                $("input[name='contractName']").attr("value",row.contract_name);
	                $("input[name='storeId']").attr("value",row.store_id);
	                $("input[name='storeName']").attr("value",row.store_name);
	                $("input[name='saleOrgId']").attr("value",row.sale_org_id);
                    $("#saleOrgName").text(row.sale_org_name);
	                $("input[name='storeMemberId']").attr("value",row.store_member_id);
	                $("input[name='storeMemberName']").attr("value",row.store_member_name);
	                $("input[name='contacts']").attr("value",row.contacts);
	                $("input[name='phone']").attr("value",row.phone);
	                $("input[name='address']").attr("value",row.address);
	            }
	        }
	    });
    })
    
	$addOrderItem.click(function(){
		//打开选择订单明细界面
		$addProduct.bindQueryBtn({
			type:'orderItem',
			bindClick:false,
			title:'查询订单明细',
			url:'/aftersales/b2b_returns/select_shipping_item.jhtml?multi=2&orderId='+$("input.orderId").val()+'&'+$("input.itemIds").serialize(),
			callback:function(rows){
				if(rows.length>0){
					var error = '';
					for (var i = 0; i < rows.length;i++) {
						var idH = $(".orderItemId_"+rows[i].id).length;
						if(idH > 0){
							$.message_alert('产品【'+rows[i].name+'】已添加');
							return false;
						}
					}
					for (var i = 0; i < rows.length;i++) {
						var row = rows[i];
						$mmGrid.addRow(rows,null,2);
						countTotal();
					}
					
				}
			}
		});	
	});
    
	//订单明细页面弹出
	$findOrder.click(function(){
		var $storeId = $(".storeId").val();
		if ($storeId==undefined || $storeId.length == 0) {
			$.message_alert("请选客户！");
			return false;
		}
		var warehouseId = $(".warehouseId").val();
		if (warehouseId==undefined || warehouseId.length == 0) {
			$.message_alert("请选仓库！");
			return false;
		}
		/* var params='';
  	    [#if organizationIsNoWarehouse == 0 ] 
      	  	var organizationId = $(".organizationId").val();
    	  	if (organizationId=='undefined' || organizationId.length == 0) {
    	  		$.message_alert('经营组织不能为空');
      			return false;
	  		}
    	  	params+='&productOrganizationId='+organizationId;
	    [/#if] */
		var sbuId = $(".sbuId").val();
		var contractId=$(".contractId").val();
		$findOrder.bindQueryBtn({
			type:'orderItem',
			bindClick:false,
			title:'参考订单',
			url:'/aftersales/b2b_returns/selectOrder.jhtml?multi=2&storeId='+$(".storeId").val()+'&sbuId='+sbuId+'&contractId='+contractId,
			callback:function(rows){
				if(rows.length>0){
					var error = '';
					for (var i = 0; i < rows.length;i++) {
						var idH = $(".productId_"+rows[i].id).length;
						if(idH > 0){
							$.message_alert('产品【'+rows[i].name+'】已添加');
							return false;
						}
					}
					for (var i = 0; i < rows.length;i++) {
						var row = rows[i];
						$mmGrid.addRow(row,null,1);
						newCountTotal();
					}
					productOrganization();
				}
			}
		});	
	});	
	
	// 删除商品
	$("a.deleteProduct").live("click", function() {
		var $this = $(this);
		$.message_confirm('${message("您确定要删除吗？")}',function(){
				var tr = $this.closest("tr");
				var le = $("#addProductTable").find("tr").length;
				if(le == 2){
					$.message_alert('${message("至少保留一个明细")}');
				}else{
					tr.remove();
					countTotal();
					var line_number = 1;
					$("span.line_no").each(function(){
						$(this).html(line_number++);
					});
					line_no--;
				}
		});
	});

	$("input[name='image1']").single_upload({
		uploadSize:"source"
	});

	$("input[name='image2']").single_upload({
		uploadSize:"source"
	});

	$("input[name='image3']").single_upload({
		uploadSize:"source"
	});
	
	$("input.NonNegative").live("change",function(){
		if($(this).val()=="" || $(this).val()<0){
			$(this).val(0);
		}
	});
	// 表单验证
	$inputForm.validate({
		rules: {
			amount: "required",
			reason: "required",
			warehouseName:"required",
			category:"required"
		},
		submitHandler:function(form){
		return false;
		}
		 
	});
	//查询客户
	$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'/member/store/select_store.jhtml?type=distributor&isMember=1',
		callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
					$("input.storeId").val(row.id);
					$("input.storeName").val(row.name);
					$(".saleOrgName").val(row.sale_org_name);
					$(".saleOrgId").val(row.sale_org_id);
					if($("input.orderId").val()!=''){
						$mmGrid.removeRow();
						$("input[name='orderId']").val('');
						$("input[name='orderSn']").val('');
						b2bReturnsItemIndex = 0;
					}
					
					$("input[name='storeName']").attr("value",row.name);
	                $("input[name='consignee']").attr("value",row.store_consignee);
	                $("input[name='consigneeMobile']").attr("value",row.store_mobile);
	                $("input[name='address']").attr("value",row.store_address);
	                $("input[name='zipCode']").attr("value",row.store_zip_code);
	               $("input[name='addressOutTradeNo']").attr("value",row.out_trade_no);
	               
	               var areaId = row.store_area;
	                if(areaId==null)areaId='';
	                var areaName = row.area_full_name;
	                if(areaName==null)areaName='';
	                var treePath = row.area_tree_path;
	                if(treePath==null)treePath='';
	                $("input[name='areaId']").val(areaId);
	        	    $("input[name='areaName']").val(areaName);
	                
	        	    
	        	    $(".warehouseId").val('');
        			$(".warehouseName").val('');
        			if(row.sale_org_region==1){
	        			$(".region").text("南区");
        			}else if(row.sale_org_region==0){
        				$(".region").text("北区");
        			}else{
        				$(".region").text("");
        			}
           			
	                //可发货余额
					var id = rows.id;
					ajaxSubmit($(id),{
						method:'post',
						url:'/finance/balance/get_balance.jhtml',
						data:{storeId:row.id},
						callback:function(resultMsg) {
							var data = resultMsg.objx;
							$("#balance").text(currency(data,true)); 
							[#if hiddenAmount==0]
							$("#balance").text("***");
							[/#if]
							$("#storeBalance").val(data); 
						}
					})
				}
	    	}
	});
	
	//查询仓库
	$("#selectWarehouse").click(function(){
        var $saleOrgId = $(".saleOrgId").val();
        var sbuId = $(".sbuId").val();
        if($saleOrgId==""){
            $.message_alert('请选择机构');
        }else{
        	$("#selectWarehouse").bindQueryBtn({
                type:'warehouse',
                bindClick:false,
				title:'${message("查询仓库")}',
				url:'/stock/warehouse/select_warehouse.jhtml?saleOrgId='+$saleOrgId+'&sbuId='+sbuId,
				callback:function(rows){
	            		if(rows.length>0){
	            			var row = rows[0];
	            			$(".warehouseId").val(row.id);
	            			$(".warehouseName").val(row.name);
	            			$(".typeSystemDictId").val(row.type_system_dict_id);
	            			$(".typeSystemDictValue").html(row.type_system_dict_value);
	            			$("input[name='organizationId']").val(row.management_organization_id);
	            			$("#organizationId").text(row.management_organization_name);
	            			
	            			var ssId = $("input[name='storeId']").val();
		            			ajaxSubmit($(row.id),{
									method:'post',
									url:'/finance/balance/get_balance.jhtml',
									data:{storeId:ssId,organizationId:row.management_organization_id},
									callback:function(resultMsg) {
										var data = resultMsg.objx;
										$("#bal").text(currency(data,true)); 
										[#if hiddenAmount==0]
										$("#bal").text("***");
										[/#if]
										$("#storeBalance").val(data);
 										//授信
 										$("#credit").text(currency(data.credit_amount,true));
 										//余额
 										$("#yue").text(currency(data.balance-data.credit_amount,true))
									}
								})
	            			
	            		if(sbuId !=3){
	            			$mmGrid.removeRow();
	            		}
	            			countTotal();
	            		}
				}
			});
        }
	})
	
	
	//打开选择地址界面
    $("#addReceiveAddress").click(function(){
        var storeId = $(".storeId").val();
        if(storeId==""){
            $.message_alert('请选择客户');
        }else{
        		select_store_address(storeId);
        }
    });
	
	//查询销售订单
		$("#selectOrder").bindQueryBtn({
			type:'order',
			title:'查询销售订单',
			url:'/b2b/order/select_order.jhtml?orderStatus=6',
			callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
					$("input.orderId").val(row.id);
					$("input.orderSn").val(row.sn);
					$("input[name='storeId']").val(row.stores);
					$("input.storeName").val(row.store_name);
					$mmGrid.load({orderId:row.id});
				}
	    	}
		});	
	
		//查询机构
		$("#selectSaleOrg").bindQueryBtn({
			type:'saleOrg',
			title:'${message("查询机构")}',
			url:'/basic/saleOrg/select_saleOrg.jhtml',
			callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
					$("input.saleOrgId").val(row.id);
					$("input.saleOrgName").val(row.name);
					if($("input.orderId").val()!=''){
						$mmGrid.removeRow();
						$("input[name='orderId']").val('');
						$("input[name='orderSn']").val('');
						$("input[name='name']").val('');
						$("input[name='mobile']").val('');
						b2bReturnsItemIndex = 0;
					}
				}
	    	}
		});
		
		 //打开选择业务员界面
	    $("#selectsaleman").bindQueryBtn({
			type:'saleman',
			title:'${message("查询认领人")}',
			url:'/member/store_member/select_saleman.jhtml'
		});
		
		$("#openArea").bindQueryBtn({
		type:'area',
		title:'${message("查询地区")}',
		url:'/basic/area/select_area.jhtml',
		callback:function(rows){
			if(rows.length>0){
				var $tr =$this.closest("tr");
				$(".areaId").val(rows[0].id);
				$(".areaName").val(rows[0].full_name);
				$("input[name='zipCode']").val('');
				}
			}
		});	
		
		var orderFullLink_items = ${fullLink_json};
	var cols = [				
    	{ title:'${message("内容")}', name:'content' ,width:300,align:'center'},
		{ title:'${message("操作人")}', name:'operator_name',width:100 ,align:'center'},
		{ title:'${message("创建日期")}', name:'create_date' ,width:150 ,align:'center'}
	];
	$('#table-full').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        items:orderFullLink_items,
        checkCol: false,
        autoLoad: true
    });
	
	//附件初始化
	initGrid();
	
	var $delProductImage1 = $(".deleteAttachment");
	$delProductImage1.live("click", function() {
		var $this = $(this);
		$this.closest("tr").remove();
	});
	
	$("form").bindAttribute({
		isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			});
	    }
	 });
	 
	 $("#selectAftersale").click(function(){
	 	var category = $('.category').val();
    	if(category=='0'||category==''||category==undefined){
    		$.message_alert('退货种类必须是售后退货才能选择售后单据！');
    		return false;
    	}
	    //查询售后单据
	    $(this).bindQueryBtn({
	        type:'aftersale',
	        bindClick:false,
	        title:'${message("查询售后单据")}',
	        url:'/aftersales/aftersale/select_aftersale.jhtml?multi=1',
	        callback:function(rows){
	            if(rows.length>0){
	                var row = rows[0];
	                if(row.return_status == null||row.return_status != "0"){
                    	$.message_alert('售后单据退货状态不是未退货状态请重新选择！');
                    }else{
                        $('.aftersaleId').val(row.id);
		                $('.aftersaleSn').val(row.sn);
		                $("input[name='factoryId']").val(row.factory_id);
		                $("input[name='factoryName']").val(row.factory_name);
		                $("input[name='storeId']").val(row.store_id);
		                $('.storeName').val(row.store_name);	                        
                    }
	        	}
	        }
	    });
	 
	 });
	 
	 
	 [#if linkStock == 1 ] 
		 //产品经营组织
		 $(".productOrganization").live("change", function() {
				productOrganizationChange(this);	
		 })
		 //产品等级
		 /* $(".productGrade").live("change", function() {
				productOrganizationChange(this);	
		 }) */
		 //色号
		 $("input.colourNumber").live("change", function() {
				productOrganizationChange(this);	
		 })
		 //含水率
		 $("input.moistureContent").live("change", function() {
				productOrganizationChange(this);	
		 })
	[/#if]
	 
});

//经营组织点击触发事件
function productOrganizationChange(e){
	var warehouseId = $("input.warehouseId").val();
	var $tr = $(e).closest("tr");
	//产品经营组织
	var productOrganizationId = $tr.find(".productOrganization").val();
	if(productOrganizationId !=null && productOrganizationId !='' && productOrganizationId !='undefined'){
		//等级
		var productGrade = $tr.find(".productGrade").val();
		//编码
		var vonderCode = $tr.find(".vonderCode").text();
		//色号
		var colourNumber = $tr.find(".colourNumber").val();
		if(colourNumber=='undefined' || colourNumber.length == 0){
			colourNumber = "";
		}
		//含水率
		var moistureContent = $tr.find(".moistureContent").val();
		if(moistureContent=='undefined' || moistureContent.length == 0){
			moistureContent = "";
		}
		var params='&productGrade='+productGrade+'&vonderCode='+vonderCode+'&colourNumber='+colourNumber
		   +'&moistureContent='+moistureContent+'&organizationId='+productOrganizationId+'&warehouseId='+warehouseId;
			params = params.substring(1,params.length);
		$.ajax({
			url:'/stock/stock/findViewStock.jhtml?'+params,
   			type : "post",
   			success : function(rows) {
   				var data= $.parseJSON(rows.content);
                   if(data.length>0){
                       for (var i = 0; i < data.length;i++) {
                       	if(data[i].totalOnhandQuantity1 !=null && data[i].totalOnhandQuantity1 !=''){
                       		$tr.find(".onhandQuantity").text(data[i].totalOnhandQuantity1);
                       	}else{
                       		$tr.find(".onhandQuantity").text(0);
                       	}
                       	if(data[i].totalAttQuantity1 !=null && data[i].totalAttQuantity1 !=''){
                       		$tr.find(".attQuantity").text(data[i].totalAttQuantity1);
                       	}else{
                       		$tr.find(".attQuantity").text(0);
                       	} 
                   	}
               	}else{
               		$tr.find(".onhandQuantity").text(0);
               		$tr.find(".attQuantity").text(0);
               	}
   			}
		})	
	}
} 

$.validator.addClassRules({
	productId: {
		required: true
	},
	quantity: {
		required: true,
		min:0
	},
	storeName: {
		required: true
	},
	warehouseName:{
		required: true
	}
});

function save(e){
	var $input = $("input.pprice");
	var str='您确定要保存吗？';
	var count = 0;
	var err=0;
	$input.each(function(){
		var p = $(this).val();
		var $tr=$(this).closest("tr");
		 var length = $tr.find(".length").val();
         var width = $tr.find(".width").val();
		var branchQuantity=$tr.find(".branchQuantity").val();
		if(branchQuantity%1!=0){
			str='支数不允许是小数，必须为整数';
			err++;
			return false;
		}
		if(p == '0'){
			count++;
			
		}
		
		 if(length!='' && length!=0){
              
                  if(length.toString().split(".")[1] ==undefined || length.toString().split(".")[1].length <2){
                    $.message_alert("订单明细宽必须为两位小数，请补齐");
                      count++;
               			 return false;
                   }
                }
                
                 if(width!='' && width!=0){
                  if(width.toString().split(".")[1] ==undefined || width.toString().split(".")[1].length <2){
                    $.message_alert("订单明细高必须为两位小数，请补齐");
                     count++;
              			 return false;
                   }
               }
	})
	if(err!=0){
		$.message_alert(str);
		return false;
	}
		var url = '/aftersales/b2b_returns/update.jhtml'
		if($("#inputForm").valid()){
		ajaxSubmit(e,{
			url: url,
			data:$("#inputForm").serialize(),
			method: "post",
			isConfirm:true,
			confirmText:str,
			callback:function(resultMsg){
				if(resultMsg.type!="success"){
					$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
						location.reload(true);
					})
					return false;
				}
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.reload(true);
				})
			 }
			})
		}
}

//审核
function check(e){
	var data = $("#inputForm").serialize();
	ajaxSubmit(e,{
	    url:"/aftersales/b2b_returns/check_wf.jhtml",
	    method:"post",
	    data:data,
	    isConfirm:true,
	    confirmText:'您确定要审核吗？',
	    callback:function(resultMsg){
		    $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
	    }
    })
}
//入库
function stockIn(e){
	if($(".storeId").attr("value")==""){
		$.message_alert('请选择客户！');
	}else{
		var $content = '<iframe name="iframePager_asign_warehouse" src="/aftersales/b2b_returns/returns_info.jhtml?id=${b2bReturns.id}" width="100%" height="420px"><\/iframe>';
		var $dialog_win = $.dialog({
			title:"${message("查看入库明细")}",
			width:1200,
			height:508,
			content: $content,
			closeIconHide:true,
			ok: "确定",
			cancel: "取消",
			onOk:function(){
				var data = $("#inputForm").serialize();
				ajaxSubmit(e,{
				    url:"/aftersales/b2b_returns/stockIn.jhtml",
				    method:"post",
				    data:data,
				    callback:function(resultMsg){
					    $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
							location.reload(true);
						})
				    }
			    })
			},
		});
	}
}
function product_view(id){
	var storeId = $('.storeId').val();
	create_iframe('/product/product/content.jhtml?id='+id+'&storeId='+storeId);
}

//更换地址
function select_store_address(storeId){
 $("#addReceiveAddress").bindQueryBtn({
    	        type:'store',
    	        bindClick:false,
    	        title:'${message("更换地址")}',
    	        url:'/member/store/select_store_address.jhtml?storeId='+storeId,
    	        callback:function(rows){
    	            if(rows.length>0){
    	            	var row = rows[0];
    	                $("input[name='consignee']").attr("value",row.consignee);
    	                $("input[name='consigneeMobile']").attr("value",row.mobile);
    	                $("input[name='address']").attr("value",row.address);
    	                $("input[name='zipCode']").attr("value",row.zip_code);
    	                $("input[name='addressOutTradeNo']").attr("value",row.out_trade_no);
    	                $(".select_area").find(".fieldSet").empty();
    	                var areaId = row.area;
    	                if(areaId==null)areaId='';
    	                var areaName = row.area_full_name;
    	                if(areaName==null)areaName='';
    	                var treePath = row.tree_path;
    	                if(treePath==null)treePath='';
    	                $("input[name='areaId']").val(areaId);
    	                $("input[name='areaName']").val(areaName);
    	                
    	            }
    	        }   
    	    });
 }

//作废
function cancel(e){
	var data = $("#inputForm").serialize();
	ajaxSubmit(e,{
	    url:"/aftersales/b2b_returns/cancel.jhtml",
	    method:"post",
	    data:data,
	    isConfirm:true,
	    confirmText:'您确定要作废吗？',
	    callback:function(resultMsg){
		    $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
	    }
    })
}

[#--
function check_wf(e){
	var $this = $(e);
	var $form = $("#inputForm");
	if($form.valid()){
		$.message_confirm("您确定要审批流程吗？",function(){
			var url="/aftersales/b2b_returns/check_wf.jhtml?id="+${b2bReturns.id};
			var data = $form.serialize();
			
			ajaxSubmit(e,{
				 url: '/wf/wf_obj_config/get_config.jhtml?obj_type_id=96&objid=${b2bReturns.id}',
				 method: "post",
				 callback:function(resultMsg){
				 	var rows = resultMsg.objx;
				 		var str = '';
					 	for(var i=0;i<rows.length;i++){
					 		var row = rows[i];
					 		str+='<option value="'+row.id+'">'+row.wf_temp_name+'</option>';
					 	}
					 	var content = '<table class="input input-edit" style="width:100%">'
								+'<tbody><tr><th>流程模版</th>'
								+'<td>'
									+'<select class="text" id="objConfId">'
										+str
									+'</select>'
								+'</td>'
							+'</tr></tbody></table>';
						var $dialog_check = $.dialog({
							title:"${message("退货单审核")}",
							height:'135',
							content: content,
							onOk:function(){
								var objConfId = $("#objConfId").val();
								if(objConfId=='' || objConfId == null){
									$.message_alert("请选择流程模版");
									return false;
								}
								data = data+'&objConfId='+objConfId;
								ajaxSubmit($this,{
									 url: url,
									 method: "post",
									 data: data,
									 callback:function(resultMsg){
										$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
											reflush_wf();
										});
									 }
								})
								
							}
						});
				 	
				 }
			})
		});
	}
} --]

function check_wf(e){
	var $this = $(e);
	var $form = $("#inputForm");
	var a = $(".sbuId").val();
	
	if($form.valid()){
		$.message_confirm("您确定要审批流程吗？",function(){
			//var objTypeId = 100020;//大自然开发
			//var modelId = 777501;//大自然开发
			var objTypeId = 100019;//大自然测试-正式
			var modelId = model(a,"正式");
			//var modelId = model(a,"测试");
			var url="/aftersales/b2b_returns/check_wf.jhtml?id=${b2bReturns.id}&modelId="+modelId+"&objTypeId="+objTypeId;
			var data = $form.serialize();
			ajaxSubmit(e,{
				method: 'post',
				url: url,
				async: true,
				callback: function(resultMsg) {
					$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
						location.reload(true);
					})
				}
			});
		});
	}
}
function model(sbuId,versions){
	var json = '{"正式":{"1":"2501","2":"5","3":"7","4":"3","5":"9","6":"57598","7":"57598","8":"107689"},';
	json +='"测试":{"1":"2501","2":"5","3":"7","4":"3","5":"9","6":"1","7":"","8":"175131"}}';
	var model = JSON.parse(json);
	return model[versions][sbuId];
}

function selectInvoice(e){
	var storeId = $("input[name='storeId']").val();
	if(storeId==""){
		$.message_alert('请选择客户');
		return false;
	}
	$(e).bindQueryBtn({
		type:'supplier',
		bindClick:false,
		title:'${message("查询发票抬头")}',
		url:'/member/store/select_store_invoice.jhtml?storeId='+storeId,
		callback:function(rows){
			if(rows.length>0){
				$("input[name='invoiceTitle']").val(rows[0].invoice_title);
			}
		}
	});
}

//查看PDF
function check_pdf(e,id){
	ajaxSubmit(e,{
		url:"/aftersales/b2b_returns/checkPdf.jhtml",
		method:"post",
		data:{id:id},
		async: false,
		callback:function(resultMsg){
			window.open(resultMsg.content);
		}
	});
}

function saveform(e){
	$.message_confirm("您确定要提交吗？",function(){
		//获取表单所有数据
		var params = $("#inputForm").serializeArray();
		//定义url
		var url = '/aftersales/b2b_returns/saveform.jhtml?Type='+e;
		ajaxSubmit(e,{
			method:'post',
			url:url,
			data:params,
			async: true,
			callback: function(resultMsg) {
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.reload(true);
				})
			}
		});
	});
}

</script>
</head>
<body>
	<div class="pathh">&nbsp;${message("查看退货单")}</div>
	<form id="inputForm" action="/aftersales/b2b_returns/update.jhtml" method="post" type="ajax" validate-type="validate">
		<input name="id" value="${b2bReturns.id}" type="hidden">
			<div class="tabContent">
				<table class="input input-edit">
					<tr>
						<th>
							${message("退货单号")}:
						</th>
						<td>
							${b2bReturns.sn}
						</td>
						<th>
							${message("ERP单号")}:
						</th>
						<td>
							${b2bReturns.erpSn}
						</td>
						<th>
							<span class="requiredField">*</span>${message("客户")}:
						</th>
						<td>
							<span class="search" style="position: relative"> 
								<input class="storeId" type="hidden" name="storeId" value="${b2bReturns.store.id}" maxlength="200" btn-fun="clear">
								<input class="text storeName" maxlength="200" type="text" value="${b2bReturns.store.name}" onkeyup="clearSelect(this)"readOnly />
								<!--<input type="button" class="iconSearch" value="" id="selectStore">-->
							</span>
						</td>
						<th>
							${message("机构")}:
						</th>
						<td>
							<span class="search" style="position: relative">
								<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value="${b2bReturns.saleOrg.id}" /> 
								<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" value="${b2bReturns.saleOrg.name}" readOnly /> 
								<!--<input type="button" class="iconSearch" value="" id="selectSaleOrg">-->
							</span>
						</td>
					</tr>
					<tr>
						<th>
							<span class="requiredField">*</span>${message("经营组织")}:
						</th>
						<td>
							<span id="organizationId">${b2bReturns.organization.name}</span>
							<input type="hidden" name="organizationId" value="${b2bReturns.organization.id}" class="text organizationId" />
						</td>
						<th>
							<span class="requiredField">*</span>${message("仓库")}:
						</th>
						<td>
							<span class="search" style="position: relative"> 
								<input type="hidden" name="warehouseId" class="text warehouseId" btn-fun="clear" value="${b2bReturns.warehouse.id}" /> 
								<input type="text" name="warehouseName" class="text warehouseName" maxlength="200" onkeyup="clearSelect(this)" value="${b2bReturns.warehouse.name}" readOnly /> 
								<input type="button" class="iconSearch" value="" id="selectWarehouse">
							</span>
						</td>
						<th>
							${message("仓库类型")}:
						</th>
						<td>
							<span class="typeSystemDictValue">${b2bReturns.warehouse.typeSystemDict.value}</span>
							<input type="hidden" name="typeSystemDictId" class="typeSystemDictId" value="${b2bReturns.warehouse.typeSystemDict.id}" />
						</td>
						<th>
							${message("发票抬头")}:
						</th>
						<td>
							<span class="search" style="position: relative"> 
								<input name="invoiceTitle" class="text invoiceTitle" maxlength="200" type="text" value="${b2bReturns.invoiceTitle}" readonly/> 
								<input type="button" class="iconSearch" value="" onclick="selectInvoice(this)">
							</span>
						</td>
					</tr>
					<tr>
						<th>
							${message("退货人姓名")}:
						</th>
						<td>
							<input name="name" type="text" class="text" value="${b2bReturns.name}">
						</td>
						<th>
							${message("退货人电话")}:
						</th>
						<td>
							<input name="mobile" type="text" class="text" value="${b2bReturns.mobile}">
						</td>
						<th>
							${message("退货单状态")}:
						</th>
						<td>
							[#if b2bReturns.status == 0] <b class="red">${message("未审核")}</b>
							[#elseif b2bReturns.status == 1] <b class="green">${message("已审核")}</b>
							[#elseif b2bReturns.status == 2] <b class="green">${message("入库中")}</b>
							[#elseif b2bReturns.status == 3] <b class="green">${message("已入库")}</b>
							[#elseif b2bReturns.status == 4] <b class="green">${message("部分退货")}</b>
							[#elseif b2bReturns.status == 5] <b class="green">${message("完全退货")}</b>
							[#elseif b2bReturns.status == 6] <b class="red">${message("已作废")}</b>
							[/#if]
						</td>
						<th>
							${message("流程状态")}:
						</th>
						<td>
							[#if b2bReturns.wfState == "0"]${message("未启动")}[/#if]
							[#if b2bReturns.wfState == "1"]${message("审核中")}[/#if] 
							[#if b2bReturns.wfState == "2"]${message("已完成")}[/#if] 
							[#if b2bReturns.wfState == "3"]${message("驳回")}[/#if]
						</td>
					</tr>
					<tr>
						<th>
							${message("创建人")}:
						</th>
						<td>
							${b2bReturns.storeMember.name}
						</td>
						<th>
							${message("ERP退货时间")}:
						</th>
						<td>
							${(b2bReturns.erpDate?string("yyyy-MM-dd HH:mm:ss"))!}
						</td>
						<th>
							${message("ERP发运备注")}:
						</th>
						<td>
							${b2bReturns.erpRemark}
						</td>
						<th>
							<span class="requiredField">*</span>${message("发运方式")}:
						</th>
						<td>
							<select id="smethod" name="smethod" class="text">
								[#list sbuItems as sbuItem]
								<option value="${sbuItem.shippingMethod.value}" [#if b2bReturns.sbu.id==sbuItem.sbu.id]selected[/#if]>${sbuItem.shippingMethod.value}</option>
								[/#list]
							</select>
						</td>
					</tr>
					<tr>
						<th>
							${message("业务类型")}:
						</th>
						<td>
							<select id="businessTypeId" name="businessTypeId" class="text"> 
								[#list businessTypes as businessType]
								<option value="${businessType.id}" [#if b2bReturns.businessType.id==businessType.id]selected[/#if]>${businessType.value}</option>
								[/#list]
							</select>
						</td>
						<th>
							${message("Sbu")}:
						</th>
						<td>
							<input type="hidden" name="sbuId" class="text sbuId" id="sbuId" btn-fun="clear" value="${b2bReturns.sbu.id}" /> 
							<span id="sbuName"> ${b2bReturns.sbu.name} </span>
						</td> 
						[#if flag]
						<th>
							${message("价格类型")}:
						</th>
						<td>
							<input type="hidden" name="memberRankId" class="text memberRankId" id="memberRankId" btn-fun="clear" value="${memberRankId}" /> 
							<span id="memberRankName">${memberRankName} </span>
						</td>
						[/#if]
						<th>
							<span class="requiredField">*</span>${message("GL时间")}:
						</th>
						<td>
							<input id="startTime" name="glDate" class="text" value="${b2bReturns.glDate}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" /> 
							<input id="endTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="hidden" btn-fun="clear" />
						</td>
					</tr>
					<tr>
						<th>
							${message("区域经理")}:
						</th>
						<td>
							${b2bReturns.regionalManager.name}
						</td>
						<th>
							${message("退货种类")}:
						</th>
						<td>
							[#if b2bReturns.sbu.id == 1]
							<select class="text category" name="category" >
								<option value="0" [#if b2bReturns.category == 0]selected[/#if]>${message("其他退货")}</option>
								<option value="1" [#if b2bReturns.category == 1]selected[/#if]>${message("售后退货")}</option>
							</select>
							[#else]
							${message("其他退货")}
							<input type="hidden" name="category" value="0" />
							[/#if]
						</td>
						[#if b2bReturns.sbu.id == 1]
						<th>
							${message("售后单据")}:
						</th>
						<td>
							<span class="search" style="position: relative"> 
								<input type="hidden" name="aftersaleId" class="text aftersaleId" btn-fun="clear" value="${b2bReturns.aftersale.id}" /> 
								<input type="text" name="aftersaleSn" class="text aftersaleSn" maxlength="200" onkeyup="clearSelect(this)" value="${b2bReturns.aftersale.sn}" readOnly /> 
								<input type="button" class="iconSearch" value="" id="selectAftersale">
							</span>
						</td>
						<th>
							${message("工厂")}:
						</th>
						<td>
							<span class="search" style="position:relative">
			                    <input type="hidden" name="factoryId" class="text factoryId" value="${b2bReturns.factoryId}" btn-fun="clear"/>
			                    <input type="text" name="factoryName" class="text factoryName"  value="${b2bReturns.factoryName}" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
			                    <input type="button" class="iconSearch" value="" id="openSaleOrg">
		                    </span>
						</td>
						[/#if]
					</tr>
					<tr>
						<th>
							${message("客户简称")}:
						</th>
						<td>
							${b2bReturns.store.alias}
						</td>
				[#if contractRoles==1]
					<th><span class="requiredField">*</span>${message("合同名称")}:</th>
            			<td>
            		 	<span class="search" style="position: relative">
            		  	<input  type="hidden" name="contractId" class="text contractId" value="${b2bReturns.customerContract.id}" btn-fun="clear" />
            		 	 <input type="text" name="contractName" class="text contractName" value="${b2bReturns.customerContract.contractName}" maxlength="200" onkeyup="clearSelect(this)" readOnly /> 
            		 	 <input type="button" class="iconSearch" value="" id="selectContract"></span>
            			</td>
            		[/#if]
						<th></th>
						<td></td>
						<th></th>
						<td></td>
					</tr>
					<tr>
						<th>
							${message("退货原因")}:
						</th>
						<td colspan="7">
							<textarea name="reason" maxlength="240" class="text" id="reason">${b2bReturns.reason}</textarea>
						</td>
					</tr>
				</table>
				<div class="title-style">${message("收货信息")}
					<div class="btns">
						[#if b2bReturns.status==0] 
						<a href="javascript:;" id="addReceiveAddress" class="button">更换收货信息</a> 
						[/#if]
					</div>
				</div>
				<table class="input input-edit">
					<tr class="border-L1">
						<th>
							<span class="requiredField">*</span>${message("收货人")}:
						</th>
						<td>
							<input type="text" class="text" name="consignee" value="${b2bReturns.consignee}" btn-fun="clear" readOnly>
						</td>
						<th>
							<span class="requiredField">*</span>${message("收货人电话")}:
						</th>
						<td>
							<input type="text" class="text" name="consigneeMobile" value="${b2bReturns.consigneeMobile}" btn-fun="clear" readOnly>
						</td>
						<th>
							${message("收货地区邮编")}:
						</th>
						<td>
							<input type="text" class="text" name="zipCode" value="${b2bReturns.zipCode}" btn-fun="clear" readOnly>
						</td>
						<th>
							${message("地址外部编码")}:
						</th>
						<td>
							<input type="text" class="text" name="addressOutTradeNo" value="${b2bReturns.addressOutTradeNo}" btn-fun="clear" readonly="readonly" />
						</td>
					</tr>
					<tr class="border-L1">
						<th>
							<span class="requiredField">*</span>${message("收货地区")}：
						</th>
						<td>
							<span class="search" style="position: relative"> 
								<input type="hidden" name="areaId" class="text areaId" value="${b2bReturns.area.id}" btn-fun="clear" /> 
								<input type="text" name="areaName" class="text areaName" value="${b2bReturns.area.name}" maxlength="200" onkeyup="clearSelect(this)" readOnly />
								<!-- <input type="button" class="iconSearch"  id="openArea"> -->
							</span>
						</td>
						<th>
							<span class="requiredField">*</span>${message("收货地址")}:
						</th>
						<td colspan="3">
							<input type="text" class="text" name="address" value="${b2bReturns.address}" btn-fun="clear" readOnly>
						</td>
						<th></th>
						<td></td>
					</tr>
				</table>
				<div class="title-style">${message("客户余额")}</div>
				<table class="input input-edit">
					<tr>
						<th>
							<span class="requiredField">*</span>${message("退款金额")}:
						</th>
						<td>
							<div>
								<input type="text" class="text" name="amount" value="${b2bReturns.amount}" minData="0" oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" readOnly>
							</div>
						</td>
					</tr>
					<tr>
						<th>
							${message("可用余额")}:
						</th>
						<td>
							<span class="red" id="bal">${currency(balance,true)}</span>
							<input type="hidden" name="storeBalance" id="storeBalance" value="${balance}" />
						</td>
						<th>
							${message("订单金额")}:
						</th>
						<td>
							<span class="red" id="amount">${currency(order.amount, true)}</span>
						</td>
						[#if flag==1]
						<th>
							${message("订单结算金额")}:
						</th>
						<td>
							<span class="red" id="saleAmount">${currency(0, true)}</span>
						</td>
						[#else]
						<th>
							${message("差额")}:
						</th>
						<td>
							<span class="red" id="chae">${currency(chae, true)}</span>
						</td>
						[/#if]
						<th>
							${message("余额")}:
						</th>
						<td>
							<span class="red" id="yue">${currency(yue, true)}</span>
						</td>
					</tr>
					<tr>
						<th>
							${message("授信")}:
						</th>
						<td>
							<span class="red" id="credit">${currency(credit, true)}</span>
						</td>
					</tr>
				</table>
				<div class="title-style">${message("退货明细")}:
					<div class="btns">
						[#if flag==null] 
							<a href="javascript:;" id="addProduct" class="button">选择产品</a> 
						[/#if]
					</div>
					[#if referenceOrder !=0] 
						<div class="btns">
							[#if flag==null] 
								<a href="javascript:;" id="findOrder" class="button">参考订单</a> 
							[/#if]
						</div>
					[/#if]	
					[#if referenceShipping !=0] 
						<div class="btns">
							[#if flag==null] 
								<a href="javascript:;" id="findShipping" class="button">参考发货单</a> 
							[/#if]
						</div>
					[/#if]	
				</div>
				<table id="table-m1"></table>
				<div class="title-style">${message("退货单汇总")}</div>
				<table class="input input-edit">
					<tr>
						<!-- 2019-05-16 冯旗 壁纸隐藏箱支，重量体积 -->
						[#if b2bReturns.sbu.id !=3]
						<th>
							${message("退货单箱数")}:
						</th>
						<td>
							<span id="totalBoxQuantity"></span>
						</td>
						<th>
							${message("退货单支数")}:
						</th>
						<td>
							<span id="totalBranchQuantity"></span>
						</td> 
						[/#if]
						<th>
							${message("退货单数量")}:
						</th>
						<td>
							<span id="totalQuantity"></span>
						</td> 
					</tr>
				</table>
				<div class="title-style">${message("全链路信息")}</div>
				<table id="table-full"></table>
				<div class="title-style">
					${message("附件信息")}
					<div class="btns">
						<a href="javascript:;" id="addAttach" class="button">添加附件</a>
					</div>
				</div>
				<table id="table-attach"></table>
				[#if b2bReturns.wfId != null]
				[#if gcpg||gcpgs]
				<div class="title-style">${message("工厂品管意见")}:[#if node.name?contains("品管")]<input type="button" class="bottonss tj" onclick="saveform(0)" value="提交"/>[/#if]</div>
				<table class="input input-edit">
					<tr>
						<th>${message("产品质量")}:</th>
						<td>
							<label><input type="radio" name="isQuality" value="0" [#if b2bReturns.isQuality==0]checked[/#if]/>正常</label>&nbsp;&nbsp;
				            <label><input type="radio" name="isQuality" value="1" [#if b2bReturns.isQuality==1]checked[/#if]/>异常</label>&nbsp;&nbsp;
						</td>
						<th>${message("意见")}:</th>
		                <td colspan="7">
		                    <textarea class="text" name="QCopinion" maxlength="4000" style="width:800px;height:160px;">${b2bReturns.QCopinion}</textarea>
		                </td>
					</tr>
				</table>
				[/#if]
				[#if zlzx||zlzxs]
				<div class="title-style">${message("质量中心意见")}:[#if node.name?contains("质量中心")]<input type="button" class="bottonss tj" onclick="saveform(1)" value="提交"/>[/#if]</div>
				<table class="input input-edit">
					<tr>
						<th>${message("产品质量")}:</th>
						<p>请填写提交产品质量,如果质量正常请通过,如果质量异常请驳回</p>
						<td>
							<label><input type="radio" name="isQuality1" value="0" [#if b2bReturns.isQuality1==0]checked[/#if]/>正常</label>&nbsp;&nbsp;
				            <label><input type="radio" name="isQuality1" value="1" [#if b2bReturns.isQuality1==1]checked[/#if]/>异常</label>&nbsp;&nbsp;
						</td>
						<th>${message("意见")}:</th>
		                <td colspan="7">
		                    <textarea class="text" name="QAopinion" maxlength="4000" style="width:800px;height:160px;">${b2bReturns.QAopinion}</textarea>
		                </td>
					</tr>
				</table>
				[/#if]
				[#if gccg||gccgs]
				<div class="title-style">${message("工厂仓管意见")}:[#if node.name?contains("仓管")]<input type="button" class="bottonss tj" onclick="saveform(2)" value="提交"/>[/#if]</div>
				<table class="input input-edit">
					<tr>
						<th>${message("产品验收数量(㎡)")}:</th>
						<td>
							<input type="text" name="productQuantity" id="productQuantity" value="${b2bReturns.productQuantity}" btn-fun="clear"/>
						</td>
					</tr>
				</table>
				[/#if]
				[/#if]
			</div>
			<div class="fixed-top">
				[#if flag == null] 
				<a href="/aftersales/b2b_returns/add/${code}.jhtml?sbuId=${b2bReturns.sbu.id}" class="iconButton" id="addButton"> 
					<span class="addIcon">&nbsp;</span>${message("1001")}
				</a> 
				[/#if] 
				[#if b2bReturns.status == 0 || b2bReturns.status == 1] 
				<input type="button" class="button cancleButton" value="${message("作废")}" onclick="cancel(this)" /> 
				[/#if] 
				[#if b2bReturns.status == 0] 
					[#if isCheckWf] 
						[#if b2bReturns.wfId==null] 
						<input type="button" class="button sureButton" value="${message("12501")}" onclick="check_wf(this)" /> 
						[/#if] 
					[#else] 
					<a href="javascript:void(0);" class="iconButton" id="shengheButton" onClick="check(this)">
						<span class="ico-shengheIcon">&nbsp;</span>${message("审核")}
					</a>
					[/#if] 
				[/#if]
				[#if flag == 2 && b2bReturns.status == 1] 
				<a href="javascript:void(0);" class="iconButton" id="shengheButton" onClick="stockIn(this)">
					<span class="ico-shengheIcon">&nbsp;</span>${message("入库")}
				</a>
				[/#if]
				[#if b2bReturns.status == 0] 
				<input type="button" class="button sureButton" onClick="save(this)" value="${message("1013")}" />
				[/#if] 
				[#if b2bReturns.status == 1 || b2bReturns.status == 4 || b2bReturns.status == 5] 
				<input type="button" class="button pdfButton" value="${message("查看PDF")}" onclick="check_pdf(this,'${b2bReturns.id}')" /> 
				[/#if] 
				
				[#if apdateSaleOrgPrice !=0] 
					[#if b2bReturns.status == 1 ||  b2bReturns.status == 4 || b2bReturns.status == 5 ] 
						<a href="javascript:void(0);" class="button sureButton" onclick="apdateSaleOrgPrice(this)">${message("修改价格")}</a> 
			    	[/#if] 
			    [/#if] 
				<input type="button" onclick="location.reload(true);"class="button refreshButton ml15" value="${message("刷新")}">
			</div>
	</form>
	<div id="wf_area" style="width: 100%"></div>
</body>
</html>