<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("查看特价单")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript">
function editQty(t,e){
	if(extractNumber(t,3,false,e)){
		getItemPrice(t);
		countTotal();
	}
}
function editPrice(t,e){
	if(extractNumber(t,2,false,e)){
		getdiscount(t);
		countTotal();
	}
}
function projectChange() {
	var  myselect=document.getElementById("type");
	var index=myselect.selectedIndex ;
	var project=myselect.options[index].value;
	if(project == 3){
		document.getElementById("projectRequired").style.display="";
	}
}

function countTotal(){
	var $input = $("input.quantity");
	var total = 0;
	$input.each(function(){
		var $this = $(this);
		var $tr = $this.closest("tr");
		var $priceApply = $tr.find(".priceApply").val();
		var amount = 0;
		if( $priceApply  != 0 && $this.val() != 0){
			amount = Number($this.val())* $priceApply ;
		}
		if(isNaN(amount)){
			amount = 0;
		}
		total = total+amount;
	});
	$(".totalAmount").text(currency(total,true));
}
$().ready(function() {
	var $inputForm = $("#inputForm");
	var $selectMember = $("#selectMember");
	var $deleteProduct = $("a.deleteProduct");
	var $addProduct = $("#addProduct");
	var itemIndex = 1;
	
	
	
	// 表单验证
	$.validator.addClassRules({
		price: {
			required: true
		},
		quantity: {
			required: true
		}/*,
		storeId: {
			required: true
		},
		storeName: {
			required: true
		}*/
	});
	//打开选择产品界面
	$addProduct.click(function(){	
	var $storeId = $(".storeId").val();
	var type = $("#type option:selected").val();
	 var sbuId = $(".sbuId").val();
	var $this = $(this);
/* 	var proGrade = 1;
	if (type == 1) {
		 proGrade = 2;
	} */
	
	if(sbuId==""){
		$.message_alert('${message("请选择SBU")}');
		return false;
	}
//		if($storeId==""){
//			$.message_alert('${message("请选择客户")}');
//		}
		$addProduct.bindQueryBtn({
			type:'product',
			bindClick:false,
			title:'查询产品',
			url:'/product/product/selectProduct.jhtml?multi=2&storeId='+$storeId+'&isToOrder=true&isPart=0&sbuId='+sbuId,
			callback:function(rows){
				if(rows.length>0){
					var error = '';
// 					for (var i = 0; i < rows.length;i++) {
// 						var idH = $(".productId_"+rows[i].id).length;
// 						if(idH > 0){
// 							$.message_alert('产品【'+rows[i].name+'】已添加');
// 							return false;
// 						}
// 					}
					for (var i = 0; i < rows.length;i++) {
					    var row = rows[i];
						var shippingWarehouseId='';
						[#if shippingWarehouses?? && shippingWarehouses[0].id??]
						shippingWarehouseId=${shippingWarehouses[0].id};
						[/#if]
						row.str=row.product_category_id+'_'+row.id+'_'+shippingWarehouseId+'_'+row.product_grade;
						$mmGrid.addRow(row,null,1);
							
					}
				}
			}
		});
		
	})
	
	//打开选择产品系列界面
	$("#addProductCategory").click(function(){	
		$addProduct.bindQueryBtn({
			type:'product',
			bindClick:false,
			title:'查询产品系列',
			url:'/product/product_category/select_productCategory.jhtml?multi=2',
			callback:function(rows){
				if(rows.length>0){
					var error = '';
// 					for (var i = 0; i < rows.length;i++) {
// 						var idH = $(".productId_"+rows[i].id).length;
// 						if(idH > 0){
// 							$.message_alert('产品【'+rows[i].name+'】已添加');
// 							return false;
// 						}
// 					}
					for (var i = 0; i < rows.length;i++) {
						var row = rows[i];
						row.product_category_id=row.id;
						row.product_category_name=row.name;
						row.id=null;
						row.name='';
						var shippingWarehouseId='';
						[#if shippingWarehouses?? && shippingWarehouses[0].id??]
						shippingWarehouseId=${shippingWarehouses[0].id};
						[/#if]
						row.str=row.product_category_id+'_0_'+shippingWarehouseId+'_0_';
						$mmGrid.addRow(row,null,1);
						
					}
				}
			}
		});
	})
	var itemIndex = 0;
	var cols = [
		{ title:'${message("发货仓")}', align:'center',name:'account_type',width:130  ,renderer: function(val,item,rowIndex){			
			var html = '<select name="priceApplyItems['+itemIndex+'].shippingWarehouse.id" class="text shippingWarehouseId" >'
				[#list shippingWarehouses as shippingWarehouse]
					+'<option value="${shippingWarehouse.id}" >${shippingWarehouse.value}</option> '
				[/#list]
				+'</select>';
			return html;
		}},
		{ title:'${message("产品系列")}', align:'center',width:160 , renderer: function(val,item,rowIndex){
			var id=0;
			if(item.id!=null){
				id=item.id;
			}
			return '<input type="hidden" shippingWarehouse_id="0" str="'+item.str+'" name="priceApplyItems['+itemIndex+'].productCategory.id" class="productCategoryId productId_'+item.product_category_id+' productId" product_id="'+id+'" product_category_id="'+item.product_category_id+'" value="'+item.product_category_id+'">'+item.product_category_name;
		}},
		{ title:'${message("产品名称")}', align:'center',width:160 , renderer: function(val,item,rowIndex){
			var name='';
			if(item.name!=null){
				name=item.name;
			}
			var id=0;
			if(item.id!=null){
				id=item.id;
			}
			return '<input type="hidden" name="priceApplyItems['+itemIndex+'].product.id" class=" productId" value="'+id+'">'+name;
		}},
		{ title:'${message("产品型号")}', name:'model', align:'center',width:160 },
		{ title:'${message("12211")}', name:'vonder_code', align:'center',width:160 },
		{ title:'${message("产品描述")}', name:'description', align:'center',width:160 },
		{ title:'${message("产品等级")}',name:'product_grade', align:'center', renderer: function(val,item,rowIndex){
			var str1 = '';
			var str2 = '';
			var str3 = '';
			/* var str4 = ''; */
				if(val==1)str1 = 'selected';
				if(val==2)str2 = 'selected';
				if(val==3)str3 = 'selected';
				/* if(val==4)str4 = 'selected'; */
			var html = '<select name="priceApplyItems['+itemIndex+'].productGrade" class="text grade" id ="productGrade">'
			+'<option value="1" '+str1+'>优等品</option>'
			+'<option value="2" '+str2+'>二等品</option>'
			+'<option value="3" '+str3+'>一等品</option>'
			/* +'<option value="4" '+str4+'>无等级</option>' */
			+'</select>';
			return html;
		}},
		{ title:'${message("销售价")}', name:'member_price' ,align:'center', width:90,renderer: function(val){
			if(val!=undefined && val!=null && val!=''){
				return '<span class="red memberPriceText">'+currency(val,true)+'</span><input type="hidden" class="text memberPrice" name="priceApplyItems['+itemIndex+'].memberPrice" value="'+val+'">';
			}
			else{
				return '-';
			}
		}},
		{ title:'${message("申请价格")}', align:'center',renderer: function(val,item,rowIndex){
			var html = '<div class="nums-input ov">'+
            	'<input type="button" class="b decrease " value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice(this.nextSibling,event)">'+
            	'<input type="text"  class="t price priceApply"  name="priceApplyItems['+itemIndex+'].price" value="" minData="0" oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)" >'+
            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice(this.previousSibling,event)">'+
        	'</div>';
        	return html;
			//return '<input type="number" oninput="getdiscount(this)" step="0.01" min=0  name="priceApplyItems['+itemIndex+'].price" class="text price priceApply">';
		}},
 		{ title:'${message("平台结算价原价")}',name:'sale_org_price', align:'center', renderer: function(val,item,rowIndex){
 			return '<span class="red saleOrgPriceText">'+currency(val,true)+'</span><input type="hidden"  name="priceApplyItems['+itemIndex+'].saleOrgPrice" class="text saleOrgPrice" value="'+val+'">';
 		}},
		{ title:'${message("平台结算价特价")}', align:'center',renderer: function(val,item,rowIndex){
			var html = '<div class="nums-input ov">'+
            	'<input type="button" class="b decrease " value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice(this.nextSibling,event)">'+
            	'<input type="text"  class="t price saleOrgSalesPrice"  name="priceApplyItems['+itemIndex+'].saleOrgSalesPrice" value="" minData="0" oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)" >'+
            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice(this.previousSibling,event)">'+
        	'</div>';
        	return html;
		}},
// 		{ title:'${message("折扣")}',name:'discount', align:'center',renderer:function(val,item,rowIndex,obj){
// 			var discount = currency(0);
// 			return '<span class="discount">'+discount+'</span>';
// 		}},
// 		{ title:'${message("申请数量")}', name:'quantity', align:'center',renderer: function(val,item,rowIndex){
// 			var html = '<div class="nums-input ov">'+
//             	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editQty(this.nextSibling,event)">'+
//             	'<input type="text"  class="t quantity"  name="priceApplyItems['+itemIndex+'].quantity" value="" minData="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" >'+
//             	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty(this.previousSibling,event)">'+
//         	'</div>';
//         	return html;
// 		}},
// 		{ title:'${message("金额")}',align:'center',renderer:function(val,item,rowIndex,obj){
// 			var item_prices = currency(0);
// 			return '<span class="red item_prices">'+currency(item_prices,true)+'</span>';
// 		}},
		
// 		{ title:'${message("开始时间")}', name:'startDate', align:'center',renderer: function(val,item,rowIndex){
// 			return '<input  id="priceApplyItems['+itemIndex+'].startDate" name="priceApplyItems['+itemIndex+'].startDate" class="text startDate" value="${startDate}" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\',startDate:\'%y-%M-%d\', maxDate: \'#F{$dp.$D(\\\''+'priceApplyItems['+itemIndex+'].endDate\\\')}\'});" type="text" btn-fun="clear"/>'
// 		}},
// 		{ title:'${message("结束时间")}', name:'endDate', align:'center',renderer: function(val,item,rowIndex){
// 			return '<input id="priceApplyItems['+itemIndex+'].endDate" name="priceApplyItems['+itemIndex+'].endDate" class="text endDate" value="${endDate}" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\',startDate:\'%y-%M-%d\', minDate: \'#F{$dp.$D(\\\''+'priceApplyItems['+itemIndex+'].startDate\\\')}\'});" type="text" btn-fun="clear"/>'
// 		}},
		{ title:'${message("最小开单数量/m2")}',name:'min_quantity',align:'center',renderer:function(val,item,rowIndex,obj){
			var html = '<div class="nums-input ov">'+
        	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editQty(this.nextSibling,event)">'+
        	'<input type="text"  class="t quantity"  name="priceApplyItems['+itemIndex+'].minQuantity" value="" minData="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" >'+
        	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty(this.previousSibling,event)">'+
    	'</div>';
    	return html;
			
		}},
		{ title:'${message("最大累计数量/m2")}',name:'max_quantity',align:'center',renderer:function(val,item,rowIndex,obj){
			var html = '<div class="nums-input ov">'+
        	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editQty(this.nextSibling,event)">'+
        	'<input type="text"  class="t quantity"  name="priceApplyItems['+itemIndex+'].maxQuantity" value="" minData="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" >'+
        	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty(this.previousSibling,event)">'+
    	'</div>';
    	return html;
		}},
		{ title:'${message("操作")}', align:'center', width:60, renderer: function(val,item,rowIndex){
			itemIndex++;
			return '<a href="javascript:;" class="btn-delete" onclick="deleteProduct(this)">删除</a>';
		}},
	];
	$mmGrid = $('#table-m1').mmGrid({
		height:'auto',
        cols: cols,
        checkCol:false,
        fullWidthRows: true
    }); 
	
	
	$(".shippingWarehouseId").live("change", function() {
		var $tr=$(this).closest("tr");
		var $productCategoryId=$tr.find(".productCategoryId");
		var productCategoryId=$productCategoryId.attr("product_category_id");
		var productId=$productCategoryId.attr("product_id");
		var shippingWarehouseId=$(this).val();
		//$productCategoryId.attr("str",productCategoryId+"_"+productId+"_"+shippingWarehouseId);
		var storeId = $(".storeId").val();
    	var saleOrgId = $(".saleOrgId").val();
    	var productGrade = $tr.find(".grade").val();
    	$productCategoryId.attr("str",productCategoryId+"_"+productId+"_"+shippingWarehouseId+"_"+productGrade);//唯一标识
    	
		//查询当前库存的商品在价格表的对应价格
			ajaxSubmit('',{
					method:'post',
					url:'/product/product_price_head/findProductPrice.jhtml',
					data:{storeId:storeId,productId:productId,saleOrgId:saleOrgId,warehouseShippingId:shippingWarehouseId,productGrade:productGrade},
					callback:function(resultMsg) {
						var data = resultMsg.objx;  
						if (data !=null) {
						   var 	price = data.store_member_price;//经销商价格
						   var  sale_org_price = data.sale_org_price;//结算价
  							if (isNaN(data.store_member_price)) {
  								$.message_alert("${message("请在价格表维护该产品价格")}");
  					      		return false;
							}else {
								$tr.find("input.saleOrgPrice").val(data.sale_org_price);
								$tr.find(".saleOrgPriceText").text(currency(data.sale_org_price,true));
								$tr.find("input.memberPrice").val(data.store_member_price);
								$tr.find(".memberPriceText").text(currency(data.store_member_price,true));
							}
					}else {
						$.message_alert("${message("请在价格表维护该产品价格")}");							
					}
						
					}
				}); 
	});
	
	$(".grade").live("change", function() {
		var $tr=$(this).closest("tr");
		var $productCategoryId=$tr.find(".productCategoryId");
		var productCategoryId=$productCategoryId.attr("product_category_id");
		var productId=$productCategoryId.attr("product_id");
		var productGrade=$(this).val();
		var storeId = $(".storeId").val();
    	var saleOrgId = $(".saleOrgId").val();
    	var shippingWarehouseId = $tr.find(".shippingWarehouseId").val();
    	$productCategoryId.attr("str",productCategoryId+"_"+productId+"_"+shippingWarehouseId+"_"+productGrade);
    	
		//查询当前库存的商品在价格表的对应价格
			ajaxSubmit('',{
					method:'post',
					url:'/product/product_price_head/findProductPrice.jhtml',
					data:{storeId:storeId,productId:productId,saleOrgId:saleOrgId,warehouseShippingId:shippingWarehouseId,productGrade:productGrade},
					callback:function(resultMsg) {
						var data = resultMsg.objx;  
						if (data !=null) {
						   var 	price = data.store_member_price;//经销商价格
						   var  sale_org_price = data.sale_org_price;//结算价
  							if (isNaN(data.store_member_price)) {
  								$.message_alert("${message("请在价格表维护该产品价格")}");
  					      		return false;
							}else {
								$tr.find("input.saleOrgPrice").val(data.sale_org_price);
								$tr.find(".saleOrgPriceText").text(currency(data.sale_org_price,true));
								$tr.find("input.memberPrice").val(data.store_member_price);
								$tr.find(".memberPriceText").text(currency(data.store_member_price,true));
							}
					}else {
						$.message_alert("${message("请在价格表维护该产品价格")}");							
					}
						
					}
				}); 
	});
    
    $("#submit_button").click(function(){
		var count = $("input.productId").length;
		if(count<1){
			$.message_alert("特价明细不能少于一条");
		}else{
			var count=[0];
			$(".productCategoryId").each(function(){
				var required=$(this).attr("str");
				if($.inArray(required,count)==-1){
					count.push(required);
				}else{
					$.message_alert("已存在信息相同的特价明细");
					return false;
				}
				
			});
			$("form").valid();
			$("form").submit();
		}
	});
    
    $("form").bindAttribute({
    	isConfirm:true,
	    callback: function(resultMsg){
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.href= '/b2b/priceApply/edit/${code}.jhtml?id='+resultMsg.objx;
			});
	    }
	 });
	 //查询客户
	 $("#selectStore").click(function(){
    	var saleOrgId = $(".saleOrgId").val();
		$("#selectStore").bindQueryBtn({
			type:'store',
			title:'${message("查询客户")}',
			bindClick:false,
			url:'/member/store/select_store.jhtml?type=distributor&isMember=1',
			callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
					$(".storeName").val(row.name);
					$(".storeId").val(row.id);
					$(".engineeringName").val('');
					$(".engineeringId").val('');
					$mmGrid.load();
					$(".saleOrgName").text(row.sale_org_name);
					$(".saleOrgId").val(row.sale_org_id);
				}
			}
		});
	});
	 
	//查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml'
	});
	
	//打开选择工程界面
	$("#selectEngineering").click(function(){	
		var $storeId = $(".storeId").val();
		if($storeId==""){
			$.message_alert('${message("请选择客户")}');
		}else{
		$("#selectEngineering").bindQueryBtn({
			type:'engineering',
			bindClick:false,
			title:'${message("查询工程")}',
			url:'/basic/engineering/select_list.jhtml?storeId='+$storeId,
		});
		}
	})
	
	$("#type").change(function(){
		var type=$("#type").val();
		if(type==3){
			$("#selectEngineering").removeAttr("disabled");
			$(".engineeringId").removeAttr("disabled");
			$(".engineeringName").removeAttr("disabled");
		}else{
			$("#selectEngineering").attr("disabled","disabled");
			$(".engineeringId").attr("disabled","disabled");
			$(".engineeringName").attr("disabled","disabled");
			$(".engineeringId").val('');
			$(".engineeringName").val('');
		}
	});
	
	
	/**初始化附件*/
    var attachIdnex = 0;
	var cols = [				
    	{ title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex,obj){
    			var url = item.url;
				var fileObj = getfileObj(item.name);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['twContractAttachs['+attachIdnex+'].fileUrl']=url;
				hideValues['twContractAttachs['+attachIdnex+'].suffix']=fileObj.suffix;
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : '',
					textName:'twContractAttachs['+attachIdnex+'].name',
					hideValues:hideValues
				});
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:590 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text file_memo" name="twContractAttachs['+attachIdnex+'].memo" >'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
			attachIdnex++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}}
	];	
	var $amGrid=$('#table-attach').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        checkCol: false,
        autoLoad: true
    });  
    var $addAttach = $("#addAttach");
	var attachIdnex = 0;
	var option1 = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row = data[i].file_info;
				$amGrid.addRow(row,null,1);
	        }
			
        }
    }
    $addAttach.file_upload(option1);
     var $deleteAttachment = $(".deleteAttachment");
	$deleteAttachment.live("click", function() {
		var $this = $(this);
		$this.closest("tr").remove();
	});
	
});
function deleteProduct(e){
	var index = $(e).closest("tr").index();
	$.message_confirm('您确定要删除吗？',function(){
		$mmGrid.removeRow(index);
		countTotal();
	})
}
function getdiscount(e){
	var priceApply=$(e).val();
	var $tr=$(e).closest("tr");
	var productPrice=$tr.find(".productPrice").val();
	var quantity=$tr.find(".quantity").val();
	var num=0.00;
	var itemPrices = 0;
	if(productPrice=="0"){
		num=0.00;
	}else if(priceApply!=""&&priceApply>0){
		num =(priceApply/productPrice)*10;
	}
	$tr.find(".discount").html(currency(num));
	
	if(priceApply != "" && priceApply>0 && quantity != "" && quantity >0){
		itemPrices = priceApply * quantity;
	}
	$tr.find(".item_prices").html(currency(itemPrices,true));
}   
function getItemPrice(e){
	var quantity=$(e).val();
	var $tr=$(e).closest("tr");
	var priceApply=$tr.find(".priceApply").val();
	var itemPrices = 0;
	if(priceApply != "" && priceApply>0 && quantity != "" && quantity >0){
		itemPrices = priceApply * quantity;
	}
	$tr.find(".item_prices").html(currency(itemPrices,true));
}   

</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增特价单")}
	</div>
	<form id="inputForm" action="/b2b/priceApply/save.jhtml" method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="id" id="pid" value="${priceApply.id}" />
		<div class="tabContent">
		<table class="input input-edit" >
			<tr>
				<th>
					${message("特价单编号")}:
				</th>
				<td>
					${priceApply.sn}
				</td>
				<th>${message("客户")}:</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="store.id" value="[#if storeMember.memberType==1]${store.id}[/#if]" class="text storeId" btn-fun="clear"/>
					<input type="text" name="storeName" class="text storeName" value="[#if storeMember.memberType==1]${store.name}[/#if]" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
					<input type="button" class="iconSearch" value="" id="selectStore">
					</span>
				</td>
				<th>${message("单据状态")}:</th>
				<td>
				</td>
				<th>${message("流程状态")}:</th>
				<td>
				</td>
			</tr>
			<tr>
				<th>
					${message("特价类型")}:
				</th>
				<td >
					<select id="type" class="text" name="type" onchange="projectChange()"> 
						<option value="0" selected>促销</option>
						<option value="1">二等品</option>
						<option value="2">定制</option>
						<option value="3">工程</option>
					</select>
				</td>
				<th>
				
				<span class="requiredField" style="display:none;" id="projectRequired">*</span>
				
					${message("工程")}:
				</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="engineering.id" class="text engineeringId" btn-fun="clear"/>
					<input type="text" name="engineeringName" class="text engineeringName" maxlength="200" onkeyup="clearSelect(this)" disabled  readOnly/>
					<input type="button" class="iconSearch" value="" id="selectEngineering" disabled>
					</span>
				</td>
				<th>
					<span class="requiredField">*</span>${message("开始时间")}:
				</th>
				<td id="start">
					<input type="text" class="text startDate" name="startDate" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'});" btn-fun="clear"/>
				</td>
				
				<th>
					<span class="requiredField">*</span>${message("结束时间")}:
				</th>
				<td id="end">
					<input type="text" class="text endDate" name="endDate" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'});" btn-fun="clear"/>
				</td>
			</tr>
			<tr>
				<th>${message("机构")}:</th>
				<td>
					
					<span class="search" style="position:relative">
					<input type="hidden" name="saleOrg.id" class="text saleOrgId" btn-fun="clear" value="[#if storeMember.memberType==1]${store.saleOrg.id}[/#if]"/>
					<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" disabled  readOnly/>
					<input type="button" class="iconSearch" value="" id="selectSaleOrg">
					</span>
					
			<!--  	<input type="hidden" name="saleOrg.id" class="text saleOrgId" btn-fun="clear" value="[#if storeMember.memberType==1]${store.saleOrg.id}[/#if]"/>
					<span class="saleOrgName">[#if storeMember.memberType==1]${store.saleOrg.name}[/#if]</span> -->
				</td>
				<th>
					${message("申请人")}:
				</th>
				<td></td>
				<th>
					${message("1011")}:
				</th>
				<td>
					${priceApply.createDate}
				</td>
				  <th>${message("Sbu")}:</th>
            	 <td>
            	<input type="hidden" name="sbuId" class="text sbuId" id="sbuId" btn-fun="clear" value="${sbu.id}"/>
            	<span   id="sbuName">
            	 ${sbu.name}
            	</span>
             </td>
				<!-- 	<th>
					<span class="requiredField">*</span>${message("sbu")}:
				</th>
				<td>
					<select id="sbuId" name="sbuId" class="text">
						[#list sbus as sbu]
						<option value="${sbu.sbu.id}"[#if sbuIds==sbu.sbu.id]selected[/#if]>${sbu.sbu.name}</option>
						[/#list]
					 </select>
				</td> -->
			</tr>
			 <tr>
                <th>${message("申请说明")}:</th>
                <td colspan="7"><textarea name="memo" class="text" id="memo"></textarea></td>
            </tr>
           </table>	
			<div class="title-style">
				${message("特价单明细")}:
				<div class="btns">
				<!-- 	<a href="javascript:;" id="addProductCategory" class="button">选择产品系列</a>  -->
					<a href="javascript:;" id="addProduct" class="button">选择产品</a>
		    	</div>
			</div>
			<table id="table-m1"></table>
			
			<div class="title-style">
				${message("附件信息")}:
				<div class="btns">
					<a href="javascript:;" id="addAttach" class="button">添加附件</a>
		    	</div>
			</div>
			<table id="table-attach"></table>
	</div>
	<div class="fixed-top">
		<input type="button" id="submit_button" class="button sureButton" value='${message("保存")}'>
		<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
	</div>
	</form>
</body>
</html>