<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("订单价格申请")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
function initWfStates(){
		wfStates = {};
		[#list wfStates as wfState]
			wfStates['${wfState}'] = '${message('22222222'+wfState)}';
		[/#list]
	}
function edit(id){
	parent.change_tab(0,'/b2b/priceApply/edit/${code}.jhtml?id='+id+'&isCheck=${isCheck}');
}
function add(){
	parent.change_tab(0,'/b2b/priceApply/add/${code}.jhtml?sbuId=${sbuId}');
}
$().ready(function() {
	/**初始化多选的下拉框*/
	initMultipleSelect();
	
	initWfStates();
	
	var order_sttuss = {'0':'已保存', '1':'已提交', '2':'已审核', '4':'已作废'};
	var wf_state ={'0':'未启动','1':'审核中','2':'已完成','3':'驳回',};
	var types={'0':'促销','1':'二等品','2':'定制','3':'工程'}
	var productGrade = {'1':'优等品','2':'二等品','3':'一等品'}
	var cols = [
		{ title:'${message("特价单编号")}', name:'sn' , align:'center', renderer: function(val,item,rowIndex){
			return '<a href="javascript:void(0);" onClick="edit('+item.id+')" class="red">'+val+'</a>';
		}},
		{ title:'${message("客户名称")}', name:'store_name' , align:'center' },
		{ title:'${message("机构")}', name:'sale_org_name' , align:'center' },
		{ title:'${message("sbu")}', name:'sbu_name' , align:'center' },
		{ title:'${message("特价类型")}', name:'' ,align:'center',renderer:function(val,item){
			var result = types[item.type];
			if(result!=undefined)return result;
			
		}},
		{ title:'${message("工程")}', name:'engineering_name' , align:'center' },
		{ title:'${message("产品系列")}', name:'product_category_name' , align:'center',isLines:true },
		{ title:'${message("产品名称")}', name:'name' , align:'center',isLines:true },
		{ title:'${message("产品等级")}',name:'product_grade',align:'center',isLines:true,renderer: function(val,item,rowIndex){
			if (val != null && val != "") return productGrade[val];
		}},
		{ title:'${message("产品型号")}', name:'model' , align:'center',isLines:true },
		{ title:'${message("12211")}', name:'vonder_code' , align:'center',isLines:true },
		{ title:'${message("产品描述")}', name:'description', align:'center',isLines:true },
		{ title:'${message("销售价")}', name:'member_price' , align:'center' ,isLines:true, renderer: function(val){
			if(val!='' ){
				return '<span class="red">'+currency(val,true)+'</span>';
			}
		}},
		{ title:'${message("申请价格")}', name:'item_price' , align:'center',isLines:true , renderer: function(val){
			if(val!='' ){
				return '<span class="red">'+currency(val,true)+'</span>';
			}
		}},
		{ title:'${message("平台结算价原价")}', name:'sale_org_price' , align:'center' ,isLines:true, renderer: function(val){
			if(val!='' ){
				return '<span class="red">'+currency(val,true)+'</span>';
			}
		}},		
		 { title:'${message("平台结算价特价")}', name:'sale_org_sales_price' , align:'center' ,isLines:true, renderer: function(val){
			if(val!='' ){
				return '<span class="red">'+currency(val,true)+'</span>';
			}
		}},
// 		{ title:'${message("申请数量")}', name:'quantity' , align:'center',isLines:true },
// 		{ title:'${message("已使用数量")}', name:'used_quantity' , align:'center',isLines:true },
// 		{ title:'${message("会员价")}',name:"member_price",align:'center',isLines:true,renderer: function(val){
// 			if(val!='' ){
// 				return '<span class="red">'+currency(val,true)+'</span>';
// 			}
// 		}},
// 		{ title:'${message("金额")}',name:"item_prices",align:'center',isLines:true,renderer: function(val){
// 			if(val!='' ){
// 				return '<span class="red">'+currency(val,true)+'</span>';
// 			}
// 		}},
// 		{ title:'${message("总金额")}', name:'total_amount' , align:'center' ,renderer: function(val){
// 			if(val!='' ){
// 				return '<span class="red">'+currency(val,true)+'</span>';
// 			}
// 		}},
		{ title:'${message("单据状态")}', name:'' ,align:'center',renderer:function(val,item){
			var result="";
			if (item.doc_status == 0) result = '<span class="blue">已保存</span>';
			else if (item.doc_status==1) result = '<span class="blue">处理中</span>';
			else if (item.doc_status == 2) result = '<span class="green">已处理</span>';
			else if (item.doc_status == 3) result = '<span class="red">已失效</span>';
			return result;
		}},
		[#if isCheckWf]
		{ title:'${message("流程状态")}', name:'' ,align:'center',renderer:function(val,item){
			var result = wfStates[item.wf_state];
			if(result!=undefined)return result;
			
		}},
		[/#if]
		{ title:'${message("开始时间")}', name:'start_date' , align:'center',renderer:function(val){
			if(val !=null && val.length>10 ){
				var str = val;
				return str.substring(0,10);
			}
		}},
		{ title:'${message("结束时间")}', name:'end_date' , align:'center',renderer:function(val){
			if(val !=null && val.length>10 ){
				var str = val;
				return str.substring(0,10);
			}
		}},
		{ title:'${message("申请人")}', name:'store_member_name' , align:'center' },
		{ title:'${message("备注")}', name:'memo' , align:'center',width:220 },
		{ title:'${message("创建日期")}', name:'create_date' ,align:'center' },

	];
	$mmGrid = $('#table-m1').mmGrid({
		autoLoad:true,
        cols: cols,
        fullWidthRows:true,
        url: '/b2b/priceApply/list_data.jhtml',
         lineRoot:"apply_items",
        method: 'post',
        params:function(){
        	return $("#listForm").serializeObject();
        },
        root: 'content',
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });
    
    //查询客户
	$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'/member/store/select_store.jhtml?type=distributor&isMember=1&isSelect=0'
	});
	
	//查询客户
	$("#selectStoreMember").bindQueryBtn({
		type:'storeMember',
		title:'${message("查询用户")}',
		url:'/member/store_member/select_store_member.jhtml?isMember=1'
	});
	
	$("#selectProduct").bindQueryBtn({
		type:'product',
		title:'${message("查询产品")}',
		url:'/product/product/selectProduct.jhtml'
	});
	
	
	//查询SBU
	$("#selectSbu").bindQueryBtn({
		type:'sbu',
		title:'${message("查询Sbu")}',
		url:'/b2b/sbu/select_sbu.jhtml'
	});
});

//条件导出		    
function segmentedExport(e){
	var needConditions = true;//至少一个条件
	var page_url = '/b2b/priceApply/to_condition_export.jhtml';//分页导出统计页面
	var url = '/b2b/priceApply/condition_export.jhtml';//导出的方法
	conditions_export(e,{needConditions:needConditions,page_url:page_url,url:url});
}

//选择导出
function exportExcel(t){
	var param = $mmGrid.serializeSelectedIds();//参数
	var tip = '${message("请选择导出的特价单！")}';//提示
	var url = '/b2b/priceApply/selected_export.jhtml';//导出的方法
  	select_export(t,{tip:tip, param:param, url:url});
}
function productPriceImport(e){	
	excel_import(e,{
		title:"${message("特价单")}",
		url:"/b2b/priceApply/import_excel.jhtml",
		template:"/resources/template/product/productPrice.xls",
		callback:function(){
			$("#searchBtn").click();
		}
	})
}	
</script>
</head>
<body>
	<form id="listForm" action="/b2b/priceApply/list.jhtml" method="get">
	<input type="hidden" name="isCheck" id="isCheck" value="${isCheck}" >
	<input type="hidden" name="objTypeId" value="${objTypeId}" >
	<input type="hidden" name="objid" value="${objid}" >
		<div class="bar">
			<div class="buttonWrap">
				<div class="flag-wrap flagImp-wrap">
					<a href="javascript:void(0);" class="iconButton" id="export1Button">
						<span class="impIcon">&nbsp;</span>导入导出
					</a>
					<ul class="flag-list">
						<!--<li><a href="javascript:void(0)" onclick="productPriceImport(this)"><i class="flag-imp01"></i>${message("导入")}</a></li>-->
						<li><a href="javascript:void(0)" onclick="exportExcel(this)"><i class="flag-imp02"></i>${message("选择导出")}</a></li>
						<li><a href="javascript:void(0)" onclick="segmentedExport(this)"><i class="flag-imp02"></i>${message("条件导出")}</a></li>
						
					</ul>
				</div>
				[#if isCheck == 1 && objid == null]
                   <a href="javascript:add();" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
	            [/#if]		
			</div>
			<div id="searchDiv">
        	<div id="search-content" >
        		<dl>	
					<dt><p>${message("特价单编号")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="sn" value =""/>
        			</dd>
        		</dl>
        		<dl>
        			<dd><input type="hidden" id="sbuId"	name="sbuId" value="${sbuId}"></dd>
        		</dl>
        		<dl>
        			<dt ><p>${message("单据状态")}：</p></dt>
        			<dd>
        				<div class="checkbox-style">
							<a href="javascript:void(0);" onclick="clearText(this)" class="deleteText close"></a>
					       	<input type="text" class="text pointer doStatus" value="${message("已保存;处理中;已处理")}" autocomplete="off" />
				       		<div class="statusList cs-box" data-value="off">
				       			<label><input  class="check js-iname" name="docStatus" value="0" type="checkbox" checked/>${message("已保存")}</label>
				       			<label><input  class="check js-iname" name="docStatus" value="1" type="checkbox" checked/>${message("处理中")}</label>
				       			<label><input  class="check js-iname" name="docStatus" value="2" type="checkbox" checked/>${message("已处理")}</label>
				       			<label><input  class="check js-iname" name="docStatus" value="3" type="checkbox"/>${message("已失效")}</label>
				      		</div>
						</div>
        			</dd>
        		</dl>
        		<dl>	
					<dt><p>${message("17007")}：</p></dt>
        			<dd >
        				<span class="search" style="position:relative">
							<input type="hidden" name="storeMemberId" class="text storeMemberId" id="storeMemberId" btn-fun="clear" />
							<input type="text" name="storeMemberName" class="text storeMemberName" maxlength="200" onkeyup="clearSelect(this)" id="storeMemberName" />
							<input type="button" class="iconSearch" value="" id="selectStoreMember">
						</span>
        			</dd>
        		</dl>
        		<dl>
        			<dt><p>${message("客户名称")}：</p></dt>
        			<dd >
        				<span class="search" style="position:relative">
							<input type="hidden" name="storeId" class="text storeId" id="storeId" btn-fun="clear" />
							<input type="text" name="storeName" class="text storeName" maxlength="200" onkeyup="clearSelect(this)" id="storeName" />
							<input type="button" class="iconSearch" value="" id="selectStore">
						</span>
        			</dd>
        		</dl>
        		<dl style= "margin-left:20px;">
			    		<dt><p>${message("12302")}：</p></dt> 
		    			<dd>
							<span style="position:relative">
							<input type="hidden" name="productId" class="text productId" btn-fun="clear" value=""/>
							<input type="text" name="productName" class="text productName" maxlength="200" value="" onkeyup="clearSelect(this)"/>
							<input type="button" class="iconSearch" value="" id="selectProduct">
							</span>
		    			</dd>
			    	</dl>
			    
			</div>
		<div class="search-btn" style="height:32px"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
		</div>
			
		</div>
		<div class="table-responsive">
	        <table id="table-m1"></table>
	        <div id="body-paginator">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</body>
</html>