<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("特价单")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript">
function view_iframe(url){
	var $view_iframe = $("#view_iframe");
	var src = $view_iframe.attr("src");
	if(src==""){
		$view_iframe.attr("src",url);
	}
}

$().ready(function() {
	var h = $(window).height();
	$("iframe").height(h-$("#tab").outerHeight());
	var objid = '${objid}';
	var menuId = '${menuId}';
	if(!isEmpty(objid)){
		url ='/b2b/priceApply/edit/${code}.jhtml?id='+objid;
		change_tab(0,url);
	}else if(!isEmpty(menuId)){
		url ='/b2b/priceApply/list/${code}.jhtml?sbuId=${sbuId}&menuId='+menuId;
		change_tab(1,url);
	}else{
		url ='/b2b/priceApply/add/${code}.jhtml?sbuId=${sbuId}';
		change_tab(0,url);
	}
});

function change_tab(index,url,flag){
	$("#tab input").removeClass("current").eq(index).addClass("current");
	var $tabContent = $(".tabContent").hide();
	var $iframe = $tabContent.eq(index).show().find("iframe");
	if(flag==undefined){
		$iframe.attr("src",url);
	}else{
		 if($iframe.attr("src")=="" && url!=""){
			$iframe.attr("src",url);
		}
	}
}
</script>
</head>
<body style="overflow:hidden;">
	<div> 
		 <ul id="tab" class="tab tab-first">
		 	<li>
	            <input type="button" value="${message("常规")}" [#if objid==null]onclick="change_tab(0,'/b2b/priceApply/add/${code}.jhtml?sbuId=${sbuId}',1)"[#else]onclick="change_tab(0,'/b2b/priceApply/edit/${code}.jhtml?id=${objid}',1)"[/#if]>
	        </li>
  			<li [#if menuId==null]style="display:none;"[/#if]>
	            <input type="button" value="${message("列表")}" onclick="change_tab(1,'/b2b/priceApply/list/${code}.jhtml?menuId=${menuId}&sbuId=${sbuId}',1)">
	        </li>
	    </ul>
	    <div class="tabContent" style="display:none;">
			<iframe src="" style="width:100%;"></iframe>
		</div>
		<div class="tabContent" style="display:none;" >
			<iframe src="" style="width:100%;"></iframe>
		</div>
	</div>
</body>
</html>