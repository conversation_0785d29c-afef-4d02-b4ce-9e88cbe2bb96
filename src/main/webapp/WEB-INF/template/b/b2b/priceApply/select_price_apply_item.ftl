<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("查询产品政策")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
$().ready(function() {
	var cols = [
		{ title:'${message("特价单号")}', name:'sn', align:'center'},
		{ title:'${message("特价类型")}', name:'type_name', align:'center'},
		{ title:'${message("工程编号")}', name:'e_sn', align:'center'},
		{ title:'${message("工程名称")}', name:'e_name', align:'center'},
		{ title:'${message("产品系列")}', name:'product_category_name', align:'center'},
		{ title:'${message("产品名称")}', name:'product_name', align:'center'},
		{ title:'${message("特价价格")}', name:'price', align:'center'},
		{ title:'${message("开始时间")}', name:'start_date', align:'center'},
		{ title:'${message("结束时间")}', name:'end_date', align:'center'},
		{ title:'${message("最小数量")}', name:'min_quantity', align:'center'},
		{ title:'${message("最大数量")}', name:'max_quantity', align:'center'},
	];
	var multiSelect = false;
	[#if multi==2]
		multiSelect = true;
	[/#if]
	
	$mmGrid = $('#table-m1').mmGrid({
		multiSelect:multiSelect,
		autoLoad: true,
		fullWidthRows:true,
		checkByClickTd:true,
		rowCursorPointer:true,
        cols: cols,
        url: '/b2b/priceApply/select_price_apply_item_data.jhtml',
	    params:function(){
        	return $("#listForm").serializeObject();
        },
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });
});
function childMethod(){
   return $mmGrid.selectedRows();
};
</script>
</head>
<body  style="min-width: 0px;">
<form id="listForm" action="/b2b/priceApply/select_warehouse.jhtml" method="get">
	<input type="hidden" name="multi" value="${multi}">
	<input type="hidden" name="storeId" value="${storeId}">
	<input type="hidden" name="productId" value="${productId}">
	<input type="hidden" name="productCategoryId" value="${productCategoryId}">
	<input type="hidden" name="typeSystemDictId" value="${typeSystemDictId}">
	<input type="hidden" name="businessTypeName" value="${businessTypeName}">
	<div class="bar">	
		<div class="buttonWrap">
		</div>
		<div id="searchDiv">
	        <div id="search-content" >
		    	<!-- <dl>
		    		<dt ><p>${message("政策单号")}：</p></dt> 
	    			<dd>
	    				<input type="text" class="text"  name="sn" value =""  btn-fun="clear"/>
	    			</dd>
		    	</dl>
        		<dl>
		    		<dt ><p>${message("政策名称")}：</p></dt> 
	    			<dd>
	    				<input type="text" class="text" id="name" name="name" value =""  btn-fun="clear"/>
	    			</dd>
		    	</dl> -->
	        </div>
			<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
		</div>
	</div>
	<div class="table-responsive">	
		<table id="table-m1"></table>
        <div id="body-paginator" style="text-align:left;">
            <div id="paginator"></div>
        </div>
	</div>
</form>
</body>
</html>