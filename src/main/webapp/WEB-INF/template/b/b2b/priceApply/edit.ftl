<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8" />
    <title>${message("编辑特价单申请")}</title>
    <link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
    <script type="text/javascript" src="/resources/js/base/dialog.js"></script>
    <script type="text/javascript" src="/resources/js/base/global.js"></script>
    <script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
    <script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
    <script type="text/javascript" src="/resources/js/base/request.js"></script>
    <script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
    <link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
    <script type="text/javascript" src="/resources/js/base/file.js"></script>
    <script type="text/javascript" src="/resources/js/utils.js"></script>
    <script type="text/javascript">
    
	    function memberPrice(t,e){
	        if(extractNumber(t,2,false,e)){
	            getdiscount(t);
	        }
	    }
    
        function minquantity(t){
            var a = $(".minquantity");
            var min = 0;
            for(i=0;i<=a.length;i++){
                var b = a.eq(i).val()==undefined?0:a.eq(i).val()*1;
                min += b;
            }
            $(".min").text(min.toFixed(6));
        }

        function maxquantity(){
            var a = $(".maxquantity");
            var max = 0;
            for(i=0;i<=a.length;i++){
                var b = a.eq(i).val()==undefined?0:a.eq(i).val()*1;
                max += b;
            }
            $(".max").text(max.toFixed(6));
        }

        function editQty(t,e){
            if(extractNumber(t,6,false,e)){
                getItemPrice(t);
                countTotal();
                maxquantity();
                minquantity();
            }
        }
        function editPrice(t,e){
            if(extractNumber(t,2,false,e)){
                getdiscount(t);
                countTotal();
            }
        }
        function countTotal(){
            var $input = $("input.quantity");
            var total = 0;
            $input.each(function(){
                var $this = $(this);
                var $tr = $this.closest("tr");
                var $priceApply = $tr.find(".priceApply").val();
                var amount = 0;
                if( $priceApply  != 0 && $this.val() != 0){
                    amount = Number($this.val())* $priceApply ;
                }
                if(isNaN(amount)){
                    amount = 0;
                }
                total = total+amount;
            });
            $(".totalAmount").text(currency(total,true));
        }
        $().ready(function() {
            var $deleteProduct = $("a.deleteProduct");
            var $selectMember = $("#selectMember");
            var itemIndex = ${priceApply.priceApplyItems?size}
            var $addProduct = $("#addProduct");

			[#if priceApply.wfId!=null]
		    	$("#wf_area").load("/act/wf/wf.jhtml?wfid=${priceApply.wfId}");
		    [/#if]


            $.validator.addClassRules({
                price: {
                    required: true
                },
                quantity: {
                    required: true
                }
            });

            //打开选择产品界面
            $addProduct.click(function(){
                var sbuId = $(".sbuId").val();
                var $storeId = $(".storeId").val();
                var type = $("#type option:selected").val();
                var proGrade = 1;
                if (type == 1) {
                    proGrade = 2;
                }
                if(sbuId==""){
                    $.message_alert('${message("请选择SBU")}');
                    return false;
                }
                $addProduct.bindQueryBtn({
                    type:'product',
                    bindClick:false,
                    title:'查询产品',
                    url:'/product/product/selectSpecialProduct.jhtml?multi=2&storeId='+$storeId+'&isToOrder=true&sbuId='+sbuId,
                    callback:function(rows){
                        if(rows.length>0){
                            var error = '';
                            for (var i = 0; i < rows.length;i++) {
                                var row = rows[i];
                                var shippingWarehouseId='';
								[#if shippingWarehouses?? && shippingWarehouses[0].id??]
									shippingWarehouseId=${shippingWarehouses[0].id};
                    			[/#if]
                                row.str=row.product_category_id+'_'+row.id+'_'+shippingWarehouseId+'_'+row.level_Id;
                                $mmGrid.addRow(row,null,1);
                            }
                        }
                    }
                });

            })


            //打开选择产品系列界面
            $("#addProductCategory").click(function(){
                $addProduct.bindQueryBtn({
                    type:'product',
                    bindClick:false,
                    title:'查询产品系列',
                    url:'/product/product_category/select_productCategory.jhtml?multi=2',
                    callback:function(rows){
                        if(rows.length>0){
                            var error = '';
                            for (var i = 0; i < rows.length;i++) {
                                var row = rows[i];
                                row.product_category_id=row.id;
                                row.product_category_name=row.name;
                                row.id=null;
                                row.name='';
                                var shippingWarehouseId='';
						[#if shippingWarehouses?? && shippingWarehouses[0].id??]
						shippingWarehouseId=${shippingWarehouses[0].id};
                        [/#if]
                                row.str=row.product_category_id+'_0_'+shippingWarehouseId+'_0_';
                                $mmGrid.addRow(row,null,1);
                            }
                        }
                    }
                });
            })

            var items = ${jsonStr};
            var cols = [
                { title:'${message("发货仓")}', align:'center',name:'shipping_warehouse',width:130  ,renderer: function(val,item,rowIndex){
                        var se='selected="selected"';
                        var html = '<select name="priceApplyItems['+itemIndex+'].shippingWarehouse.id" class="text shippingWarehouseId">';
						[#list shippingWarehouses as shippingWarehouse]
							if(${shippingWarehouse.id}==val){
			                    html+='<option value="${shippingWarehouse.id}" '+se+' >${shippingWarehouse.value}</option> ';
			                }else{
			                    html+='<option value="${shippingWarehouse.id}" >${shippingWarehouse.value}</option> ';
			                }
		                [/#list]
                        html+='</select>';
                        return html;
                }},
                { title:'${message("产品系列")}', align:'center',width:160 , renderer: function(val,item,rowIndex){
                        var id=0;
                        if(item.id!=null){
                            id=item.id;
                        }
                        if(item.str==undefined){
                            var shippingWarehouseId=item.shipping_warehouse==null?'':item.shipping_warehouse;
                            item.str=item.product_category_id+'_'+id+'_'+shippingWarehouseId+'_'+item.level_Id;
                        }
                        return '<input type="hidden" shippingWarehouse_id="0" str="'+item.str+'" name="priceApplyItems['+itemIndex+'].productCategory.id" class="productCategoryId productId_'+item.product_category_id+' productId" product_id="'+id+'" product_category_id="'+item.product_category_id+'" value="'+item.product_category_id+'">'+item.product_category_name;
                }},
                { title:'${message("产品名称")}', align:'center',width:160 , renderer: function(val,item,rowIndex,obj){
                        var name='';
                        if(item.name!=null){
                            name=item.name;
                        }
                        var id=0;
                        if(item.id!=null){
                            id=item.id;
                        }
                        if(obj==undefined){
                            return '<input type="hidden" name="priceApplyItems['+itemIndex+'].product.id" value="'+id+'" class="productId_'+id+'"><input type="hidden" name="priceApplyItems['+itemIndex+'].id" value="'+item.item_id+'">'+name;
                        }else{
                            return '<input type="hidden" name="priceApplyItems['+itemIndex+'].product.id" value="'+id+'" class="productId_'+id+'"><input type="hidden" name="priceApplyItems['+itemIndex+'].id" value="">'+item.name;
                        }
                }},
                { title:'${message("产品型号")}', name:'model', align:'center',width:160 },
                { title:'${message("12211")}', name:'vonder_code', align:'center',width:160 },
                { title:'${message("产品描述")}', name:'description', align:'center',width:160 },
                { title:'${message("产品等级")}',name:'product_grade',align:'center', renderer: function(val,item,rowIndex){
                        var str='selected="selected"';
            			var html='<select name="priceApplyItems['+itemIndex+'].productLevel.id" class="text grade" id ="productGrade">';
           				[#list productLevelList as products]
	           				if(${products.id}==item.level_Id){
	           					html+='<option value="${products.id}" '+str+' >${products.value}</option> ';
	           				}else{
	           					html+='<option value="${products.id}">${products.value}</option> ';
	           				}
           				[/#list]
           				html+='</select><input type="hidden" value="'+ item.level_Id +'" class="t productGrades"/>';
            			return html;
                }},
                { title:'${message("销售价")}', name:'member_price' ,align:'center', width:90,renderer: function(val){
                	if(isEmpty(val)){
                		val = 0;
                	}
                        return '<span class="red memberPriceText">'+currency(val,true)+'</span><input type="hidden"  name="priceApplyItems['+itemIndex+'].memberPrice" class="text memberPrice" value="'+val+'">';
                    }},
                { title:'${message("申请价格")}',name:'item_price', align:'center',renderer:function(val,item,rowIndex,obj){
                        var price = '';
                        if(obj==undefined){
                            price = item.item_price;
                        }
                        var html = '<div class="nums-input ov">'+
                                '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice(this.nextSibling,event)">'+
                                '<input type="text"  class="t price priceApply"  name="priceApplyItems['+itemIndex+'].price" value="'+price+'" minData="0" oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)" >'+
                                '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice(this.previousSibling,event)">'+
                                '</div>';
                        return html;
                }},
                { title:'${message("折扣")}',name:'discount', align:'center',renderer:function(val,item,rowIndex,obj){
                	var discount = 0;
                	if(!isEmpty(item.discount)){
                		discount = item.discount;
                	}
                    return '<span class="discount">'+discount+'</span>'+
                    '<input type="hidden"  class="t discount"  name="priceApplyItems['+itemIndex+'].discount"  minData="0" value="'+discount+'">';
                }},
                { title:'${message("平台结算价原价")}',[#if isMember==1 || seeSaleOrgPrice == 0]hidden:true,[/#if]name:'sale_org_price', align:'center', renderer: function(val,item,rowIndex){
                        return '<span class="red saleOrgPriceText">'+currency(val,true)+'</span><input type="hidden"  name="priceApplyItems['+itemIndex+'].saleOrgPrice" class="text saleOrgPrice" value="'+val+'">';
                }},
                { title:'${message("平台结算价特价")}',[#if isMember==1 || seeSaleOrgPrice == 0]hidden:true,[/#if]name:'sale_org_sales_price', align:'center',renderer: function(val,item,rowIndex){
                        var sale_org_sales_price = item.sale_org_sales_price;
                        if(sale_org_sales_price == ''){
                            sale_org_sales_price = 0;
                        }
                        var html = '<div class="nums-input ov">'+
                                '<input type="button" class="b decrease " value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice(this.nextSibling,event)">'+
                                '<input type="text"  class="t price saleOrgSalesPrice"  name="priceApplyItems['+itemIndex+'].saleOrgSalesPrice" value="'+sale_org_sales_price+'" minData="0" oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)" >'+
                                '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice(this.previousSibling,event)">'+
                                '</div>';
                        return html;
                }},
                {title:'价格表ID', align:'center',name:'',width:60, hidden:true,renderer: function(val,item,rowIndex){
                        var priceId=item.ppriceId==null?'':item.ppriceId;
                        return '<input   type="text" readonly="readonly" name="priceApplyItems['+itemIndex+'].priceId" value="'+priceId+'" >'
                    }},
                {title:'产品部结算价',[#if isMember!= 0 || editProductOrgPrice !=1]hidden:true,[/#if] align:'center',name:'product_org_price',renderer: function(val,item,rowIndex){
                        var product_org_price=item.product_org_price==null?'':item.product_org_price;
                        return '<span class="red productOrgPriceText">'+currency(val,true)+'</span><input type="hidden"  name="priceApplyItems['+itemIndex+'].productOrgPrice" class="text productOrgPrice" value="'+product_org_price+'">';
                    }},
                { title:'${message("最小开单数量/m2")}',name:'min_quantity',align:'center',renderer:function(val,item,rowIndex,obj){
                        var quantity = 1;
                        if(obj==undefined){
                            quantity = val;
                        }
                        var html = '<div class="nums-input ov">'+
                                '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editQty(this.nextSibling,event)">'+
                                '<input type="text"  class="t quantity minquantity"  name="priceApplyItems['+itemIndex+'].minQuantity" value="'+quantity+'" minData="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" >'+
                                '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty(this.previousSibling,event)">'+
                                '</div>';
                        return html;
                }},
                { title:'${message("最大累计数量/m2")}',name:'max_quantity',align:'center',renderer:function(val,item,rowIndex,obj){
                        var quantity = 1;
                        if(obj==undefined){
                            quantity = val;
                        }
                        var html = '<div class="nums-input ov">'+
                                '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editQty(this.nextSibling,event)">'+
                                '<input type="text"  class="t quantity maxquantity"  name="priceApplyItems['+itemIndex+'].maxQuantity" value="'+quantity+'" minData="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" >'+
                                '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editQty(this.previousSibling,event)">'+
                                '</div>';
                        return html;
                }},
                { title:'${message("使用数量")}',name:'usage_quantity', align:'center',width:160 },	
                { title:'${message("操作")}', align:'center', width:60, renderer: function(val,item,rowIndex){
                        itemIndex++;
                        return '<a href="javascript:;" class="btn-delete" onclick="deleteProduct(this)">删除</a>';
                }},
            ];
            $mmGrid = $('#table-m1').mmGrid({
                height:'auto',
                cols: cols,
                items:items,
                fullWidthRows:true,
                checkCol: false,
                autoLoad: true,
                callback:function(){
                    minquantity();
                    maxquantity();
                }
            });

            var orderFullLink_items = ${fullLink_json};
            var cols = [
                { title:'${message("内容")}', name:'content' ,width:300,align:'center'},
                { title:'${message("操作人")}', name:'operator_name',width:100 ,align:'center'},
                { title:'${message("创建日期")}', name:'create_date' ,width:150 ,align:'center'}
            ];
            $('#table-full').mmGrid({
                fullWidthRows:true,
                height:'auto',
                cols: cols,
                items:orderFullLink_items,
                checkCol: false,
                autoLoad: true
            });

            $(".shippingWarehouseId").live("change", function() {
                var $tr=$(this).closest("tr");
                var $productCategoryId=$tr.find(".productCategoryId");
                var productCategoryId=$productCategoryId.attr("product_category_id");
                var productId=$productCategoryId.attr("product_id");
                var shippingWarehouseId=$(this).val();
                var storeId = $(".storeId").val();
                var saleOrgId = $(".saleOrgId").val();
                var productGrade = $tr.find(".grade").val();
                $productCategoryId.attr("str",productCategoryId+"_"+productId+"_"+shippingWarehouseId+"_"+productGrade);
                //查询当前库存的商品在价格表的对应价格
                ajaxSubmit('',{
                    method:'post',
                    url:'/product/product_price_head/findProductPrice.jhtml',
                    data:{storeId:storeId,productId:productId,saleOrgId:saleOrgId,warehouseShippingId:shippingWarehouseId,productGrade:productGrade},
                    callback:function(resultMsg) {
                        var data = resultMsg.objx;
                        if (data != null) {
                            var 	price = data.store_member_price;//经销商价格
                            var  sale_org_price = data.sale_org_price;//结算价
                            if (isNaN(data.store_member_price)) {
                                $.message_alert("${message("请在价格表维护该产品价格")}");
                                return false;
                            }else {
                                $tr.find("input.saleOrgPrice").val(data.sale_org_price);
                                $tr.find(".saleOrgPriceText").text(currency(data.sale_org_price,true));
                                $tr.find("input.memberPrice").val(data.store_member_price);
                                $tr.find("input.productOrgPrice").val(data.product_org_price);
                                $tr.find(".productOrgPriceText").text(currency(data.product_org_price,true));
                            }
                        }else {
                            $.message_alert("${message("请在价格表维护该产品价格")}");
                        }
                    }
                });
            });

            $(".grade").live("change", function() {

                var $tr=$(this).closest("tr");
                var productGrades = $tr.find(".productGrades").val();
                var $productCategoryId=$tr.find(".productCategoryId");
                var productCategoryId=$productCategoryId.attr("product_category_id");
                var productId=$productCategoryId.attr("product_id");
                var productGrade=$(this).val();
                var storeId = $(".storeId").val();
                var sbuId = $(".sbuId").val();
                var saleOrgId = $(".saleOrgId").val();
                var shippingWarehouseId = $tr.find(".shippingWarehouseId").val();
                $productCategoryId.attr("str",productCategoryId+"_"+productId+"_"+shippingWarehouseId+"_"+productGrade);

				if(productId!=0){
	                //查询当前库存的商品在价格表的对应价格
	                ajaxSubmit('',{
	                    method:'post',
	                    url:'/product/product_price_head/findSpecialPrice.jhtml',
	                    data:{storeId:storeId,productId:productId,saleOrgId:saleOrgId,warehouseShippingId:shippingWarehouseId,productGrade:productGrade,sbuId:sbuId},
	                    callback:function(resultMsg) {
	                        var data = resultMsg.objx;
	                        if (data !=null) {
	                            var 	price = data.store_member_price;//经销商价格
	                            var  sale_org_price = data.sale_org_price;//结算价
	                            if (isNaN(data.store_member_price)) {
	                                $tr.find("#productGrade").val(productGrades);
	                                $.message_alert("${message("请在价格表维护该产品价格")}");
	                                return false;
	                            }else {
	                                $tr.find("input.saleOrgPrice").val(data.sale_org_price);
	                                $tr.find(".saleOrgPriceText").text(currency(data.sale_org_price,true));
	                                $tr.find("input.memberPrice").val(data.store_member_price);
	                                $tr.find(".productGrades").val(productGrade);
                                    $tr.find("input.productOrgPrice").val(data.product_org_price);
                                    $tr.find(".productOrgPriceText").text(currency(data.product_org_price,true));
	                            }
	                        }else {
	                            $tr.find("#productGrade").val(productGrades);
	                            $.message_alert("${message("请在价格表维护该产品价格")}");
	                            return false;
	                        }
	                    }
	                });
                }
            });


            $("#submit_button").click(function(){
                var count = $("input.productId").length;
                if(count<1){
                    $.message_alert("特价明细不能少于一条");
                }else{
                    var count=[0];
                    var sub=true;
                    $(".productCategoryId").each(function(){
                        var required=$(this).attr("str");
                        count.push(required);
                    });
                    var  myselect=document.getElementById("type");
                    var index=myselect.selectedIndex ;
                    var project=myselect.options[index].innerText;
                    if(project=="工程"){
                        if($(".engineeringName").val() == ''){
                            $.message_alert("工程为必输字段");
                            return false;
                        }
                    }
                    if ($("#startDate").val() == ''){
                        $.message_alert("开始时间为必输");
                        return false;
                    }
                    if ( $("#endDate").val() == ''){
                        $.message_alert("结束时间为必输");
                        return false;
                    }
                    $("form").valid();
                    if(sub){
                        $("form").submit();
                    }
                }
            });

            $("form").bindAttribute({
                isConfirm:true,
                callback: function(resultMsg){
                    $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                        location.reload(true);
                    })
                }
            });
            
            //查询客户
            $("#selectStore").click(function(){
                var sbuId = $(".sbuId").val();
                if(isNull(sbuId) == null){
                	$.message_alert('sbu不能为空');
                    return false;
                }
                $("#selectStore").bindQueryBtn({
                    type:'store',
                    title:'${message("查询客户")}',
                    bindClick:false,
                    url:'/member/store/select_store.jhtml?type=distributor&isMember=1&'+'&sbuId='+sbuId,
                    callback:function(rows){
                        if(rows.length>0){
                            var row = rows[0];
                            $(".storeName").val(row.name);
                            $(".storeId").val(row.id);
                            $("#storeAlias").text(row.alias);
                            $(".engineeringName").val('');
                            $(".engineeringId").val('');
                            $(".saleOrgName").val(row.sale_org_name);
                            $(".saleOrgId").val(row.sale_org_id);
                        }
                    }
                });
            });

            //查询机构
            $("#selectSaleOrg").bindQueryBtn({
				type:'saleOrg',
				title:'${message("查询机构")}',
				url:'/basic/saleOrg/select_saleOrg.jhtml',
				callback:function(rows){
					if(rows.length>0){
						var id = rows[0].id;
						$(".saleOrgId").val(rows[0].id);
						$(".saleOrgName").val(rows[0].name);
						$(".storeId").val('');
						$(".storeName").val('');
					}
				}
			});
            

            //打开选择工程界面
            $("#selectEngineering").click(function(){
                var $storeId = $(".storeId").val();
                if($storeId==""){
                    $.message_alert('${message("请选择客户")}');
                }else{
                    $("#selectEngineering").bindQueryBtn({
                        type:'engineering',
                        bindClick:false,
                        title:'${message("查询工程")}',
                        url:'/basic/engineering/select_list.jhtml?storeId='+$storeId,
                    });
                }
            })

            $("#type").change(function(){
                var  myselect=document.getElementById("type");
                var index=myselect.selectedIndex ;
                var project=myselect.options[index].innerText;
                if(project=="工程"){
                    $("#selectEngineering").removeAttr("disabled");
                    $(".engineeringId").removeAttr("disabled");
                    $(".engineeringName").removeAttr("disabled");
                }else{
                    $("#selectEngineering").attr("disabled","disabled");
                    $(".engineeringId").attr("disabled","disabled");
                    $(".engineeringName").attr("disabled","disabled");
                    $(".engineeringId").val('');
                    $(".engineeringName").val('');
                }
            });

            /**初始化附件*/
            var twContractAttach_items = ${twContractAttach_json};
            var attachIdnex=0;
            var cols = [
                { title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex,obj){
                        if(obj==undefined){
                            var url = item.file_url;
                            var fileObj = getfileObj(item.file_name , item.name, item.suffix);

                            /**设置隐藏值*/
                            var hideValues = {};
                            hideValues['twContractAttachs['+attachIdnex+'].id']=item.id;
                            hideValues['twContractAttachs['+attachIdnex+'].fileUrl']=url;
                            hideValues['twContractAttachs['+attachIdnex+'].suffix']=fileObj.suffix;

                            return createFileStr({
                                url : url,
                                fileName : fileObj.file_name,
                                name : fileObj.name,
                                suffix : fileObj.suffix,
                                time : item.create_date,
                                textName:'twContractAttachs['+attachIdnex+'].name',
                                hideValues: hideValues
                            });
                        }else{
                            var url = item.url;
                            var fileObj = getfileObj(item.name);
                            /**设置隐藏值*/
                            var hideValues = {};
                            hideValues['twContractAttachs['+attachIdnex+'].fileUrl']=url;
                            hideValues['twContractAttachs['+attachIdnex+'].suffix']=fileObj.suffix;

                            return createFileStr({
                                url : url,
                                fileName : fileObj.file_name,
                                name : fileObj.name,
                                suffix : fileObj.suffix,
                                time : '',
                                textName:'twContractAttachs['+attachIdnex+'].name',
                                hideValues:hideValues
                            });

                        }


                    }},
                { title:'${message("备注")}', name:'memo' ,width:590 ,align:'center', renderer: function(val,item,rowIndex){
                        return '<div><textarea class="text file_memo" name="twContractAttachs['+attachIdnex+'].memo" >'+val+'</textarea></div>';
                    }},
                { title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
                        attachIdnex++;
                        return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
                    }}
            ];
            var $amGrid=$('#table-attach').mmGrid({
                fullWidthRows:true,
                height:'auto',
                cols: cols,
                items:twContractAttach_items,
                checkCol: false,
                autoLoad: true
            });

            var $addAttach = $("#addAttach");
            var attachIdnex = 0;
            var option1 = {
                dataType: "json",
                uploadToFileServer:true,
                uploadSize: "fileurl",
                callback : function(data){
                    var date = new Date();
                    var year = date.getFullYear();
                    var month = date.getMonth()+1;
                    var day = date.getDate();
                    var time = year+'-'+month+'-'+day;
                    for(var i=0;i<data.length;i++){
                        var row=data[i].file_info;
                        $amGrid.addRow(row,null,1);
                    }

                }
            }
            $addAttach.file_upload(option1);
            var $deleteAttachment = $(".deleteAttachment");
            $deleteAttachment.live("click", function() {
                var $this = $(this);
                $this.closest("tr").remove();
            });
        });
        function deleteProduct(e){
            var index = $(e).closest("tr").index();
            $.message_confirm('您确定要删除吗？',function(){
                $mmGrid.removeRow(index);
                countTotal();
            })
        }

        function add(wf_index,url){
            location.href=url;
        }

        function subflag(){
            $("#isSubmit").val(1);
        }

        function check(t,flag){
            var str='您确定要审核吗';
            if(flag==0){
                str='<div>您确定要&nbsp;&nbsp;<b>作废</b>&nbsp;&nbsp;当前特价单吗？<br/>确定:将失效当前特价单下所有的产品特价。<br/>取消:不做任何处理。</div>'
            }
            ajaxSubmit(t,{
                url: '/b2b/priceApply/check.jhtml?id=${priceApply.id}&flag='+flag,
                method: "post",
                isConfirm:true,
                confirmText : str,
                callback:function(resultMsg){
                    $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                        location.reload(true);
                    });
                }
            })
        }

        function check_wf(e) {
            var $this = $(e);
            var $form = $("#inputForm");
            var sbuId = $(".sbuId").val();
            var modelId = model($("#sbuId").val(),"正式");
            if($form.valid()){
                $.message_confirm("您确定要审批流程吗？",function(){
                    //var objTypeId = 100020;//大自然开发
                    //var modelId = 777501;//大自然开发
                    var objTypeId = 100023;//大自然测试&正式
                    var url="/b2b/priceApply/start_check_wf.jhtml?id=${priceApply.id}&modelId="+modelId+"&objTypeId="+objTypeId;
                    var data = $form.serialize();
                    ajaxSubmit(e,{
                        method: 'post',
                        url: url,
                        async: true,
                        data:data,
                        callback: function(resultMsg) {
                            $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                                location.reload(true);
                            })
                        }
                    });
                });
            }
        }

        //查流程模板
        function model(sbuId,versions){
            var json = '{"正式":{"1":"57600","2":"57600","3":"57600","4":"57600","5":"57600","6":"57600","7":"57600","8":"57600"},';
            json +='"测试":{"1":"120001","2":"120005","3":"120009","4":"120003","5":"120007","6":"","7":"","8":"175144","10":"3115158","11":"3115158","12":"3115158"}}';
            var model = JSON.parse(json);
            return model[versions][sbuId];
        }

        function getdiscount(e){
        	  var $tr=$(e).closest("tr");
        	  var priceApply=$tr.find(".priceApply").val();
        	  var productPrice=$tr.find(".memberPrice").val();
        	  var quantity=$tr.find(".quantity").val();
        	  var num=0;
        	  var itemPrices = 0;
        	  if(productPrice=="0" || !(productPrice!=undefined && productPrice!=null && productPrice!='')){
        	    num=0;
        	  }else if(productPrice!=undefined && productPrice!=null && productPrice!=''&&priceApply>0){
        	    num =(priceApply/productPrice)*100;
        	  }
        	  $tr.find(".discount").html(currency(num.toFixed(0)));
     	      $tr.find(".discount").val(currency(num.toFixed(0)));//折扣
	          if(priceApply != "" && priceApply>0 && quantity != "" && quantity >0){
	              itemPrices = priceApply * quantity;
	          }
	          $tr.find(".item_prices").html(currency(itemPrices,true));
     	}
        function getItemPrice(e){
            var quantity=$(e).val();
            var $tr=$(e).closest("tr");
            var priceApply=$tr.find(".priceApply").val();
            var itemPrices = 0;
            if(priceApply != "" && priceApply>0 && quantity != "" && quantity >0){
                itemPrices = priceApply * quantity;
            }
            $tr.find(".item_prices").html(currency(itemPrices,true));
        }

    </script>
</head>
<body>
<div class="pathh">
    &nbsp;${message("查看特价单")}
</div>
<form id="inputForm" action="/b2b/priceApply/save.jhtml" method="post" type="ajax" validate-type="validate" enctype="multipart/form-data">
    <input type="hidden" name="id" id="pid" value="${priceApply.id}" />
    <input type="hidden" name="isSubmit" id="isSubmit" value="" />
    <div class="tabContent order-info">
        <table class="input input-edit">
            <tr>
                <th>
                ${message("特价单编号")}:
                </th>
                <td>
                ${priceApply.sn}
                </td>
                <th>${message("客户")}:</th>
                <td>
					<span class="search" style="position:relative">
					<input type="hidden" name="store.id" class="text storeId" value="${priceApply.store.id}" btn-fun="clear"/>
					<input type="text" name="storeName" class="text storeName" maxlength="200" onkeyup="clearSelect(this)" value="${priceApply.store.name}" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectStore">
					</span>
                </td>
                <th>${message("单据状态")}:</th>
                <td>
					[#if priceApply.docStatus == 0]<b class="blue">${message("已保存")}</b>[/#if]
					[#if priceApply.docStatus == 1]<b class="blue">${message("处理中")}</b>[/#if]
					[#if priceApply.docStatus == 2]<b class="green">${message("已处理")}</b>[/#if]
					[#if priceApply.docStatus == 3]<b class="red">${message("已失效")}</b>[/#if]
                </td>
                <th>${message("流程状态")}:</th>
                <td>
					[#if priceApply.wfState??]
                        ${message("22222222"+priceApply.wfState)}
                    [/#if]
                </td>
            </tr>
            <tr>
                <th>
                	${message("特价类型")}:
                </th>
                <td >
                    <select id="type" class="text" name="typeID">
						[#list specialTypes as type]
                            <option value="${type.id}" [#if priceApply.type.id==type.id]selected[/#if]>${type.value}</option>
                        [/#list]
                    </select>
                </td>
                <th>
               		${message("工程")}:
                </th>
                <td>
					<span class="search" style="position:relative">
						<input type="hidden" name="engineering.id" class="text engineeringId" value="${priceApply.engineering.id}" btn-fun="clear"/>
						<input type="text" name="engineeringName" class="text engineeringName" value="${priceApply.engineering.name}" maxlength="200" onkeyup="clearSelect(this)" [#if priceApply.type!=3]disabled[/#if]  readOnly/>
						<input type="button" class="iconSearch" value="" id="selectEngineering" [#if priceApply.type!=3]disabled[/#if]>
					</span>
                </td>
                <th>
                    <span class="requiredField">*</span>${message("开始时间")}:
                </th>
                <td id="start">
                    <input type="text" class="text startDate" name="startDate" id="startDate" value="${priceApply.startDate?string("yyyy-MM-dd")}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'});" btn-fun="clear"/>
                </td>

                <th>
                    <span class="requiredField">*</span>${message("结束时间")}:
                </th>
                <td id="end">
                    <input type="text" class="text endDate" name="endDate" id="endDate" value="${priceApply.endDate?string("yyyy-MM-dd")}" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'});" btn-fun="clear"/>
                </td>
            </tr>
            <tr>
                <th>${message("机构")}:</th>
                <td>
                    <span class="search" style="position:relative">
						<input type="hidden" name="saleOrg.id" class="text saleOrgId" btn-fun="clear" value="${priceApply.saleOrg.id}"/>
						<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" value="${priceApply.saleOrg.name}"  readOnly/>
						<input type="button" class="iconSearch" value="" id="selectSaleOrg">
					</span>
                </td>
                <th>
                    <span class="requiredField">*</span>${message("申请人")}:
                </th>
                <td>
					<span class="search" style="position:relative">
                    <input type="hidden" name="storeMemberId" class="text storeMemberId" value="${priceApply.storeMember.id}" btn-fun="clear"/>
                    <input type="text" name="storeMemberName" class="text storeMemberName"  value="${priceApply.storeMember.name}" maxlength="200"  readOnly/>
                    </span>
                </td>
                <th>
                	${message("1011")}:
                </th>
                <td>
                	${priceApply.createDate?string("yyyy-MM-dd HH:mm:ss")}
                </td>
                <th>${message("Sbu")}:</th>
                <td>
                    <input type="hidden" name="sbuId" class="text sbuId" id="sbuId" btn-fun="clear" value="${priceApply.sbu.id}"/>
                    <span id="sbuName">${priceApply.sbu.name}</span>
                </td>
            </tr>
            <tr>
            	<th>
					${message("经营组织")}:
				</th>
				<td>
					[#if priceApply.docStatus == 0] 
						<select name="organizationId" id="organizationId" class="text organizationId" >
							[#list organizationList as organization]
								<option value="${organization.id}" [#if priceApply.organization.id == organization.id]selected="selected"[/#if]>${organization.name}</option>
							[/#list]
						</select>
					[#else] 
						<input type="hidden" name="organizationId" class="text organizationId" id="organizationId" value="${priceApply.organization.id}"/>
                    	<span id="organizationName">${priceApply.organization.name}</span>
					[/#if]
				</td>
				<th>
					${message("客户简称")}:
				</th>
				<td>
					<span id="storeAlias">${priceApply.store.alias}</span>
				</td>
            </tr>
            <tr>
                <th>
                ${message("申请说明")}:
                </th>
                <td colspan="7"><textarea name="memo" class="text" id="memo">${priceApply.memo}</textarea></td>
            </tr>
            <tr></tr>
            <tr>
                <th>${message("最小数量汇总")}:</th>
                <td><span class="min text red"></span></td>
                <th>${message("最大数量汇总")}:</th>
                <td><span class="max text red"></span></td>
            </tr>
        </table>

        <div class="title-style">
        ${message("特价单明细")}:
            <div class="btns">
              	<a href="javascript:;" id="addProductCategory" class="button">选择产品系列</a>  
                <a href="javascript:;" id="addProduct" class="button">选择产品</a>
            </div>
        </div>
        <table id="table-m1"></table>

        <div class="title-style">
        ${message("附件信息")}:
            <div class="btns">
                <a href="javascript:;" id="addAttach" class="button">添加附件</a>
            </div>
        </div>
        <table id="table-attach"></table>


        <div class="title-style">
        ${message("全链路信息")}:
        </div>
        <table id="table-full"></table>
    </div>
    <div class="fixed-top">
        <a href="javascript:void(0);" onclick="add(2,'/b2b/priceApply/add/${code}.jhtml?sbuId=${priceApply.sbu.id }')" class="iconButton" id="addButton">
        	<span class="addIcon">&nbsp;</span>
        	${message("新增")}
        </a>
		[#if priceApply.docStatus==0]
			<input type="button" id="submit_button" class="button sureButton" value="${message("保存")}">
			<a href="javascript:void(0);" class="button sureButton" onclick="check_wf()">${message("12501")}</a>
			<a href="javascript:void(0);" class="button cancleButton" onclick="check(this,0)">${message("作废")}</a>
        [/#if]
        <input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
    </div>
</form>
<div id="wf_area" style="width:100%"></div>
</body>
</html>