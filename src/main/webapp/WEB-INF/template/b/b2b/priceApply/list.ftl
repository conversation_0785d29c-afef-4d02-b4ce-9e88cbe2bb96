<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("订单价格申请")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/dynamicForm/mmGridConfiguration.js"></script>
<script type="text/javascript" src="/resources/js/dynamicForm/formList.js"></script>
<script type="text/javascript" src="/resources/js/dynamicForm/searchControlType.js"></script>
<script type="text/javascript">
	$().ready(function() {
		//获取按钮控件
		getButtonHtml(0,'${dTemplates.id}');
		//获取筛选html
		parameterList('${userId}','${dTemplates.id}');
		//遍历列表表头
		traverseList('${userId}','${dTemplates.id}','${defaultQuery}','${dTemplates.pdfPath}','${dTemplates.excelPath}');
	});
	
	//查看
	function edit(id){
		parent.change_tab(0,'/b2b/priceApply/edit/${code}.jhtml?id='+id);
	}
	//新增
	function add(e){
	    parent.change_tab(0,'/b2b/priceApply/add/${code}.jhtml?sbuId=${sbuId}');
	}
</script>
</head>
<body>
	<form id="listForm" action="/b2b/priceApply/list.jhtml" method="get">
		<input type="hidden" name="userId" class="userId"  value="${userId}"/>
		<input type="hidden" name="templateId" class="templatesId"  value="${dTemplates.id}"/>
		<input type="hidden" name="pagesize" class="pagesize" value="${pagesize}"/>
		<input type="hidden" name="sbu_id" class="sbu_id" value="${sbuId}"/>
		<div class="bar">
			<div class="buttonWrap"></div>
				<div id="searchDiv">
	        	<div id="search-content" >
	        		<table id="search-table"></table>	
				</div>
				<div class="search-btn" style="height:32px">
					<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
				</div>
			</div>
		</div>
		<div class="table-responsive">
	        <table id="table-m1"></table>
	        <div id="body-paginator">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</body>
</html>