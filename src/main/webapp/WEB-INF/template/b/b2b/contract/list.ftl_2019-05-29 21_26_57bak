<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("合同列表")} </title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />	
<script type="text/javascript">
function initWfStates(){
		wfStates = {};
		[#list wfStates as wfState]
			wfStates['${wfState}'] = '${message('22222222'+wfState)}';
		[/#list]
	}
function add(){
	parent.change_tab(0,'/b2b/contract/add/${code}.jhtml?flag=${flag}');
}
$().ready(function() {

	/**初始化多选的下拉框*/
	initMultipleSelect();

	//查询客户
	$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'/member/store/select_store.jhtml?type=distributor&isMember=1&isSelect=0'
	});
	
	//查询用户
	$("#selectStoreMember").bindQueryBtn({
		type:'creator',
		title:'${message("查询创建人")}',
		url:'/member/store_member/select_store_member.jhtml'
	});
	
	initWfStates();
	var region={"0":"东区","1":"西区","2":"南区","3":"北区"};
	var cols = [
		{ title:'${message("合同编号")}', name:'contract_no' ,align:'center',renderer:function(val,item){
			return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'/b2b/contract/edit/${code}.jhtml?id='+item.id+'&flag=${flag}\')" class="red">'+val+'</a>';
		}},
		{ title:'${message("经销商")}', name:'store_name' ,align:'center' },
		{ title:'${message("机构")}', name:'sale_org_name' ,align:'center' },
		{ title:'${message("区域")}', name:'region' ,align:'center',renderer:function(val){
			var result = region[val];
			if(result==undefined) result='';
			return result;
		} },
		{ title:'${message("地区")}', name:'area_name' ,align:'center' },
		{ title:'${message("乡镇")}', name:'villages' ,align:'center' },
		{ title:'${message("合同签署人")}', name:'distributor_signer' ,align:'center' },
		{ title:'${message("创建日期")}', name:'create_date' ,align:'center' },
	];

	$mmGrid = $('#table-m1').mmGrid({
        cols: cols,
        fullWidthRows:true,
        autoLoad: true,
        url: '/b2b/contract/list_data.jhtml',
        params:function(){
        	return $("#listForm").serializeObject();
        },
		plugins : [
            $('#paginator').mmPaginator()
        ]
    });
});

//导入
function customer_import(e){
	excel_import(e,{
		title:'${message("合同导入")}',
		url:'/b2b/contract/import_excel.jhtml',
		template:'/resources/template/member/contract.xls',
		callback:function(){
			$("#searchBtn").click();
		
		}
	});
}

//条件导出		    
function segmentedExport(e){
	var needConditions = false;//
	var page_url = '/b2b/contract/to_condition_export.jhtml';//分页导出统计页面
	var url = '/b2b/contract/condition_export.jhtml';//导出的方法
	conditions_export(e,{needConditions:needConditions,page_url:page_url,url:url});
}

//选择导出
function exportExcel(t){
	var param = $mmGrid.serializeSelectedIds();//参数
	var tip = '${message("请选择导出合同！")}';//提示
	var url = '/b2b/contract/selected_export.jhtml';//导出的方法
  	select_export(t,{tip:tip, param:param, url:url});
}
</script>
</head>
<body>
	<form id="listForm" action="/b2b/contract/list/${code}.jhtml" method="get">
		<input type="hidden" name="flag" value="${flag}">
		<div class="bar">
				<div class="buttonWrap">
				<div class="flag-wrap flagImp-wrap">
					<a href="javascript:void(0);" class="iconButton" id="export1Button">
							<span class="impIcon">&nbsp;</span>导入导出
					</a>
					<ul class="flag-list">
						<li><a href="javascript:void(0)" onclick="customer_import(this)"><i class="flag-imp02"></i>${message("导入")}</a></li>
						<li><a href="javascript:void(0)" onclick="exportExcel(this)"><i class="flag-imp02"></i>${message("选择导出")}</a></li>
						<li><a href="javascript:void(0)" onclick="segmentedExport(this)"><i class="flag-imp02"></i>${message("条件导出")}</a></li>

					</ul>
				</div>
			[#if flag != 1]
				<a href="javascript:add();" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
			[/#if]		
			</div>
			<div id="searchDiv">
        	<div id="search-content" >
        		<dl>
        			<dt><p>${message("合同编号")}：</p></dt>
        			<dd>
        				<input type="text" class="text"  name="sn" btn-fun="clear" />
        			</dd>
        		</dl>
        		<dl>
        			<dt><p>${message("客户")}：</p></dt>
        			<dd >
        				<span class="search" style="position:relative">
							<input type="hidden" name="storeId" class="text storeId" id="storeId" btn-fun="clear" />
							<input type="text" name="storeName" class="text storeName" maxlength="200" onkeyup="clearSelect(this)" id="storeName" />
							<input type="button" class="iconSearch" value="" id="selectStore">
						</span>
        			</dd>
        		</dl>
        		<!-- <dl>
        			<dt ><p>${message("单据状态")}：</p></dt>
        			<dd>
        				<div class="checkbox-style">
							<a href="javascript:void(0);" onclick="clearText(this)" class="deleteText close"></a>
					       	<input type="text" class="text pointer doStatus" value="${message("已保存;处理中;已处理")}" autocomplete="off" />
				       		<div class="statusList cs-box" data-value="off">
				       			<label><input  class="check js-iname" name="docstatus" value="0" type="checkbox" checked/>${message("已保存")}</label>
				       			<label><input  class="check js-iname" name="docstatus" value="1" type="checkbox" checked/>${message("已提交")}</label>
				       			<label><input  class="check js-iname" name="docstatus" value="2" type="checkbox" checked/>${message("已处理")}</label>
				       			<label><input  class="check js-iname" name="docstatus" value="3" type="checkbox"/>${message("已关闭")}</label>
				      		</div>
						</div>
        			</dd>
        		</dl> -->
        		<!-- <dl>
					<dt><p>${message("创建时间")}:</p></dt>
					<dd class="date-wrap">
							<input id="startTime" name="firstTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
							<div class="fl">--</div>
							<input id="endTime" name="lastTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="text" btn-fun="clear"/>
					</dd>
				</dl> -->
			</div>
			<div class="search-btn"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
			</div>	
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
	   		<div id="body-paginator" style="text-align:left;">
	    		<div id="paginator"></div>
	   		</div>
		</div>
		</form>
	</body>
</html>