<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("合同查看")} </title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<style type="text/css">
	tr.s-tr,tr.s-tr td{height:10px !important;}
	div.w_1135{ width: 1135px;}
#qButton{
width:20px;
height:20px;
position:absolute;
top:4px;
left:12px;
background:url(/resources/images/button/ico-aa.png) no-repeat center;
}
#addquantity {
padding:0 21px;
height:28px;
line-height:28px;
margin-right:0;
}
#n-input{
width:74%;
float:left;
margin-right:5px;
}
</style>
<script type="text/javascript">

function editPrice(t,e){
	extractNumber(t,2,true,e);
}

function editSale(t,e){
	extractNumber(t,2,true,e)
}

function totalSale(){
	var totalSale = 0;
	var $atofadoaValue = $("#atofdoa").val();
	var $tadaValue = $("#tada").val();
	var totalAnnual = 0;
 	$("input.annual").each(function(){
		var $inputAnnual = $(this).val();
		if($inputAnnual=="" || $inputAnnual==null ){
			$inputAnnual = 0.00;
	 	}
		totalAnnual = accAdd($inputAnnual,totalAnnual);
	})
	$("#atofdoa").attr('value',totalAnnual); 
 	if($tadaValue=="" || $tadaValue==null ){
 		$tadaValue = 0.00;
 	}
 	if($atofadoaValue=="" || $atofadoaValue==null ){
 		$atofadoaValue = 0.00;
 	}
	var $monthlyValue = $("#monthly").val();
	var totalMonthly = 0;
	$("input.monthly").each(function(){
		var $inputMonthly = $(this).val();
		if($inputMonthly=="" || $inputMonthly==null ){
			$inputMonthly = 0.00;
	 	}
		totalMonthly = accAdd($inputMonthly,totalMonthly);
	})
	$("#monthly").attr('value',totalMonthly);
	
	var $distributionMonthVal = $("#distributionMonth").val();
	var totalDistributionMonth = 0 ;
	$("input.distributionMonth").each(function(){
		var $thisValue = $(this).val();
		if($thisValue=="" || $thisValue==null ){
			$thisValue = 0.00;
	 	}
		totalDistributionMonth = accAdd($thisValue,totalDistributionMonth);
	})
	$("#distributionMonth").attr('value',totalDistributionMonth);
	
	var totalDistributionYear = 0;
	$("input.distributionYear").each(function(){
		var $thisValue = $(this).val();
		if($thisValue=="" || $thisValue==null ){
			$thisValue = 0.00;
	 	}
		totalDistributionYear = accAdd(totalDistributionYear,$thisValue);
	})
	$("#tada").attr('value',totalDistributionYear);
	
	totalSale = accAdd(totalAnnual,totalDistributionYear);
	$("#aasi").attr('value',totalSale);
	
	//合计
	var totalQuota = 0;
	var $engineeringIndexValue = $("#engineeringIndex").val();
	var $esstValue = $("#esst").val();
	var $siohdpValue = $("#siohdp").val();
	totalQuota = accAdd($engineeringIndexValue,$esstValue);
	totalQuota = accAdd(totalQuota,$siohdpValue);
	totalQuota = accAdd($("#aasi").val(),totalQuota);
	$("#totalQuota").attr("value",totalQuota);
	
	$(".increase").each(function(){
		$(this).prop("value","+");
	})
}

function editSaleTotal(t,e){
	if(extractNumber(t,2,true,e)){
		totalSale();
	}
}

function check_wf(e){
	var $this = $(e);
	var $form = $("#inputForm");
	var flag =1;
	var content = '您确定要审核吗？';
	var data = $form.serialize()+"&flag="+flag;
	if($form.valid()){
		$.message_confirm(content,function(){
			//begin
			ajaxSubmit(e,{
				 url: '/wf/wf_obj_config/get_config.jhtml?obj_type_id=59&objid=${dr.id}',
				 method: "post",
				 callback:function(resultMsg){
				 	var rows = resultMsg.objx;
				 	if(rows.length==1){
				 		data = data+'&objConfId='+rows[0].id;
				 		ajaxSubmit('',{
							 url: "/b2b/contract/check_wf.jhtml",
							 method: "post",
							 data: data,
							 callback:function(resultMsg){
								$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
									reflush_wf();
								});
							 }
						})
				 	}else{
				 		var str = '';
					 	for(var i=0;i<rows.length;i++){
					 		var row = rows[i];
					 		str+='<option value="'+row.id+'">'+row.wf_temp_name+'</option>';
					 	}
					 	var content = '<table class="input input-edit" style="width:100%">'
								+'<tbody><tr><th>流程模版</th>'
								+'<td>'
									+'<select class="text" id="objConfId">'
										+str
									+'</select>'
								+'</td>'
							+'</tr></tbody></table>';
						var $dialog_check = $.dialog({
							title:"政策录入审核",
							height:'135',
							content: content,
							onOk:function(){
								var objConfId = $("#objConfId").val();
								if(objConfId=='' || objConfId == null){
									$.message_alert("请选择政策录入模版");
									return false;
								}
								var url="/b2b/contract/check_wf.jhtml";
								data = data+'&objConfId='+objConfId;
								
								ajaxSubmit($this,{
									 url: url,
									 method: "post",
									 data: data,
									 callback:function(resultMsg){
										$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
											reflush_wf();
										});
									 }
								})
								
							}
						});
				 	}
				 	
					var h = 150;
					$dialog_check.css("top",h+"px");
				 }
			
			});
				
			//end
			
		});
	}
}

// 审核
function check(e){
	var $this = $(e);
	var $form = $("#inputForm");
	if($form.valid()){
		var url="/b2b/contract/check.jhtml";
		var data = $form.serialize()+"&flag=1";
		ajaxSubmit($this,{
			 url: url,
			 method: "post",
			 data: data,
			 isConfirm:true,
			 confirmText:'您确定要审核吗？',
			 callback:function(resultMsg){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.reload(true);
				})
			 }
		})
	}
}
function save(e,isSubmit){
	var url = '/b2b/contract/update.jhtml';
	var $form = $("#inputForm");
	if($form.valid()){
		var data = $form.serialize();
		var content = '';
		if(isSubmit==1){
			content = '您确定要提交吗？';
			data+= '&isSubmit='+1
		}else{
			content = '您确定要保存吗？';
		}
		ajaxSubmit(e,{
			url: url,
			data:data,
			method: "post",
			isConfirm:true,
			confirmText:content,
			callback:function(resultMsg){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.reload(true);
				})
			}
		})
	}
};
function close_e(e){
	var $this = $(e);
	var $form = $("#inputForm");
	var flag = 2;
	var	content = '您确定要关闭吗？';
	$.message_confirm(content,function(){
		var url="/b2b/contract/cancel.jhtml";
		var data = $form.serialize()+"&flag="+flag;
		ajaxSubmit($this,{
			 url: url,
			 method: "post",
			 data: data,
			 callback:function(resultMsg){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.reload(true);
				});
			 }
		})
	});
}

function initGrid(){
	/**初始化订单附件*/
   var contractAttachsJson = ${contractAttachsJson};
    var depositAttachIndex=0;
	var cols = [				
    	{ title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex,obj){
			if(obj==undefined){
				var url = item.url;
				var fileObj = getfileObj(item.file_name , item.name, item.suffix);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['contractAttachs['+depositAttachIndex+'].id']=item.id;
				hideValues['contractAttachs['+depositAttachIndex+'].url']=url;
				hideValues['contractAttachs['+depositAttachIndex+'].suffix']=fileObj.suffix;
				
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : item.create_date,
					textName:'contractAttachs['+depositAttachIndex+'].name',
					hideValues: hideValues
				});
			}else{
				var url = item.url;
				var fileObj = getfileObj(item.name);
				/**设置隐藏值*/
				var hideValues = {};
				hideValues['contractAttachs['+depositAttachIndex+'].url']=url;
				hideValues['contractAttachs['+depositAttachIndex+'].suffix']=fileObj.suffix;
				
				return createFileStr({
					url : url,
					fileName : fileObj.file_name,
					name : fileObj.name,
					suffix : fileObj.suffix,
					time : '',
					textName:'contractAttachs['+depositAttachIndex+'].name',
					hideValues:hideValues
				});
			}
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:590 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text" name="contractAttachs['+depositAttachIndex+'].memo" >'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
			depositAttachIndex++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}}
	];
	var $amGrid=$('#table-attach').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        items:contractAttachsJson,
        checkCol: false,
        autoLoad: true
    });
    
    var $addAttach = $("#addAttach");
	var attachIdnex = 0;
	var option1 = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row=data[i].file_info;
				$amGrid.addRow(row,null,1);
	        }
			
        }
    }
    $addAttach.file_upload(option1);
}
$().ready(function() {
	var $bAreaId = $("#areaId");
	$bAreaId.lSelect();
	[#if dr.wfId!=null]
	 $("#wf_area").load("/wf/wf.jhtml?wfid=${dr.wfId}");
	[/#if]

	var payment_types = {'0':'${message("第三方平台订单支付")}','1':'${message("用户余额充值")}','2':'${message("用户余额订单支付")}','3':'${message("客户余额充值")}','4':'${message("客户余额订单支付")}','5':'${message("供应商订单支付")}'};
    var methods = {'0':'${message("在线支付")}','1':'${message("线下支付")}'};
    var termTypes = {'0':'${message("APP")}','1':'${message("微商城")}','2':'${message("PC商城")}','3':'${message("PC后台")}'};
    var payTypes = {'0':'${message("用户余额")}','1':'${message("客户余额")}','2':'${message("微信")}','3':'${message("支付宝")}','4':'${message("银联")}','5':'${message("临时额度")}'};
    var statuss = {'0':'${message("等待支付")}','1':'${message("支付成功")}','2':'${message("支付失败")}'}
    
   // var orderFullLink_items = ${fullLink_json};
	var cols = [				
    	{ title:'${message("内容")}', name:'content' ,width:300,align:'center'},
		{ title:'${message("操作人")}', name:'operator_name',width:100 ,align:'center'},
		{ title:'${message("创建日期")}', name:'create_date' ,width:150 ,align:'center'}
	];
	$('#table-full').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
    //    items:orderFullLink_items,
        checkCol: false,
        autoLoad: true
    });
    
    //查询客户
    $("#selectStore").click(function(){
		$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		bindClick:false,
		url:'/member/store/select_store.jhtml?type=distributor&isMember=1',
		callback:function(rows){
			if(rows.length>0){
				var row = rows[0];
				$(".storeName").val(row.name);
				$(".storeId").val(row.id);
				$(".fixedNumber").val(row.fixed_number);
				[#if storeMember.memberType==1]
				$(".saleOrgName").val(row.sale_org_name);
				$(".saleOrgId").val(row.sale_org_id);
				[/#if]
			}
		}
		});
	});

    
	//查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml'
	});
	
	$("input[name='image']").single_upload({
		uploadSize:"source"
	});
	$("input[name='image2']").single_upload({
		uploadSize:"source"
	});
	$("input[name='image3']").single_upload({
		uploadSize:"source"
	});
	
	//附件初始化
	initGrid();
	
	var $delProductImage1 = $(".deleteAttachment");
	$delProductImage1.live("click", function() {
		var $this = $(this);
		$this.closest("tr").remove();
	});
	$("#inputForm").bindAttribute({
		isConfirm:true,
		callback: function(resultMsg){
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
		}
	 });
	
	$("#openArea").bindQueryBtn({
		type:'area',
		title:'${message("查询地区")}',
		url:'/basic/area/select_area.jhtml',
		callback:function(rows){
			if(rows.length>0){
				var $tr =$this.closest("tr");
				$(".areaId").val(rows[0].id);
				$(".areaName").val(rows[0].full_name);
				//$("input[name='address']").val('');
				$("input[name='zipCode']").val('');
			}
		}
	});	
	 
});

function deleteInvoice(e){
	var index = $(e).closest("tr").index();
	$.message_confirm('您确定要删除吗？',function(){
		$quota_mmGrid.removeRow(index);
	})
}

//计划赋值实际
function addquantity() {
	var $input=$("input.pPrice");
	var $tr = $input.closest("tr");
	$tr.find("input.actualAmount").val($input.val());
}

function clearSelect(e){
	var $this = $(e);
	var value = $this.val();
	if(value==undefined || value.length==0){
		var $tr = $(e).closest("div.search");
		$this.prev("input").val("");
		$(".bankName").text('');
	}
}

function totalQuota(){
	var totalQuota = 0;
	var $engineeringIndexValue = $("#engineeringIndex").val();
	var $esstValue = $("#esst").val();
	var $siohdpValue = $("#siohdp").val();
	totalQuota = accAdd($engineeringIndexValue,$esstValue);
	totalQuota = accAdd(totalQuota,$siohdpValue)
	$("#totalQuota").attr("value",totalQuota);
}

function editTotalQuota(t,e){
	if(extractNumber(t,2,true,e)){
		totalQuota();
	}
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("查看合同")}
	</div>
	<form id="inputForm" action="/b2b/contract/update.jhtml"  method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="id" value="${contract.id}" />
		<input type="hidden" name="sn" value="${contract.sn}" />
		<div class="tabContent">
		<table class="input input-edit">
			<tr>
				<th>
					<span class="requiredField">*</span>${message("合同编号")}:
				</th>
				<td>
					<input type="text" class="text contract_no" name="contract_no" value="${contract.contract_no}" btn-fun="clear" />
				</td>
				<th>
					<span class="requiredField">*</span>${message("经销商姓名")}:
				</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="storeId" class="text storeId" value="${contract.store.id}" btn-fun="clear"/>
					<input type="text" name="storeName" class="text storeName" value="${contract.store.name}" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
					<input type="button" class="iconSearch" value="" id="selectStore">
					</span>
				</td>
				<th>
					${message("品牌保证金")}:
				</th>
				<td>
					<div class="nums-input ov">
						<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
						<input type="text"  class="t"  name="brand_caution_money" value="${contract.brand_caution_money}"  oninput="editPrice (this,event)" onpropertychange="editSale (this,event)" >
						<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
					</div>				
				</td>
				<th>
					${message("收货人")}:
				</th>
				<td>
					<input type="text" class="text consignee" name="consignee" value="${contract.consignee}" btn-fun="clear" />
				</td>
			</tr>
			<tr>
				<th>${message("机构")}:</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value="${contract.saleOrg.id}"/>
					<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" value="${contract.saleOrg.name}" readOnly/>
					<input type="hidden" class="iconSearch" value="" id="selectSaleOrg">
					</span>
				</td>
				<th>${message("合同主体")}:	</th>
                <td ><input type="text" class="text" name="contract_body" value="${contract.contract_body}" btn-fun="clear" ></td>
				<th>${message("签署人身份证")}:</th>
				<td>
					<input type="text" class="text distributor_signer_id_card" name="distributor_signer_id_card" value="${contract.distributor_signer_id_card}" btn-fun="clear" />
				</td>
				
				<th>
					${message("收货人联系方式")}:
				</th>
				<td>
					<input type="text" class="text consignee_phone" name="consignee_phone" value="${contract.consignee_phone}" btn-fun="clear" />
				</td>
			</tr>
			<tr>
				<th>
					${message("区域")}:
				</th>
				<td>
					<select id="region" name="region" class="text">
						<option value="">${message("请选择")}</option>
						<option [#if contract.region==0]selected[/#if] value="0">${message("东区")}</option>
						<option [#if contract.region==1]selected[/#if] value="1">${message("西区")}</option>
						<option [#if contract.region==2]selected[/#if] value="2">${message("南区")}</option>
						<option [#if contract.region==3]selected[/#if] value="3">${message("北区")}</option>
					</select>
				</td>
				<th>
					${message("CRM名称")}:
				</th>
				<td>
					<input type="text" class="text" name="crmName" value="${contract.crmName}" btn-fun="clear" >
				</td>
				<th>${message("直营区域")}:	</th>
                <td ><input type="text" class="text" name="drt_region" value="${contract.drt_region}" btn-fun="clear" ></td>
                <th>${message("经销商联系方式")}:	</th>
                <td ><input type="text" class="text fixedNumber" name="storeFixedNumber" value="${contract.store.fixedNumber}" btn-fun="clear" ></td>
			</tr>
			<tr>
				<th>${message("营业执照")}:	</th>
                <td ><input type="text" class="text" name="business_license" value="${contract.business_license}" btn-fun="clear" ></td>
                <th>${message("固定电话")}:	</th>
                <td ><input type="text" class="text" name="telephone" value="${contract.telephone}" btn-fun="clear" ></td>
                <th><span class="requiredField">*</span>${message("签署状态")}:	</th>
                <td >
					<select id="agr_status" name="agr_status" class="text">
						<option [#if contract.agr_status==0]selected[/#if] value="0">${message("是")}</option>
						<option [#if contract.agr_status==1]selected[/#if] value="1">${message("否")}</option>
					</select>
				</td>
                <th>${message("收货地址")}:	</th>
                <td ><input type="text" class="text" name="consignee_address" value="${contract.consignee_address}" btn-fun="clear" ></td>
			</tr>
			<tr>
                <th>${message("税务登记证")}:	</th>
                <td ><input type="text" class="text" name="tax_registration_certificate" value="${contract.telephone}" btn-fun="clear" ></td>
				<th>${message("合同签署人")}:	</th>
                <td ><input type="text" class="text" name="distributor_signer" value="${contract.distributor_signer}" btn-fun="clear" ></td>
                <th>${message("所属业务实体")}:	</th>
                <td ><input type="text" class="text" name="business_entity" value="${contract.business_entity}" btn-fun="clear" ></td>
                <th>${message("分销区域")}:	</th>
                <td ><input type="text" class="text" name="dst_region" value="${contract.dst_region}" btn-fun="clear" ></td>
			</tr>
			<tr>
				<th>
					${message("创建日期")}:
				</th>
				<td><span>${contract.createDate?string("yyyy-MM-dd HH:mm:ss")}</span></td>
				 <th>${message("乡镇")}:	</th>
                <td ><input type="text" class="text" name="villages" value="${contract.villages}" btn-fun="clear" ></td>
				<th></th>
				<td></td>
				<th></th>
				<td></td>
			</tr>
			<tr>
				<th>
					${message("地区")}:
				</th>
				<td colspan="3">
					<input type="hidden" id="areaId" name="area.id" value="${(contract.area.id)!}" treePath="${(contract.area.treePath)!}"/>
				</td>
				<th></th>
				<td></td>
			</tr>
		</table>
		<!-- <div class="title-style">
			${message("全链路信息")}
		</div>
		<table id="table-full"></table> -->
		<div class="title-style">
			${message("指标卡")}
		 </div>
			<table class="input input-edit" width="1000">
           	<tr>
           		<th>${message("实木月度销量")}:</th>
                <td>
	                <div class="nums-input ov">
	                <input type="hidden" name="contractQuota.id" btn-fun="clear" value="${contract.contractQuota.id}"/>
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t monthly"  name="contractQuota.msosw" value="${contract.contractQuota.msosw}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
                <th>${message("实木年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t annual"  name="contractQuota.asosw" value="${contract.contractQuota.asosw}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("实木出货城市")}:</th>
                <td>
	               <input type="text" name="contractQuota.swsc" value="${contract.contractQuota.swsc}" class="text" btn-fun="clear" />
				</td>
			</tr>
			<tr>
				<th>${message("多层月度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t monthly"  name="contractQuota.mmsv" value="${contract.contractQuota.mmsv}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("多层年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t annual"  name="contractQuota.mysv" value="${contract.contractQuota.mysv}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("多层出货城市")}:</th>
                <td>
	               <input type="text" name="contractQuota.mtsc" class="text" btn-fun="clear" value="${contract.contractQuota.mtsc}"/>
				</td>
           </tr>
		   <tr>
				  <th>${message("三层月度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t monthly"  name="contractQuota.tlmsv" value="${contract.contractQuota.tlmsv}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("三层年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t annual"  name="contractQuota.ttas" value="${contract.contractQuota.ttas}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("三层出货城市")}:</th>
                <td>
	               <input type="text" name="contractQuota.ttsc" class="text" btn-fun="clear" value="${contract.contractQuota.ttsc}"/>
				</td>
			</tr>
			<tr>
				<th>${message("竹地板月度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t monthly"  name="contractQuota.msobf" value="${contract.contractQuota.msobf}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("竹地板年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t annual"  name="contractQuota.asobf" value="${contract.contractQuota.asobf}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
                <th>${message("竹地板出货城市")}:</th>
                <td>
	                <input type="text" name="contractQuota.bfsc" class="text" btn-fun="clear" value="${contract.contractQuota.bfsc}"/>
				</td>
            </tr>
            <tr>
            	<th>${message("强化月度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t monthly"  name="contractQuota.stmsv" value="${contract.contractQuota.stmsv}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("强化年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t annual"  name="contractQuota.stasv" value="${contract.contractQuota.stasv}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("强化出货城市")}:</th>
                <td>
	                <input type="text" name="contractQuota.enhancedSC" class="text" btn-fun="clear" value="${contract.contractQuota.enhancedSC}"/>
				</td>
			</tr>
			<tr>
            	<th>${message("生态月度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t monthly"  name="contractQuota.emsv" value="${contract.contractQuota.emsv}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
                <th>${message("生态年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t annual"  name="contractQuota.aes" value="${contract.contractQuota.aes}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("生态出货城市")}:</th>
                <td>
	               <input type="text" name="contractQuota.esc" class="text" btn-fun="clear" value="${contract.contractQuota.esc}"/>
				</td>
            </tr>
             <tr>
             	<th>${message("直营区域月度合计")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t" id="monthly" name="contractQuota.mtodoa" value="${contract.contractQuota.mtodoa}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("直营区域年度合计")}:</th>
                <td>
	                 <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t" id="atofdoa" name="contractQuota.atofdoa" value="${contractQuota.atofdoa}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th></th>
				<td></td>
			</tr>
			<tr>
            	<th>${message("分销区域实木月度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionMonth"  name="contractQuota.msoswida" value="${contract.contractQuota.msoswida}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
                <th>${message("分销区域实木年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionYear"  name="contractQuota.asoswida" value="${contract.contractQuota.asoswida}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("分销区域实木出货城市")}:</th>
                <td>
	                <input type="text" name="contractQuota.daswsc" class="text" btn-fun="clear" value="${contract.contractQuota.daswsc}"/>
				</td>
			</tr>
			<tr>
				<th>${message("分销区域多层月度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionMonth"  name="contractQuota.mlmsida" value="${contract.contractQuota.mlmsida}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
            	<th>${message("分销区域多层年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionYear"  name="contractQuota.mlasida" value="${contract.contractQuota.mlasida}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
            	<th>${message("分销区域多层出货城市")}:</th>
                <td>
	                <input type="text" name="contractQuota.damsc" class="text" btn-fun="clear" value="${contract.contractQuota.damsc}"/>
				</td>
            </tr>
            <tr>
                <th>${message("分销区域三层月度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionMonth"  name="contractQuota.ttmsida" value="${contract.contractQuota.ttmsida}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("分销区域三层年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionYear"  name="contractQuota.asottida" value="${contract.contractQuota.asottida}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("分销区域三层出货城市")}:</th>
                <td>
	                <input type="text" name="contractQuota.dattsc" class="text" btn-fun="clear" value="${contract.contractQuota.dattsc}"/>
				</td>
            </tr>
            <tr>
				<th>${message("分销区域竹地板月度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionMonth"  name="contractQuota.msobfida" value="${contract.contractQuota.msobfida}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
            	<th>${message("分销区域竹地板年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionYear"  name="contractQuota.asobfida" value="${contract.contractQuota.asobfida}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)"> 
					</div>
				</td>
                <th>${message("分销区域竹地板出货城市")}:</th>
                <td>
	               	<input type="text" name="contractQuota.dabfsc" class="text" btn-fun="clear" value="${contract.contractQuota.dabfsc}"/>
				</td>
			</tr>
            <tr>
				<th>${message("分销区域强化月度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionMonth"  name="contractQuota.smsida" value="${contract.contractQuota.smsida}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("分销区域强化年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-" onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionYear"  name="contractQuota.daeas" value="${contract.contractQuota.daeas}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("分销区域强化出货城市")}:</th>
                <td>
	                <input type="text" name="contractQuota.dassc" class="text" btn-fun="clear" value="${contract.contractQuota.dassc}"/>
				</td>
			</tr>
            <tr>
				<th>${message("分销区域生态月度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionMonth"  name="contractQuota.mesida" value="${contract.contractQuota.mesida}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
                <th>${message("分销区域生态年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionYear"  name="contractQuota.asoeida" value="${contract.contractQuota.asoeida}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)"> 
					</div>
				</td>
				<th>${message("分销区域生态出货城市")}:</th>
                <td>
	                <input type="text" name="contractQuota.daesc" class="text" btn-fun="clear" value="${contract.contractQuota.daesc}"/>
				</td>
			  </tr>
              <tr>
				<th>${message("分销区域月度合计")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t" id="distributionMonth" name="contractQuota.tmda" value="${contract.contractQuota.tmda}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("分销区域年度合计")}:</th>
                <td>
	                 <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t"  id="tada" name="contractQuota.tada" value="${contract.contractQuota.tada}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)"> 
					</div>
				</td>
				<th>${message("销量指标（直营+分销）年度合计")}:</th>
                <td>
	                 <input type="text" style="border: 0px;text-align:center;color:black;" class="text" id="aasi"  name="contractQuota.aasi" [#if contract.contractQuota.aasi??]value="${contract.contractQuota.aasi}" [#else]value="0"[/#if]  readonly="readonly"/>
				</td>
			 </tr>
            <tr>
                <th>${message("工程指标")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal(this,event)">
					<input type="text"  class="t"  id="engineeringIndex" name="contractQuota.engineeringIndex" value="${contract.contractQuota.engineeringIndex}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal(this,event)">
					</div>
				</td>
				<th>${message("电商销量指标")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal(this,event)">
					<input type="text"  class="t" id="esst" name="contractQuota.esst" value="${contract.contractQuota.esst}"  oninput="editTotalQuota (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal(this,event)">
					</div>
				</td>
				<th>${message("家装套餐销量指标")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal(this,event)">
					<input type="text"  class="t" id="siohdp" name="contractQuota.siohdp" value="${contract.contractQuota.siohdp}"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal(this,event)">
					</div>
				</td>
			 </tr>
            <tr>
				<th>${message("合计")}:</th>
                <td>
	                 <input type="text" style="border: 0px;text-align:center;color:black;" class="text" id="totalQuota"  name="contractQuota.total" value="${contract.contractQuota.total}" readonly="readonly"/>
				</td>
				<th>${message("现有门店数量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
					<input type="text"  class="t"  name="contractQuota.noes" value="${contract.contractQuota.noes}"  oninput="editSale (this,event)" onpropertychange="editSale (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
					</div>
				</td>
				<th>${message("年度门店保有量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
					<input type="text"  class="t"  name="contractQuota.aso" value="${contract.contractQuota.aso}"  oninput="editSale (this,event)" onpropertychange="editSale (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
					</div>
				</td>
			 </tr>
            <tr>
				<th>${message("新增指标")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
					<input type="text"  class="t"  name="contractQuota.newIndicators" value="${contract.contractQuota.newIndicators}"  oninput="editSale (this,event)" onpropertychange="editSale (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
					</div>
				</td>
               	<th>${message("重装指标")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
					<input type="text"  class="t"  name="contractQuota.reloadIndex" value="${contract.contractQuota.reloadIndex}"  oninput="editSale (this,event)" onpropertychange="editSale (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
					</div>
				</td>
				<th>${message("全国促销活动实施场次")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
					<input type="text"  class="t"  name="contractQuota.nonpe" value="${contract.contractQuota.nonpe}"  oninput="editSale (this,event)" onpropertychange="editSale (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
					</div>
				</td>
            </tr>
               <tr>
            	<th>${message("期初库存")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
					<input type="text"  class="t"  name="contractQuota.openingInventory" value="${contract.contractQuota.openingInventory}"  oninput="editSale (this,event)" onpropertychange="editSale (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
					</div>
				</td>
                <th>${message("期末库存")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
					<input type="text"  class="t"  name="contractQuota.endingInventory" value="${contract.contractQuota.endingInventory}"  oninput="editSale (this,event)" onpropertychange="editSale (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
					</div>
				</td>
				<th>${message("Nature合计指标")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
					<input type="text"  class="t"  name="contractQuota.natureAggregateIndex" value="${contract.contractQuota.natureAggregateIndex}"  oninput="editSale (this,event)" onpropertychange="editSale (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
					</div>
				</td>
            </tr>
               <tr>
				<th>${message("审核情况")}:</th>
                <td>
	               <select  name="contractQuota.auditSituation" class="text">
	               		<option value="">${message("请选择")}</option>
			 	 		<option [#if contract.contractQuota.auditSituation==1]selected[/#if] value="1">${message("合格")}</option>
						<option [#if contract.contractQuota.auditSituation==2]selected[/#if] value="2">${message("不合格")}</option>
			   		</select>
				</td>
            	<th>${message("审核情况备注")}:</th>
                <td colspan="2">
					<textarea name="contractQuota.auditNotes" id="auditNotes" class="text" >${contract.contractQuota.auditNotes}</textarea>
				</td>
            </tr>
         </table>
		<div class="title-style">
			${message("附件信息")}
			<div class="btns">
				<a href="javascript:;" id="addAttach" class="button">添加附件</a>
			</div>
		</div>
		<table id="table-attach"></table>
		</div>
		<div class="fixed-top">
			<a href="/b2b/contract/add/${code}.jhtml" class="iconButton" id="addButton">
				<span class="addIcon">&nbsp;</span>${message("1001")}
			</a>
				<input type="button" id="submit_button" class="button sureButton" value="${message("保存")}" onclick="save(this)">
		    
		    <input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
	</form>
	<div id="wf_area" style="width:100%"></div>
</body>
</html>