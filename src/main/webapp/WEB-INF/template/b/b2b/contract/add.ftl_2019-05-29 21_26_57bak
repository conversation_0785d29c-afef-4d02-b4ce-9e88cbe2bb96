<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("新增合同")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<style>
	tr.s-tr,tr.s-tr td{height:10px !important;}
	div.w_1135{ width: 1135px;}
</style>
<script type="text/javascript">
function editPrice(t,e){
	extractNumber(t,2,true,e)
}
function editSale(t,e){
	extractNumber(t,2,true,e)
}

function totalSale(){
	var totalSale = 0;
	var $atofadoaValue = $("#atofdoa").val();
	var $tadaValue = $("#tada").val();
	var totalAnnual = 0;
 	$("input.annual").each(function(){
		var $inputAnnual = $(this).val();
		if($inputAnnual=="" || $inputAnnual==null ){
			$inputAnnual = 0.00;
	 	}
		totalAnnual = accAdd($inputAnnual,totalAnnual);
	})
	$("#atofdoa").attr('value',totalAnnual); 
 	if($tadaValue=="" || $tadaValue==null ){
 		$tadaValue = 0.00;
 	}
 	if($atofadoaValue=="" || $atofadoaValue==null ){
 		$atofadoaValue = 0.00;
 	}
	var $monthlyValue = $("#monthly").val();
	var totalMonthly = 0;
	$("input.monthly").each(function(){
		var $inputMonthly = $(this).val();
		if($inputMonthly=="" || $inputMonthly==null ){
			$inputMonthly = 0.00;
	 	}
		totalMonthly = accAdd($inputMonthly,totalMonthly);
	})
	$("#monthly").attr('value',totalMonthly);
	
	var $distributionMonthVal = $("#distributionMonth").val();
	var totalDistributionMonth = 0 ;
	$("input.distributionMonth").each(function(){
		var $thisValue = $(this).val();
		if($thisValue=="" || $thisValue==null ){
			$thisValue = 0.00;
	 	}
		totalDistributionMonth = accAdd($thisValue,totalDistributionMonth);
	})
	$("#distributionMonth").attr('value',totalDistributionMonth);
	
	var totalDistributionYear = 0;
	$("input.distributionYear").each(function(){
		var $thisValue = $(this).val();
		if($thisValue=="" || $thisValue==null ){
			$thisValue = 0.00;
	 	}
		totalDistributionYear = accAdd(totalDistributionYear,$thisValue);
	})
	$("#tada").attr('value',totalDistributionYear);
	
	totalSale = accAdd(totalAnnual,totalDistributionYear);
	$("#aasi").attr('value',totalSale);
	
	//合计
	var totalQuota = 0;
	var $engineeringIndexValue = $("#engineeringIndex").val();
	var $esstValue = $("#esst").val();
	var $siohdpValue = $("#siohdp").val();
	totalQuota = accAdd($engineeringIndexValue,$esstValue);
	totalQuota = accAdd(totalQuota,$siohdpValue);
	totalQuota = accAdd($("#aasi").val(),totalQuota);
	$("#totalQuota").attr("value",totalQuota);
	
	$(".increase").each(function(){
		$(this).prop("value","+");
	})
}

function editSaleTotal(t,e){
	if(extractNumber(t,2,true,e)){
		totalSale();
	}
}


$().ready(function() {
	var $inputForm = $("#inputForm");
	var $bAreaId = $("#areaId");
	$bAreaId.lSelect();
	var $submitButton = $("#submitButton");
	$("input[name='image']").single_upload({
		uploadSize:"source"
	});
	$("input[name='image2']").single_upload({
		uploadSize:"source"
	});
	$("input[name='image3']").single_upload({
		uploadSize:"source"
	});
	
	var d = new Date();
	var month=d.getMonth()+1;
	$("input.balanceMonth").val(d.getFullYear()+"-"+(month<10?'0'+month:month));
	
	// 表单验证
	$inputForm.validate({
		rules: {
			storeName: {
				required: true
			},
			amount: {
				required: true,
// 				min:0.001,
				decimal: {
					integer: 12,
					fraction: ${setting.priceScale}
				}
			},
			balanceMonth: {
				required: true
			}
		} 
	});
	
	/**初始化订单附件*/
    var contractAttachIndex=0;
	var cols = [				
    	{ title:'${message("附件")}', name:'content' ,width:260,align:'center',renderer:function(val,item,rowIndex){
    		var url = item.url;
			var fileObj = getfileObj(item.name);
			/**设置隐藏值*/
			var hideValues = {};
			hideValues['contractAttachs['+contractAttachIndex+'].url']=url;
			hideValues['contractAttachs['+contractAttachIndex+'].suffix']=fileObj.suffix;
			
			return createFileStr({
				url : url,
				fileName : fileObj.file_name,
				name : fileObj.name,
				suffix : fileObj.suffix,
				time : '',
				textName:'contractAttachs['+contractAttachIndex+'].name',
				hideValues:hideValues
			});               
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:590 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text" name="contractAttachs['+contractAttachIndex+'].memo" >'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
			contractAttachIndex++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}}
	];
	var $amGrid=$('#table-attach').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        checkCol: false,
        autoLoad: true
    });
    
    
    var $addAttach = $("#addAttach");
	var attachIdnex = 0;
	var option1 = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row = data[i].file_info;
				$amGrid.addRow(row,null,1);
	        }
			
        }
    }
    $addAttach.file_upload(option1);
    
     var $deleteAttachment = $(".deleteAttachment");
	$deleteAttachment.live("click", function() {
		var $this = $(this);
		$this.closest("tr").remove();
	});
    
	var cols = [				
    	{ title:'${message("内容")}', name:'content' ,width:300,align:'center'},
		{ title:'${message("操作人")}', name:'operator_name',width:100 ,align:'center'},
		{ title:'${message("创建日期")}', name:'create_date' ,width:150 ,align:'center'}
	];
	$('#table-full').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        checkCol: false,
        autoLoad: true
    });
	
	$("#openArea").bindQueryBtn({
		type:'area',
		title:'${message("查询地区")}',
		url:'/basic/area/select_area.jhtml',
		callback:function(rows){
			if(rows.length>0){
				var $tr =$this.closest("tr");
				$(".areaId").val(rows[0].id);
				$(".areaName").val(rows[0].full_name);
				//$("input[name='address']").val('');
				$("input[name='zipCode']").val('');
			}
		}
	});	
	
	$("form").bindAttribute({
		isConfirm:true,
		callback: function(resultMsg){
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.href= '/b2b/contract/edit/${code}.jhtml?id='+resultMsg.objx;
			});
		}
	 });
	
	//查询客户
	 $("#selectStore").click(function(){
		$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		bindClick:false,
		url:'/member/store/select_store.jhtml?type=distributor&isMember=1',
		callback:function(rows){
			if(rows.length>0){
				var row = rows[0];
				$(".storeName").val(row.name);
				$(".storeId").val(row.id);
				$(".fixedNumber").val(row.fixed_number);
				$(".saleOrgName").val(row.sale_org_name);
				$(".saleOrgId").val(row.sale_org_id);
				
			}
		}
		});
	});
	
	//查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml'
	});
	
});

function clearSelect(e){
	var $this = $(e);
	var value = $this.val();
	if(value==undefined || value.length==0){
		var $tr = $(e).closest("div.search");
		$this.prev("input").val("");
		$(".bankName").text('');
	}
}

function totalQuota(){
	var totalQuota = 0;
	var $engineeringIndexValue = $("#engineeringIndex").val();
	var $esstValue = $("#esst").val();
	var $siohdpValue = $("#siohdp").val();
	totalQuota = accAdd($engineeringIndexValue,$esstValue);
	totalQuota = accAdd(totalQuota,$siohdpValue)
	$("#totalQuota").attr("value",totalQuota);
}

function editTotalQuota(t,e){
	if(extractNumber(t,2,true,e)){
		totalQuota();
	}
}

</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增合同")}
	</div>
	<form id="inputForm" action="/b2b/contract/save.jhtml" method="post" type="ajax" validate-type="validate">

		<div class="tabContent">
			<table class="input input-edit">
			<tr>
				<th>
					<span class="requiredField">*</span>${message("合同编号")}:
				</th>
				<td>
					<input type="text" class="text contract_no" name="contract_no" value="" btn-fun="clear" />
				</td>
				<th>
					<span class="requiredField">*</span>${message("经销商")}:
				</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="storeId" class="text storeId" value="[#if storeMember.memberType == 1]${store.id}[/#if]" btn-fun="clear"/>
					<input type="text" name="storeName" class="text storeName" value="[#if storeMember.memberType == 1]${store.name}[/#if]" maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
					<input type="button" class="iconSearch" value="" id="selectStore">
					</span>
				</td>
				<th>
					${message("品牌保证金")}:
				</th>
				<td>
					<div class="nums-input ov">
						<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
						<input type="text"  class="t"  name="brand_caution_money" value=""  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >
						<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
					</div>				
				</td>
				<th>
					${message("收货人")}:
				</th>
				<td>
					<input type="text" class="text consignee" name="consignee" value="" btn-fun="clear" />
				</td>
			</tr>
			<tr>
				<th>${message("机构")}:</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value="${saleOrg.id}"/>
					<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" value="${saleOrg.name}" readOnly/>
					<input type="hidden" class="iconSearch" value="" id="selectSaleOrg">
					</span>
				</td>
				<th>${message("合同主体")}:	</th>
                <td ><input type="text" class="text" name="contract_body" value="" btn-fun="clear" ></td>
				<th>${message("签署人身份证")}:</th>
				<td>
					<input type="text" class="text distributor_signer_id_card" name="distributor_signer_id_card" value="" btn-fun="clear" />
				</td>
				
				<th>
					${message("收货人联系方式")}:
				</th>
				<td>
					<input type="text" class="text consignee_phone" name="consignee_phone" value="" btn-fun="clear" />
				</td>
			</tr>
			<tr>
				<th>
					${message("区域")}:
				</th>
				<td>
					<select id="region" name="region" class="text">
						<option value="">${message("请选择")}</option>
						<option value="0">${message("东区")}</option>
						<option value="1">${message("西区")}</option>
						<option value="2">${message("南区")}</option>
						<option value="3">${message("北区")}</option>
					</select>
				</td>
			<th>
					${message("CRM名称")}:
				</th>
				<td>
					<input type="text" class="text" name="crmName" value="" btn-fun="clear" >
				</td>
				<th>${message("直营区域")}:	</th>
                <td ><input type="text" class="text" name="drt_region" value="" btn-fun="clear" ></td>
                <th>${message("经销商联系方式")}:	</th>
                <td ><input type="text" class="text fixedNumber" name="storeFixedNumber" value="" btn-fun="clear" ></td>
			</tr>
			<tr>
				<th>${message("营业执照")}:	</th>
                <td ><input type="text" class="text" name="business_license" value="" btn-fun="clear" ></td>
                <th>${message("固定电话")}:	</th>
                <td ><input type="text" class="text" name="telephone" value="" btn-fun="clear" ></td>
                <th><span class="requiredField">*</span>${message("签署状态")}:	</th>
                <td >
					<select id="agr_status" name="agr_status" class="text">
						<option value="0">${message("是")}</option>
						<option value="1">${message("否")}</option>
					</select>
				</td>
                <th>${message("收货地址")}:	</th>
                <td ><input type="text" class="text" name="consignee_address" value="" btn-fun="clear" ></td>
			</tr>
			<tr>
                <th>${message("税务登记证")}:	</th>
                <td ><input type="text" class="text" name="tax_registration_certificate" value="" btn-fun="clear" ></td>
				<th>${message("合同签署人")}:	</th>
                <td ><input type="text" class="text" name="distributor_signer" value="" btn-fun="clear" ></td>
                <th>${message("所属业务实体")}:	</th>
                <td ><input type="text" class="text" name="business_entity" value="" btn-fun="clear" ></td>
                <th>${message("分销区域")}:	</th>
                <td ><input type="text" class="text" name="dst_region" value="" btn-fun="clear" ></td>
			</tr>
			<tr>
				<th>
					${message("创建日期")}:
				</th>
				<td></td>
				<th>${message("乡镇")}:	</th>
                <td ><input type="text" class="text" name="villages" value="" btn-fun="clear" ></td>
				<th></th>
				<td></td>
				<th></th>
				<td></td>
			</tr>
			<tr>
				<th>
					${message("地区")}:
				</th>
				<td colspan="3">
					<input type="hidden" id="areaId" name="area.id"/>
				</td>
			</tr>
		</table>
		<!-- <div class="title-style">
			${message("全链路信息")}
		</div>
		<table id="table-full"></table> -->
		<div class="title-style">
			${message("指标卡")}
		 </div>
			<table class="input input-edit" width="1000">
           	<tr>
           		<th>${message("实木月度销量")}:</th>
                <td>
	                <div class="nums-input">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t monthly"  name="contractQuota.msosw" value="" minData="0" oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" class="b increase" value="+"   onMouseDown ="increase(this,event)"  onMouseUp="editSaleTotal (this,event)"> 
					</div>
				</td>
                <th>${message("实木年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)"> 
					<input type="text"  class="t annual"  name="contractQuota.asosw" value="" minData="0" oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("实木出货城市")}:</th>
                <td>
	               <input type="text" name="contractQuota.swsc" value="" class="text" btn-fun="clear" />
				</td>
				</tr>
				<tr>
				<th>${message("多层月度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t monthly"  name="contractQuota.mmsv" value="" minData="0"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"  onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("多层年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t annual"  name="contractQuota.mysv" value="" minData="0" oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("多层出货城市")}:</th>
                <td>
	               <input type="text" name="contractQuota.mtsc" class="text" btn-fun="clear" value=""/>
				</td>
           </tr>
		   <tr>
				  <th>${message("三层月度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t monthly"  name="contractQuota.tlmsv" value="" minData="0" oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"  onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("三层年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t annual"  name="contractQuota.ttas" value="" minData="0" oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("三层出货城市")}:</th>
                <td>
	               <input type="text" name="contractQuota.ttsc" class="text" btn-fun="clear" value=""/>
				</td>
				</tr>
				<tr>
				<th>${message("竹地板月度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t monthly"  name="contractQuota.msobf" value="" minData="0" oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"  onMouseUp="editSaleTotal (this,event)"> 
					</div>
				</td>
				<th>${message("竹地板年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t annual"  name="contractQuota.asobf" value="" minData="0" oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
                <th>${message("竹地板出货城市")}:</th>
                <td>
	                <input type="text" name="contractQuota.bfsc" class="text" btn-fun="clear" value=""/>
				</td>
            </tr>
            <tr>
            	<th>${message("强化月度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t monthly"  name="contractQuota.stmsv" value="" minData="0" oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"  onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("强化年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t annual"  name="contractQuota.stasv" value="" minData="0" oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("强化出货城市")}:</th>
                <td>
	                <input type="text" name="contractQuota.enhancedSC" class="text" btn-fun="clear" value=""/>
				</td>
				</tr>
				<tr>
            	<th>${message("生态月度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t monthly"  name="contractQuota.emsv" value="" minData="0" oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"  onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
                <th>${message("生态年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t annual"  name="contractQuota.aes" value="" minData="0" oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("生态出货城市")}:</th>
                <td>
	               <input type="text" name="contractQuota.esc" class="text" btn-fun="clear" value=""/>
				</td>
            </tr>
             <tr>
             	<th>${message("直营区域月度合计")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)" >
					<input type="text"  class="t" id="monthly" name="contractQuota.mtodoa" value="" minData="0" oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)" >
					</div>
				</td>
				<th>${message("直营区域年度合计")}:</th>
                <td>
	                 <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal (this,event)" />
					<input type="text"  class="t" id="atofdoa" name="contractQuota.atofdoa" value="" minData="0"  oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal (this,event)" />
					</div>
				</td>
            	<th></th>
            	<td></td>
				</tr>
				<tr>
				<th>${message("分销区域实木月度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionMonth"  name="contractQuota.msoswida" value="" minData="0" oninput="editSaleTotal(this,event)" onpropertychange="editSaleTotal(this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"  onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
                <th>${message("分销区域实木年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionYear"  name="contractQuota.asoswida" value="" minData="0" oninput="editSaleTotal(this,event)" onpropertychange="editSaleTotal(this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"  onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("分销区域实木出货城市")}:</th>
                <td>
	                <input type="text" name="contractQuota.daswsc" class="text" btn-fun="clear" value=""/>
				</td>
			</tr>
			<tr>
				<th>${message("分销区域多层月度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionMonth"  name="contractQuota.mlmsida" value="" minData="0" oninput="editSaleTotal(this,event)" onpropertychange="editSaleTotal(this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"  onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
            	<th>${message("分销区域多层年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionYear"  name="contractQuota.mlasida" value="" minData="0" oninput="editSaleTotal(this,event)" onpropertychange="editSaleTotal(this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"  onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
            	<th>${message("分销区域多层出货城市")}:</th>
                <td>
	                <input type="text" name="contractQuota.damsc" class="text" btn-fun="clear" value=""/>
				</td>
			</tr>
			<tr>
                <th>${message("分销区域三层月度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionMonth"  name="contractQuota.ttmsida" value="" minData="0" oninput="editSaleTotal(this,event)" onpropertychange="editSaleTotal(this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"  onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("分销区域三层年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionYear"  name="contractQuota.asottida" value="" minData="0" oninput="editSaleTotal(this,event)" onpropertychange="editSaleTotal(this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"  onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("分销区域三层出货城市")}:</th>
                <td>
	                <input type="text" name="contractQuota.dattsc" class="text" btn-fun="clear" value=""/>
				</td>
			</tr>
			<tr>
				<th>${message("分销区域竹地板月度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionMonth"  name="contractQuota.msobfida" value="" minData="0" oninput="editSaleTotal(this,event)" onpropertychange="editSaleTotal(this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"  onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
            	<th>${message("分销区域竹地板年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionYear"  name="contractQuota.asobfida" value="" minData="0" oninput="editSaleTotal(this,event)" onpropertychange="editSaleTotal(this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"  onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
                <th>${message("分销区域竹地板出货城市")}:</th>
                <td>
	               	<input type="text" name="contractQuota.dabfsc" class="text" btn-fun="clear" value=""/>
				</td>
			</tr>
			<tr>
				<th>${message("分销区域强化月度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionMonth"  name="contractQuota.smsida" value="" minData="0" oninput="editSaleTotal(this,event)" onpropertychange="editSaleTotal(this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"  onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("分销区域强化年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionYear"  name="contractQuota.daeas" value="" minData="0" oninput="editSaleTotal(this,event)" onpropertychange="editSaleTotal(this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"  onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("分销区域强化出货城市")}:</th>
                <td>
	                <input type="text" name="contractQuota.dassc" class="text" btn-fun="clear" value=""/>
				</td>
				</tr>
				<tr>
				<th>${message("分销区域生态月度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionMonth"  name="contractQuota.mesida" value="" minData="0" oninput="editSaleTotal(this,event)" onpropertychange="editSaleTotal(this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"  onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
                <th>${message("分销区域生态年度销量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"  onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t distributionYear"  name="contractQuota.asoeida" value="" minData="0" oninput="editSaleTotal(this,event)" onpropertychange="editSaleTotal(this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"  onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("分销区域生态出货城市")}:</th>
                <td>
	                <input type="text" name="contractQuota.daesc" class="text" btn-fun="clear" value=""/>
				</td>
			</tr>
            <tr>
				<th>${message("分销区域月度合计")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-" onMouseDown ="decrease(this,event)"  onMouseUp="editSaleTotal (this,event)">
					<input type="text"  class="t" id="distributionMonth" name="contractQuota.tmda" value="" minData="0" oninput="editSaleTotal(this,event)" onpropertychange="editSaleTotal(this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"  onMouseUp="editSaleTotal (this,event)">
					</div>
				</td>
				<th>${message("分销区域年度合计")}:</th>
                <td>
	                 <div class="nums-input ov">
					<input type="button" class="b decrease" value="-" onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal(this,event)" />
					<input type="text"  class="t"  id="tada" name="contractQuota.tada" value="" minData="0" oninput="editSaleTotal (this,event)" onpropertychange="editSaleTotal (this,event)" >
					<input type="button" value="+" class="b increase" onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal(this,event)" />
					</div>
				</td>
				<th>${message("销量指标（直营+分销）年度合计")}:</th>
                <td>
	                 <input type="text" style="border: 0px;text-align:center;color:black;" class="text" id="aasi" minData="0" name="contractQuota.aasi" value="" readonly="readonly"/>
				</td>
			</tr>
			<tr>
                <th>${message("工程指标")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal(this,event)">
					<input type="text"  class="t" id="engineeringIndex"  name="contractQuota.engineeringIndex" value="" minData="0" oninput="editSaleTotal(this,event)" onpropertychange="editSaleTotal(this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal(this,event)">
					</div>
				</td>
				<th>${message("电商销量指标")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal(this,event)">
					<input type="text"  class="t" id="esst"  name="contractQuota.esst" value="" minData="0" oninput="editSaleTotal(this,event)" onpropertychange="editSaleTotal(this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal(this,event)">
					</div>
				</td>
				<th>${message("家装套餐销量指标")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editSaleTotal(this,event)">
					<input type="text"  class="t" id="siohdp" name="contractQuota.siohdp" value=""  minData="0" oninput="editSaleTotal(this,event)" onpropertychange="editSaleTotal(this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editSaleTotal(this,event)">
					</div>
				</td>
			</tr>
			<tr>
				<th>${message("合计")}:</th>
                <td>
	                 <input type="text" style="border: 0px;text-align:center;color:black;" class="text" id="totalQuota" minData="0" name="contractQuota.total" value="" readonly="readonly"/>
				</td>
				<th>${message("现有门店数量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
					<input type="text"  class="t"  name="contractQuota.noes" value=""  oninput="editSale (this,event)" onpropertychange="editSale (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
					</div>
				</td>
				<th>${message("年度门店保有量")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
					<input type="text"  class="t"  name="contractQuota.aso" value=""  oninput="editSale (this,event)" onpropertychange="editSale (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
					</div>
				</td>
			</tr>
			<tr>
				<th>${message("新增指标")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
					<input type="text"  class="t"  name="contractQuota.newIndicators" value=""  oninput="editSale (this,event)" onpropertychange="editSale (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
					</div>
				</td>
               	<th>${message("重装指标")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
					<input type="text"  class="t"  name="contractQuota.reloadIndex" value=""  oninput="editSale (this,event)" onpropertychange="editSale (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
					</div>
				</td>
				<th>${message("全国促销活动实施场次")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
					<input type="text"  class="t"  name="contractQuota.nonpe" value=""  oninput="editSale (this,event)" onpropertychange="editSale (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
					</div>
				</td>
			  </tr>
               <tr>
            	<th>${message("期初库存")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
					<input type="text"  class="t"  name="contractQuota.openingInventory" value=""  oninput="editSale (this,event)" onpropertychange="editSale (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
					</div>
				</td>
                <th>${message("期末库存")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
					<input type="text"  class="t"  name="contractQuota.endingInventory" value=""  oninput="editSale (this,event)" onpropertychange="editSale (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
					</div>
				</td>
				<th>${message("Nature合计指标")}:</th>
                <td>
	                <div class="nums-input ov">
					<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
					<input type="text"  class="t"  name="contractQuota.natureAggregateIndex" value=""  oninput="editSale (this,event)" onpropertychange="editSale (this,event)" >
					<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
					</div>
				</td>
			</tr>
				<tr>
				<th>${message("审核情况")}:</th>
                <td>
	               <select  name="contractQuota.auditSituation" class="text">
	               		<option value="">${message("请选择")}</option>
			 	 		<option value="1">${message("合格")}</option>
						<option value="2">${message("不合格")}</option>
			   		</select>
				</td>
            		<th>${message("审核情况备注")}:</th>
                <td colspan="2">
					<textarea name="contractQuota.auditNotes" id="auditNotes" class="text" >附备注</textarea>
				</td>
            </tr>
         </table>
		<div class="title-style">
			${message("附件信息")}
			<div class="btns">
				<a href="javascript:;" id="addAttach" class="button">添加附件</a>
			</div>
		</div>
		<table id="table-attach"></table>
		</div>
		<div class="fixed-top">
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
	</form>
</body>
</html>