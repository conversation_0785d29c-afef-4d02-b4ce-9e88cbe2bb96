<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<title>${message("编辑产品")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/layout.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/zTreeStyle.css" rel="stylesheet" type="text/css">
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/plugin/editor/kindeditor.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.ztree.core-3.5.js"></script>
<style>
#inputForm>.tabContent{position:relative;}
tr.productPartsTr .upload-list.upload-pic .a-upload,tr.productPartsTr .upload-list.upload-pic .ul-box .pic{width:60px;height:60px}
tr.productPartsTr .upload-list.upload-pic .ul-box{width:60px;height:60px;margin:0;float:none;display:inline-block}
.box-wrap01{clear:both}
.box-wrap01 dl{float:left;padding-left:50px}
.box-wrap01 dt{float:left;width:100px}
.box-wrap01 dd{float:left}
label{display:inline-block}
/* .upload-list.upload-pic .ul-box .pic a{
	width: 60px;
	height: 60px;	
} */
.subnav{
margin-top:10px;
}
</style>
<script type="text/javascript">
[#assign attachmentIndex = 0]
var zTree;
$().ready(function(e) {
 	zTree = $("#js-menu").initTree({
		url:'/product/product_attach_category/getNodes.jhtml',
		otherParam:{pcId:function(){
			return $("#previousProductCategoryId2").val();
		}},
		onClick:function(e,treeId, treeNode) {
	        
	        //----------------------------------
	       	if(treeNode.id!=0||treeNode.id==''){
	        	var a_div=$(".attach_content_n").find("div.a_"+treeNode.id);
	        	if(a_div.length==0){
	        		var html='<div class="pro-cont attach_div a_'+treeNode.id+'"><div class="specboxT"><span>当前分类:<span id="category_name">'+treeNode.name+'</span></span></span><a href="javascript:void(0);" class="iconButton addAttachment" style="float: right;margin-right: 0;margin-top: 0.5px;">增加附件</a><input class="att" value="'+treeNode.id+'" type="hidden"></div>'+
                	'<div class="clearfix"></div><ul class="pro-c-list"></ul></div>';
                	$(".attach_content_n").append(html);
	        	
	        	}
	        	$("div.attach_div").hide();
	        	$("div.a_"+treeNode.id).show();
	       	}
	        
// 	        ajaxSubmit($(e),{
// 				method:'get',
// 				url:"get_product_attachs.jhtml",
// 				data: {categoryId: treeNode.id, productId: ${product.id}},
// 				callback: function(data) {
// 					var data = $.parseJSON(resultMsg.content);
// 					var item=data.objx;
// 					if (item.longitude!=null && item.latitude!=null){
// 						setMarker(item, treeNode);
// 						showRangeFences(item.latitude, item.longitude);
// 					}
// 				}
// 			});
	        //---------------------------------
	        
		}
	})
     zTree.addNodes(null,{'id':'','pId':'','name':'其他分类','isParent':false});
    treeScoll();
    
  
}); 
function queryTree(){
	zTree.reAsyncChildNodes(null, "refresh");
	zTree.addNodes(null,{'id':'','pId':'','name':'其他分类','isParent':false});
	$(".attach_div").remove();
}

</script>
<script type="text/javascript">
var attachmentIndex = 0;
function editPrice(t,e){
	
	extractNumber(t,2,false,e)
}

function editQty(t,e,n){
	calcutePerBranch();
	var rate = 3;
	if(n!=undefined)rate = n;
	extractNumber(t,rate,false,e);
}
function aaa(t,e,n){
	$("#perBranch").val("");
	$("#branchPerBox").val("");
	var rate = 3;
	if(n!=undefined)rate = n;
	extractNumber(t,rate,false,e);
}
$().ready(function() {
	//设置不能点右键
	
	$(".tabContent").css("min-height",$(window).height()-133+'px');  //133算出距离顶部的大小
	var attachNum=${(productAttachs ? size)!"0"};
	var $inputForm = $("#inputForm");
	var $addSbu = $("#addSbu");
	var $productCategoryId = $("#productCategoryId2");
	var $is_area_name = $("#is_area_name");
	var $all_checked = $("#all_checked");
	var $all_not_checked = $("#all_not_checked");
	var $all_checked_button = $("#all_checked_button");
	var $all_not_checked_button = $("#all_not_checked_button");
	var $area_show = $("#area_show");
	var $isMemberPrice = $("#isMemberPrice");
	var $memberPriceTr = $("#memberPriceTr");
	var $memberPrice = $("#memberPriceTr input");
	var $productImageTable = $("#productImageTable");
	var $addProductImage = $("#addProductImage");
	var $deleteProductImage = $("a.deleteProductImage");
	var $parameterTable = $("#parameterTable");
	var $attributeTable = $("#attributeTable");
	var $specificationIds = $("#specificationSelect :checkbox");
	var $specificationProductTable = $("#specificationProductTable");
	var $addSpecificationProduct = $("#addSpecificationProduct");
	var $deleteSpecificationProduct = $("a.deleteSpecificationProduct");
	var productImageIndex = ${(product.productImages?size)!"0"};
	var previousProductCategoryId = "${product.productCategory.id}";
	var $usePartnerSingle = $("input[name='usePartnerSingle']");
	var $partnerSingle = $("#partnerSingle");
	var $fixedProfit = $("#fixedProfit");
	var $addProductImageList=$("#addProductImageList");
	var $addProductParts = $("#addProductParts");
	var productPartsIndex = ${(product.productParts?size)!0};
	var $productPartsTable = $("#productPartsTable");
	var $deleteParts = $("a.deleteParts");
	var $addAttachment = $("a.addAttachment");
	
	//查询sbu
	$("#addSbu").bindQueryBtn({
        type:'sbu',
        title:'${message("查询sbu")}',
        url:'/basic/sbu/select_sbu.jhtml?multi=2',
        callback:function(rows){
        	if(rows.length>0){
	        	 for (var i = 0; i < rows.length;i++) {
	        		 var idH = $(".sbuId_"+rows[i].id).length;
	                 if(idH > 0){
	                	 $.message_alert('sbu['+rows[i].name+']已添加');
	                     return false;
	                 }
	        	 }
				for (var i = 0; i < rows.length;i++) {
					var row = rows[i];
					$mmGrid.addRow(row,null,1);
				}
			}
		}
	})
	
	var countAddress = 0;
	var cols = [
	    		{ title:'${message("Sbu")}', align:'center',name:'sbu_name',width:300,renderer: function(val,item,rowIndex){
	    			var sbu_name =item.name;
					var sbuId =item.id;
					if(item.sbu!=null){
					sbuId = item.sbu;
					}
					if(item.name==null){
					sbu_name = item.sbu_name;
					}
	    			   return '<input type="hidden" name="productSbu['+countAddress+'].sbu.id" class="text sbuId sbuId_'+sbuId+'" value="'+sbuId+'"  />'+sbu_name;
	    		}},
	    		
	    		{ title:'${message("设置")}', align:'center',renderer: function(val,item,rowIndex,obj){
	    			if(obj==undefined){
	    				if(item.is_default){
		    				return '<label onClick="sz(this)">'+
		    				'<input type="checkbox" name="productSbu['+countAddress+'].isDefault" class="isDefaultCheckbox" checked="true"/>是否默认'+
		    				'<input type="hidden" name="productSbu['+countAddress+'].isDefault" class="isDefault" value="1"  /></label>';
	    				}else{
		    				return '<label onClick="sz(this)">'+
		    				'<input type="checkbox" name="productSbu['+countAddress+'].isDefault" class="isDefaultCheckbox"  />是否默认'+
		    				'<input type="hidden" name="productSbu['+countAddress+'].isDefault" class="isDefault" value="0" /></label>';
	    				}
	    			}else{
	    				if($("input[value=1].isDefault").length>0){
	    				return '<label onClick="sz(this)">'+
	    				'<input type="checkbox" name="productSbu['+countAddress+'].isDefault" class="isDefaultCheckbox" />是否默认'+
	    				'<input type="hidden" name="productSbu['+countAddress+'].isDefault" class="isDefault" value="0"  /></label>';
	    				}else{
	    				return '<label onClick="sz(this)">'+
	    				'<input type="checkbox" name="productSbu['+countAddress+'].isDefault" class="isDefaultCheckbox" checked />是否默认'+
	    				'<input type="hidden" name="productSbu['+countAddress+'].isDefault" class="isDefault" value="1" /></label>';
	    			}}

	    		}},
	    		{ title:'${message("操作")}', align:'center', width:60, renderer:function(val,item){
	    			countAddress++;
	    			return '<a href="javascript:;" class="btn-delete" onclick="deleteAddress(this)">删除</a>';
	    			
	    		}}
	    ];
		var items  = ${sbu_json};
		$mmGrid = $('#table-sbu').mmGrid({
			height:'auto',
		    cols: cols,
		    items:items,
	        checkCol: false,
	        autoLoad: true,
		    fullWidthRows: true
		 });
	   

var $parts_li = $(".parts-li");
$("input[name='isParts']").click(function(){
	var $this = $(this);
	if($this.prop("checked")){
		$parts_li.show();
	}else{
		$parts_li.hide();
	}
});
$addProductParts.live("click", function() {
	var productCategoryId = $("#previousProductCategoryId2").val();
	if (productCategoryId==undefined || productCategoryId.length == 0) {
		$.message_alert("请选择产品分类");
		return false;
	}
  $(this).bindQueryBtn({
		type:'partsCategory',
		bindClick:false,
		title:'${message("选配项")}',
		url:'/product/partsCategory/selectPartsCategory.jhtml?multi=2&productCategoryId='+productCategoryId,
		callback:function(rows){
			if(rows.length>0){
				for (var i = 0; i < rows.length;i++) {
					var $part_group_id = $("input.part_group_id[value='"+rows[i].parts_group_id+"']");
					if($part_group_id.length>0){
						var part_id_i = 0;
						$part_group_id.each(function(index) {
								var $part_id = $(this).closest("tr").find("input.part_id[value='"+rows[i].parts_id+"']");
								if ($part_id.length>0) {
									$.message_alert('选配项【配置项['+rows[i].parts_group_name+'],可选项['+rows[i].parts_name+']】已添加');
									part_id_i++;
									return false;
								}
						});
						if (part_id_i > 0) {
							return false;
						}
					}
				
					[@compress single_line = true]
					var html = '<tr class="productPartsTr">
						 	<td>
						 		&nbsp;
							</td>
							<td>
								<input type="hidden" class="part_group_id" name="productParts['+productPartsIndex+'].partsGroup.id" value="'+rows[i].parts_group_id+'">
								<input type="hidden" name="productParts['+productPartsIndex+'].partsGroup.name" value="'+rows[i].parts_group_name+'">
								'+rows[i].parts_group_name+'
							</td>
							<td>';
							[/@compress]
							var fs;
					if(rows[i].parts_id !=null) {
					[@compress single_line = true]
						html += '<input type="hidden" class="part_id" name="productParts['+productPartsIndex+'].parts.id" value="'+rows[i].parts_id+'">
									<input type="hidden" name="productParts['+productPartsIndex+'].parts.name" value="'+rows[i].parts_name+'">
									<input type="hidden"  name="productParts['+productPartsIndex+'].name" value="'+rows[i].parts_name+'">
									'+rows[i].parts_name;
									[/@compress]
					} else {
						html += '<input type="text" class="text" name="productParts['+productPartsIndex+'].name" value="'+rows[i].parts_name+'">';
					}
					var image = rows[i].image;
					if(image==null)image='/resources/images/default-img.jpg';
					[@compress single_line = true]
					html += '</td>
							<td>
								<input type="hidden" name="upload_image'+productPartsIndex+'" value="'+image+'"/>
							</td>
							<td>
								<label onClick="chooseOne(this)">
									<input type="checkbox" name="productParts['+productPartsIndex+'].isStandard" class="js_partsGroupId_'+rows[i].parts_group_id+'" />${message("是否标配")}
									<input type="hidden" name="_productParts['+productPartsIndex+'].isStandard" value="false" />
								</label>
							</td>
							<td>
								<span class="search" style="position:relative">
									<input type="hidden"  name="productParts['+productPartsIndex+'].super_parts.id" class="text superPartsId superPart"/>
									<input type="text"  class="text superPartsName superPart" maxlength="200"  onkeyup="clearSelect(this)"/>
									<input type="button" class="iconSearch superPart" value="" id="selectSuperParts" onclick="openSuperPartsWin(this)">
								</span>
							</td>
							<td style="text-align:center;border-bottom: solid 1px #f7f7f7;">
								<a href="javascript:;" class="deleteParts">[${message("1002")}]</a>
							</td>
					</tr>';
					[/@compress]
					if($part_group_id.length>0){
						$part_group_id.each(function(index) {
							if(index == $part_group_id.length - 1) {
								$(this).closest("tr").after(html);
							}
						});
					}
					else {
						$productPartsTable.append(html);
					}
					productPartsIndex++;

					//可选项图片js
					var iName="upload_image"+(productPartsIndex-1);
					$("input[name='"+iName+"']").single_upload({
						uploadSize:"medium",
						hideDel:true
					});

				}
			}
		
		}
	});	
});
	//已有可选项图片
	[#list product.productParts as pParts]
	    $("input[name='productParts[${pParts_index}].image']").single_upload({
			uploadSize:"medium",
			hideDel:true
		});
	[/#list]

	// 删除选配项
	$deleteParts.live("click", function() {
		var $this = $(this);
		$this.closest("tr").remove();
	});
	
	//列表展示图(342*342)
    $("input[name='image']").single_upload({
		uploadSize:"medium"
	});
    
  //计算每箱支数
	$("#branchPerBox").blur(function(){
		calcutePerBranch();
	});
	$("#perBranch").blur(function(){
		calcutePerBranch();
	});
	
	
	//上传产品图片
var $addProductImage = $("#addProductImage");
var productImgIdnex = ${product.productImages?size};
var option1 = {
		dataType: "json",
    	uploadToFileServer:true,
	    uploadSize: "thumbnailMediumLarge",
        callback : function(data){
        	for(var i=0;i<data.length;i++){
	        	var file_info = data[i].file_info;
	        	[@compress single_line = true]                    	
	            var html = '<div class="ul-box" style="padding:15px 0px 0px">
	                  <div class="pic"  style="margin-left:45px;"><a href="'+file_info.medium+'" target="_blank"><img src="'+file_info.medium+'"></a></div>
	                  <p style="margin-top:20px">${message("排序")} <input type="text" name="productImages['+productImgIdnex+'].order" class="txt sort"></p>
	                  <a class="del delProductImage1" href="javascript:void(0);"></a>
	                  <input type="hidden" name="productImages['+productImgIdnex+'].title" value="'+file_info.name+'">
	                  <input type="hidden" name="productImages['+productImgIdnex+'].source" value="'+file_info.source+'">
		              <input type="hidden" name="productImages['+productImgIdnex+'].large" value="'+file_info.large+'">
		              <input type="hidden" name="productImages['+productImgIdnex+'].medium" value="'+file_info.medium+'">
		              <input type="hidden" name="productImages['+productImgIdnex+'].thumbnail" value="'+file_info.thumbnail+'">
	              </div>';
				[/@compress]                                 
	            $addProductImage.before(html);
	            productImgIdnex++;
	        }
        }
    }
    $addProductImage.file_upload(option1);
    
	//删除产品图片
    var $delProductImage1 = $(".delProductImage1");
	$delProductImage1.live("click", function() {
		var $this = $(this);
		$this.closest("div.ul-box").remove();
	});
	
	// 修改产品分类
	$productCategoryId.change(function() {
		var hasValue = false;
		$parameterTable.add($attributeTable).find(":input").each(function() {
			if ($.trim($(this).val()) != "") {
				hasValue = true;
				return false;
			}
		});
		if (hasValue) {
			$.dialog({
				type: "warn",
				content: "${message("productCategoryChangeConfirm")}",
				width: 450,
				onOk: function() {
					loadParameter();
					//loadAttribute();
					previousProductCategoryId = $productCategoryId.val();
				},
				onCancel: function() {
					$productCategoryId.val(previousProductCategoryId);
				}
			});
		} else {
			loadParameter();
			//loadAttribute();
			previousProductCategoryId = $productCategoryId.val();
		}
	});
	
//设置
	function chooseOne(domEl){
		var $this = $(domEl).find("input[type='checkbox']");
		var $tr = $this.closest("table");
			$tr.find("input.isDefaultCheckbox").prop("checked",false);
			$this.prop("checked",true);
			$tr.find("input.isDefault").val("0");
			$(domEl).find("input.isDefault").val("1");
	}

	


	
	
function loadParameter() {
		ajaxSubmit('',{
			url: "/product/product/parameter_groups.jhtml",
			method: "GET",
			data: {id: $("#previousProductCategoryId2").val()},
			callback:function(resultMsg){
				var data = $.parseJSON(resultMsg.content);
				var trHtml = "";
				var selectIndex = 0;
				$.each(data, function(i, parameterGroup) {
					trHtml += 
						'<tr><td colspan="6" style="font-weight: 900 !important;font-size: 14px;">'+parameterGroup.name+'</td></tr>';
					var paramSize = parameterGroup.parameters.length;
					var num = 0;
					$.each(parameterGroup.parameters, function(j, parameter) {
						var sj = j%3;
						if(j==0){//第一个元素
							trHtml += '<tr>';
						}else if(sj==0){//整除，有三个元素
							trHtml += '</tr><tr>';
						}
						
			            trHtml +='<th>'+parameter.name+'：</th>';
                        if (parameter.searchType==1) {
                        	trHtml +='<td><select name="productParamValues['+selectIndex+'].parameterValue.id" class="text"><option value="">请选择</option>';
	                            $.each(parameter.parameterValues, function(n, parameterValue) {
									trHtml +='<option value="'+parameterValue.id+'">'+parameterValue.value+'</option>';
	                            });
	                            trHtml +='</select></td>';
                        }else if(parameter.searchType==0){
							trHtml +='<td><input type="hidden" name="productParamValues['+selectIndex+'].parameter.id" value="'+parameter.id+'"><input type="text" name="productParamValues['+selectIndex+'].value" class="text" btn-fun="clear"></td>';
                        }else if(parameter.searchType==2){
                        	trHtml += '<td><input type="hidden" name="productParamValues['+selectIndex+'].parameter.id" value="'+parameter.id+'">'+
							'<div class="nums-input ov">'+
				            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">'+
				            	'<input type="text"  class="t"  name="productParamValues['+selectIndex+'].value" value="" minData="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" >'+
				            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">'+
				        	'</div></td>';
							//trHtml +='<td><input type="hidden" name="productParamValues['+selectIndex+'].parameter.id" value="'+parameter.id+'"><input type="number" name="productParamValues['+selectIndex+'].value" class="text" btn-fun="clear"></td>';
                        }
		                if(j+1==paramSize){//最后一个元素
		                	/**补齐列*/
		                	for(var k=0;k<3-(j+1)%3;k++){
		                		trHtml += "<th class='noBg'></th><td></td>";
		                	}
		                	/**行结束*/
							trHtml += '</tr>';
						}
						selectIndex++;
					});
					 
				});
				$("#parameterTable").html(trHtml);
			}
		});
	
	}
	
	// 加载参数
	/* function loadParameter() {
		$.ajax({
			url: "parameter_groups.jhtml",
			type: "GET",
			data: {id: $("#previousProductCategoryId2").val()},
			dataType: "json",
			beforeSend: function() {
				$parameterTable.empty();
			},
			success: function(data) {
				var trHtml = "";
				$.each(data, function(i, parameterGroup) {
					trHtml += '<tr><td style="text-align: right;"><strong>' + parameterGroup.name + ':<\/strong><\/td><td>&nbsp;<\/td><\/tr>';
					$.each(parameterGroup.parameters, function(i, parameter) {
						[@compress single_line = true]
							trHtml += 
							'<tr>
								<th>' + parameter.name + ': <\/th>
								<td>
									<input type="text" name="parameter_' + parameter.id + '" class="text " btn-fun="clear" maxlength="200" \/>
								<\/td>
							<\/tr>';
						[/@compress]
					});
				});
				$parameterTable.append(trHtml);
			}
		});
	} */
	
	$.validator.addClassRules({
		productImageFile: {
			required: true,
			extension: "${setting.uploadImageExtension}"
		},
		productImageOrder: {
			digits: true
		}
	});
	
	// 表单验证
	$inputForm.validate({
		rules: {
			productCategoryId: "required",
			name: "required",
			price: {
				required: true,
				[#if store == null]
				min: [#if product.pushPrice??]0[#else]0[/#if],
				max: [#if product.pushMaxPrice??]999999999999[#else]999999999999[/#if],
				[#else]
				min: [#if product.pushPrice??]${product.pushPrice}[#else]0[/#if],
				max: [#if product.pushMaxPrice??]${product.pushMaxPrice}[#else]999999999999[/#if],
				[/#if]
				decimal: {
					integer: 18,
					fraction: ${setting.priceScale}
				}
			},
			cost: {
				min: 0,
				decimal: {
					integer: 18,
					fraction: ${setting.priceScale}
				}
			},
			fixedProfit: {
				min: 0,
				decimal: {
					integer: 18,
					fraction: ${setting.priceScale}
				}
			},
			marketPrice: {
				min: 0,
				decimal: {
					integer: 18,
					fraction: ${setting.priceScale}
				}
			},
			stock: "digits",
			productCommission: {min: 0, max: 100, number:true, digits: true},
			partnerSingle: {min: 0, max: 100, number:true, digits: true},
			point: "digits"
		},
		submitHandler: function(form) {
			return false;
		}
	});
	
	$inputForm.bindAttribute({
		isConfirm:true,
		dataType:"json",
		extraParam:function(){
			$("textarea").blur();
			var value = $("textArea[name='introduction']").val();
			var  data = {introduction_base4 : window.encodeURI(value)};
			 return data;
		},
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			});
	    }
	});

	//查询分类
	$("#selectProductCategory").bindQueryBtn({
		type:'productCategory',
		title:'${message("查询分类")}',
		url:'/product/product_category/select_productCategory.jhtml?isEnabled=1',
		callback:function(rows){
			if(rows.length>0){
				var row = rows[0];
				 $("input[name='productCategoryId']").attr("value",row.id);
	                $("input[name='productCategoryName']").attr("value",row.name);
				$("#productPartsBody").html("");
				loadParameter();
			}
		}
	})
	
    $(".deleteAttachment").live("click", function() {
		var $this = $(this);
		$this.closest("li").remove();
	});
    
    
    // ------------------------------------------------------添加附件-----------------------------------
 // 添加附件
	$addAttachment.live("click", function() {
		
		var $this = $(this);
		$div = $this.closest("div");
		var id = $div.find("input[class='att']").val();
		var attachCategoryIdHtml = "";
		var attachIdnex = 0;
		var option1 = {
			dataType: "json",
			addClick: 0,
		    uploadToFileServer:true,
// 		    uploadSize: "fileurl",
			uploadSize: "thumbnail",
			onlyImage:false,
	        callback : function(data){
	        	var date = new Date();
				var year = date.getFullYear();
				var month = date.getMonth()+1;
				var day = date.getDate();
				var time = year+'-'+month+'-'+day;
				[#assign src = "/resources/images/"]
				var src='';
		        for(var i=0;i<data.length;i++){
					var row = data[i].file_info;
					
					if (id != undefined) {
						attachCategoryIdHtml = '<input type="hidden" name="productAttachs['+attachNum+'].productAttachCategory.id" value="'+id+'" onChange="" \/>';
					}
					
		    		var url = row.url;
		    		var thumbnail = row.thumbnail;
					var fileObj = getfileObj(row.name);
					var suf=fileObj.suffix.toLowerCase();
	        		if(suf=='jpg'||suf=='png'||suf=='gif'||suf=='jpeg'||suf=='bmp'){
	        			src=thumbnail;
	        		}else if(suf=='mp3'){
	        			src='${src}audio.png';
	        		}else if(suf=='xls'||suf=='xlsx'){
	        			src='${src}excel.png';
	        		}else if(suf=='exe'){
	        			src='${src}exe.png';
	        		}else if(suf=='fla'){
	        			src='${src}flash.png';
	        		}else if(suf=='html'){
	        			src='${src}html.png';
	        		}else if(suf=='mp4'){
	        			src='${src}mp4.png';
	        		}else if(suf=='pdf'){
	        			src='${src}pdf.png';
	        		}else if(suf=='ppt'){
	        			src='${src}ppt.png';
	        		}else if(suf=='txt'){
	        			src='${src}txt.png';
	        		}else if(suf=='doc'||suf=='docx'){
	        			src='${src}word.png';
	        		}else if(suf=='xml'){
	        			src='${src}xml.png';
	        		}else if(suf=='zip'){
	        			src='${src}zip.png';
	        		}else if(suf=='avi'||suf=='wmv'||suf=='rmvb'||suf=='3gp'){
	        			src='${src}video.png';
	        		}else{
	        			src='${src}currency.png';
	        		}
					var str_2='<li style="margin:5px 10px 0 0"><div class="pic" onclick="addAttachDownloadTimes(&quot;'+url+'&quot;,&quot;'+fileObj.suffix+'&quot;,&quot;'+row.name+'&quot;)"><img src="'+src+'" /></div><input type="text" class="txt" style="width:69%" name="productAttachs['+attachNum+'].name" value="'+fileObj.name+'" placeholder="请输入附件名称"/>.'+fileObj.suffix+
                        '<textarea name="productAttachs['+attachNum+'].memo" class="txt" placeholder="请输入备注信息"></textarea><i class="deleteAttachment del"></i>'+
						'<input name="productAttachs['+attachNum+'].url" value="'+url+'" type="hidden"><input name="productAttachs['+attachNum+'].thumbnail" value="'+thumbnail+'" type="hidden">'+
						'<input name="productAttachs['+attachNum+'].suffix" value="'+fileObj.suffix+'" type="hidden">'+attachCategoryIdHtml+'</li>';
					
					$(".a_"+id).find("ul.pro-c-list").append(str_2);
					attachNum++;
					attachCategoryIdHtml='';
		        }
				
	        }
	    }
	    $this.file_upload(option1);
		
	});


    //-------------------------------------------------------end--------------------------------------
	
});

//sbu列表删除
function deleteSbu(e){
	var index = $(e).closest("tr").index();
	$mmGrid.removeRow(index);
}

//sbu列表设置
function sz(domEl){
	
		var $this = $(domEl).find("input[type='checkbox']");
		var $tr = $this.closest("table");
			$tr.find("input.isDefaultCheckbox").prop("checked",false);
			$this.prop("checked",true);
			$tr.find("input.isDefault").val("0");
			$(domEl).find("input.isDefault").val("1");
	
}


function t(){
//获取事件 注意：在mozilla下 出发事件的函数不能带参数
var evt = getEvent();
var rest = false;
if(evt){
	rest = true;
	//获取事件源的对象
	var element = evt.srcElement || evt.target;
	//获取事件源的对象（这里的事件源对象是 文本框）
	evt = evt.keyCode || evt.charCode;
	//获取 文本框的值
	var text = element.value;
	if(evt!=8 && evt!=13){
		//按下是否是 “.” 并只允许按下一个
		if(evt == 46){
			if(text || text != ''){
				if(!(/^\d+$/g.test(text)))
					rest = false;
			}else{
				rest =false;
			}
		//按下是否是数字键 退格键 回车键
		}else if(evt < 48 || evt > 57){
			rest = false;
		}
	}
}
return rest;
}

/**
* @ 获取事件（鼠标、键盘）在火狐下触发函数不能带参数
* @ 兼容 IE Mozilla Google Maxthon Opera
* @ return key/null
*/
function getEvent(){
 if(document.all){
      return window.event;//IE
 }else{//FF
  func = getEvent.caller;
   while(func != null){
		var arg0 = func.arguments[0];
		if(arg0)
			return arg0;
		func = func.caller;
	}
    return null;
 }
}

function deleteAddress(e){
	var index = $(e).closest("tr").index();
	$.message_confirm('您确定要删除吗？',function(){
		$mmGrid.removeRow(index);
	})
}

//同一选配项只能有一个标配
function chooseOne(domEl){
	var $this = $(domEl).find("input[type='checkbox']");
	var $tr = $this.closest("tr");
	if($this.attr('checked')){
		$this.prop("checked",false);
		$tr.find("input.superPart").prop("disabled",false);
	}
	else{
		var sameClass = $this.attr("class");
		var $input = $("input."+sameClass);
		$input.prop("checked",false);
		$input.closest("tr").find("input.superPart").prop("disabled",false);
		
		$this.prop("checked",true);
		$tr.find("input.superPart").prop("disabled",true);
	}
}

function deleteProduct(e){	
	$.message_confirm('您确定要删除吗？',function(){
		ajaxSubmit($(e),{
			method:'post',
			url:'/product/product/delete.jhtml?ids=${product.id}',
			callback: function(data) {
				parent.reflush_list();
				location.href="/product/product/add/${code}.jhtml";
			}
		})
	})
} 	

function openSuperPartsWin(e){
	$(e).bindQueryBtn({
		type:'superParts',
		bindClick:false,
		title:'${message("查询引用选配项")}',
		url:'/product/partsCategory/selectPartsCategory.jhtml?productCategoryId=${product.productCategory.id}',
		callback:function(rows){
			if(rows.length>0){
				var row = rows[0];
				var $tr = $(e).closest('tr');
				var part_id = $tr.find('.part_id').val();
				var part_group_id = $tr.find('.part_group_id').val();
				
				if(row.parts_group_id == part_group_id){
					$.message_alert('${message("请选择其他配置项的可选项")}');
					return false;
				}
				if(row.parts_id==null){
					$.message_alert('${message("请选择可选项不为空的数据")}');
					return false;
				}else if(row.parts_id==part_id){
					$.message_alert('${message("引用可选项不能与选配项一样")}');
					return false;
				}
				$tr.find('.superPartsId').val(row.parts_id);
				$tr.find('.superPartsName').val(row.parts_name);
			}
		}
	});
}

function addAttachDownloadTimes(url,suffix,name){
	var suffix_str=suffix.toLowerCase();
	if(suffix_str!="png" && suffix_str!="jpg" && suffix_str!="jpeg"
			&& suffix_str!="gif" && suffix_str!="bmp"){
		downloadFile(url,name);
	}else{
		window.open(url);
	}
}
</script>

<!--                         左右拖拉   需特殊方法                                                           -->
<script type="text/javascript">
function treeScoll(options){
		var clickX, leftOffset, inx, nextW2, nextW;
		var dragging  = false;
		var doc 	  = document;
		var labBtn 	  = $("#labBtn");
		var wrapWidth = $("body").width();
		
		var begin_height = 0;
		var end_height = 0;
		var height__spec = 0;
	
		labBtn.bind('mousedown',function(){
				dragging   = true;
				leftOffset = 0;
				inx 	   = $(this).index('label');
				begin_height = $("#search-content").height();
				$("body").addClass("resize-ing");
			}
		);
		
		var settings = {
			minClickX:130,
			maxClickX:700,
		}
		
		$.extend(settings, options);
	
		doc.onmousemove = function(e){
			if (dragging) {
				clickX = e.pageX;
				//第一个拖动按钮左边不出界
				if(clickX > leftOffset) {
					if(clickX<settings.minClickX || clickX>settings.maxClickX){
						return false;
					}
					
					var prev = labBtn.prev();
					var left = prev.offset().left;
					var next = labBtn.next();
					
					var w = prev.width();
					var prev_w = clickX-left;
					var spec = prev_w-w;
					
					labBtn.prev().width( prev_w + 'px');
					labBtn.css('left', prev.width()+5 + 'px');//按钮移动
					next.css('left',  prev.width()+15 + 'px');
					
// 					labBtn.prev().width( prev_w + 'px');
// 					labBtn.css('left', next.offset().left-20 + 'px');//按钮移动
// 					next.width(next.width()-spec+'px');
					
					end_height = $("#search-content").height();
				    height__spec = begin_height-end_height;
				    begin_height = end_height;
				    if(height__spec!=0){
				    	changeGridHeight(height__spec);
				    }
				}
			}
		};
	
		$(doc).mouseup(function(e) {
		    dragging = false;
		    e.cancelBubble = true;
		    $("body").removeClass("resize-ing");
		})
		
		function changeGridHeight(h_spec){
			var $bodyWrapper = $(".mmg-bodyWrapper");
			var $responsive =  $(".table-responsive");
			var $mmGrid = $(".mmGrid");
			
			$bodyWrapper.height($bodyWrapper.height()+h_spec);
			$responsive.height($responsive.height()+h_spec);
			$mmGrid.height($mmGrid.height()+h_spec);
			
		
		}
	
	}
	
function calcutePerBranch(){
	var branchPerBox = $("#branchPerBox").val();
	var perBranch = $("#perBranch").val();
	if (branchPerBox =='' ||  perBranch == ''|| isNaN(branchPerBox) || isNaN(perBranch)) {
		$("#perBox").val(0); 
	}
	$("#perBox").val(accMul(Number(branchPerBox),Number(perBranch))); 
	$("#perBoxStr").text(accMul(Number(branchPerBox),Number(perBranch)));
}

function subModel(t){
		var $this = $(t);
		var value = $this.val();
		var model = '';
		model = value.substring(0,value.indexOf("-"));

		$("input[name='model']").val(model);
	}
</script>
</head>
<body class="tree-contain">
	<div class="pathh">
		&nbsp;${message("查看产品")}
	</div>
	<form id="inputForm" action="/product/product/update.jhtml" method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="id" value="${product.id}" />
		<input type="hidden" name="hits" value="${product.hits}" />
		<input type="hidden" name="type" value="${product.type}"/>
		<input type="hidden"  name="isUsePartnerSingle" id="isUsePartnerSingle"
		 value="[#if product.isUsePartnerSingle == null]0[#else]${product.isUsePartnerSingle}[/#if]" />
		<ul id="tab" class="tab">
			<li>
				<input type="button" value="${message("12081")}" />
			</li>
			<li>
				<input type="button" value="${message("12103")}" />
			</li>
			
			<li>
				<input type="button" value="${message("12074")}" />
			</li>
			<li class="parts-li" [#if !product.isParts]style="display:none;"[/#if]>
				<input type="button" value="${message("产品选配项")}" />
			</li>
			<li >
				<input type="button" value="${message("产品资料")}" />
			</li>
		</ul>
		<div class="tabContent">
			<div class="clearfix mt25">
				<div class="box-style fl" style="width: 60%">
					<b class="box-tit">${message("12081")}</b>
					<div class="box-main">
					<span class="red">*修改产品分类，将清空产品参数、产品资料数据</span>
					   	<table class="input input-edit" style="margin-top:5px;">
					   		<tr>
								<th>
									${message("12061")}:
								</th>
								<td>
									<span class="search" style="position:relative">
										<input type="hidden" class="text" name="productCategoryId" id="previousProductCategoryId2" value="${product.productCategory.id}" />
										<input type="text" class="text" name="productCategoryName" value="${product.productCategory.name}" />
										<input type="button" class="iconSearch" value="" id="selectProductCategory">
									</span>
								</td>
								<th>
									${message("产品编码")}:
								</th>
								<td>
									<input type="text" name="vonderCode" class="text " btn-fun="clear" value="${product.vonderCode}" maxlength="200" />
								</td>
							</tr>
							<tr>
								<th>
									${message("产品品牌")}:
								</th>
								<td>
									[#--<input type="text" name="brandName" class="text " btn-fun="clear" maxlength="200" value="${product.brandName}" />
									--]
									<select name="brandId" class="text">
						          [#list brands as brand]
							            <option value="${brand.id}" [#if product.brand.id = brand.id]selected="selected"[/#if]>${brand.value}</option>
						          [/#list]
					                </select>
								</td>
								<th>
									${message("产品型号")}:
								</th>
								<td>
									<input type="text" name="model" class="text " btn-fun="clear" maxlength="200" value="${product.model}"/>
							    </td>	
							</tr>
							<tr>
								<th>
									${message("产品描述")}:
								</th>
								<td colspan=3>
									<input type="text" name="description" class="text " btn-fun="clear" maxlength="200" value="${product.description}" />
								</td>
							</tr>
							<tr>
								<th>
									<span class="requiredField">*</span>${message("产品名称")}:
								</th>
								<td>
									<input type="text" class="text" name="name" value="${product.name}" />
								</td>
								<th>
									${message("12093")}:
								</th>
								<td>
									<div class="nums-input ov">
										<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
										<input type="text"  class="t"  name="weight" value="${product.weight}" minData="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" >
										<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
									</div>	
								</td>
							</tr>
							<tr>
							    <th>
						        ${message("计量单位")}:
								</th>
								<td>
									<input type="text" class="text" name="unit" value="${product.unit}" btn-fun="clear" />
								</td>	
								<th>
									${message("每箱支数")}:
								</th>
								<td>
									<div class="nums-input ov">
										<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event,2)" onMouseUp="editQty(this.nextSibling,event,2)">
										<input type="text"  class="t branchPerBox"  name="branchPerBox" id="branchPerBox" value="${product.branchPerBox}" minData="0" oninput="editQty (this,event,2)" onpropertychange="editQty(this,event,2)" >
										<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event,2)" onMouseUp="editQty(this.previousSibling,event,2)">
									</div>
							    </td>
							   <!--  <th>
									${message("每箱单位数")}:
								</th>
								<td>
									<div class="nums-input ov">
										<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event,9)" onMouseUp="editQty(this.nextSibling,event,9)">
										<input type="text"  class="t"  id="perBox" name="perBox" value="${product.perBox}" minData="" oninput="editQty (this,event,9)" onpropertychange="editQty(this,event,9)" >
										<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event,9)" onMouseUp="editQty(this.previousSibling,event,9)">
									</div>	
								</td> -->
							</tr>
							<tr>
							<th>
			                     ${message("生产工厂")}:
								</th>
								<td>
								<input type="text" name="manufactoryName" class="text " btn-fun="clear" maxlength="200" value="${product.manufactoryName}" />
								</td>
							<th>
								${message("每支单位数")}:
								</th>
								<td>
									<div class="nums-input ov">
										<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event,9)"  onMouseUp="editQty(this.nextSibling,event,9)">
										<input type="text"  class="t" id="perBranch" name="perBranch" value="${product.perBranch}" minData="0" oninput="editQty (this,event,9)" onpropertychange="editQty(this,event,9)" >
										<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event,9)" onMouseUp="editQty(this.previousSibling,event,9)">
									</div>								
								</td>
							</tr>
							<tr>
								 <th>
									${message("提前天数")}:
								</th>
								<td>
									<div class="nums-input ov">
										<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event,0)">
										<input type="text"  class="t"  name="prevDay" value="${product.prevDay}" minData="" oninput="editQty (this,event,0)" onpropertychange="editQty(this,event,0)" >
										<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event,0)">
									</div>								
								</td>
								<th>
									${message("每箱单位数")}:
								</th>
							     <td>
							     <!--    <span id="perBoxStr">${product.perBox}</span>
									<input type="hidden" name="perBox" id="perBox" class="text  perBox" btn-fun="clear" maxlength="200" value="${product.perBox}" readonly="readonly" />  -->
								  <div class="nums-input ov">
											<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event,9)"  onMouseUp="aaa(this.nextSibling,event,9)">
											<input type="text"  class="t perBox" id="perBox" name="perBox" value="${product.perBox}" minData="0" oninput="aaa (this,event,9)" onpropertychange="aaa(this,event,9)" >
											<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event,9)" onMouseUp="aaa(this.previousSibling,event,9)">
									</div>
								</td>
							</tr>
							<tr>
								<th>
									${message("税控编号")}:
								</th>
								<td>
									<input type="text" name="fiscalCoding" class="text " btn-fun="clear" maxlength="200" value="${product.fiscalCoding}" />
								</td>
								<th>
									${message("1012")}:
								</th>
								<td>
									<div class="nums-input ov">
										<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event,0)">
										<input type="text"  class="t"  name="order" value="${product.order}" minData="0" oninput="editQty (this,event,0)" onpropertychange="editQty(this,event,0)" >
										<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event,0)">
									</div>
								</td>					
							</tr>
							<tr>
								<th>
									${message("体积")}:
								</th>
								<td>
									<input type="text" name="volume" class="text " btn-fun="clear" value="${product.volume}" maxlength="200" />
								</td>
								<th>
							   		${message("生产工艺")}:
							   </th>
							   <td>
							   		<input type="text" name="artwork" class="text " btn-fun="clear" maxlength="200" value="${product.artwork}" />
							   </td>
							</tr>
							<tr>
							    
								<th>
									${message("产品规格")}:
								</th>
								<td>
									<input type="text" name="spec" class="text " btn-fun="clear" maxlength="200" value="${product.spec}" />
								</td>
							   	<th>${message("产品级别")}:</th>
								<td>
									<select id="productGrade" name="productGrade" class="text" >
										<option value="1"[#if product.productGrade == 1]selected="selected"[/#if]>${message("优等品")}</option>
										<option value="2"[#if product.productGrade == 2]selected="selected"[/#if]>${message("二等品")}</option>
										<option value="3"[#if product.productGrade == 3]selected="selected"[/#if]>${message("一等品")}</option>
										<option value="4"[#if product.productGrade == 4]selected="selected"[/#if]>${message("无等级")}</option>
									</select>
								</td>
							</tr>
							 <tr>
								<th>
								${message("木种/花色")}:
								</th>
								<td>
									<input type="text" name="woodTypeOrColor" class="text " btn-fun="clear" value="${product.woodTypeOrColor}" maxlength="200" />
								</td>
								<th>
									<span class="requiredField">*</span>${message("标准零售价")}:
								</th>
								<td>
									<div class="nums-input ov">
										<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
										<input type="text"  class="t"  name="price" value="${product.price}" minData="0" oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >
										<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
									</div>									
								</td>
							
							</tr> 
							<tr>
							<th>
									${message("产品底价")}:
								</th>
								<td>
								
									<div class="nums-input ov">
										<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
										<input type="text"  class="t"  name="cost" value="${product.cost}" minData="0" oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >
										<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
									</div>	
								</td>
								<th>
									${message("成本价")}:
								</th>
								<td>
								
									<div class="nums-input ov">
										<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
										<input type="text"  class="t"  name="marketPrice" value="${product.marketPrice}" minData="0" oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" >
										<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
									</div>	
									
								</td>
							</tr>
							<tr>
							<th>${message("产品大类")}:</th>
							<td>
								<input type="text" name="productOne" class="text " btn-fun="clear" maxlength="200" value="${product.productOne}" />
							</td>
							<th>${message("产品中类")}:</th>
							<td>
								<input type="text" name="productTwo" class="text " btn-fun="clear" maxlength="200" value="${product.productTwo}" />
							</td>
							</tr>
							<tr>
							<th>${message("产品小类")}:</th>
							<td>
								<input type="text" name="productThree" class="text " btn-fun="clear" maxlength="200" value="${product.productThree}" />
							</td>
							<th>${message("辅单位")}:</th>
							<td>
								<input type="text" name="specTwo" class="text " btn-fun="clear" maxlength="200" value="${product.specTwo}" />
							</td>
							</tr>
							<tr>
							<th>${message("长")}:</th>
							<td>
								<input type="text" name="erp_length" class="text " btn-fun="clear" maxlength="200" value="${product.erp_length}" />
							</td>
							<th>${message("宽")}:</th>
							<td>
								<input type="text" name="erp_width" class="text " btn-fun="clear" maxlength="200" value="${product.erp_width}" />
							</td>
							</tr>
							<tr>
							<th>${message("高")}:</th>
							<td>
								<input type="text" name="erp_height" class="text " btn-fun="clear" maxlength="200" value="${product.erp_height}" />
							</td>
							</tr>
							
							<tr>
                                <th>
									${message("描述")}:
								</th>
								<td colspan=3>
									<input type="text" name="detailDescription" class="text " btn-fun="clear" maxlength="200" oninput="subModel(this)" onpropertychange="subModel(this)" value="${product.detailDescription}" />
								</td>
							</tr>
							<tr>
								<th>
									${message("12013")}:
								</th>
								<td colspan=3>
									<input type="text" name="memo" class="text " btn-fun="clear" value="${product.memo}" maxlength="200" />
								</td>
							</tr>
							<tr>
							    <th>
									${message("纸张转换率(㎡/张)")}:
								</th>
								<td>
									<div class="nums-input ov">
										<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event,6)">
										<input type="text"  class="t"  name="paperNum" value="${product.paperNum}" minData="0" oninput="editQty (this,event,6)" onpropertychange="editQty(this,event,6)" >
										<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event,6)">
									</div>								
								</td>
								<th>
									${message("12083")}:
								</th>
								<td>
									<input type="text" name="seoTitle" class="text " btn-fun="clear" value="${product.seoTitle}" maxlength="200" />
								</td>
							</tr>
							<tr>
								<th>
									<p style="line-height:normal">${message("12090")}</p>(342×342):
								</th>
								<td colspan="3">
								<input type="hidden" name="image" id="image" value="${product.image}">
								</td>
							</tr>
							<tr>
								<th>
									${message("12096")}:
								</th>
								<td  colspan="3">
									<label>
										<input type="checkbox" name="isParts" value="true" [#if product.isParts]checked="checked"[/#if]/>${message("是否定制")}
										<input type="hidden" name="_isParts" value="false" />
									</label>
									&nbsp;&nbsp;&nbsp;&nbsp;
									<label>
										<input type="checkbox" name="isMarketable" value="true"[#if product.isMarketable] checked="checked"[/#if] />${message("是否在售")}
										<input type="hidden" name="_isMarketable" value="false" />
									</label>
				                   [#--&nbsp;&nbsp;&nbsp;&nbsp;
									<label>
										<input type="checkbox" name="isList" value="true"[#if product.isList] checked="checked"[/#if] />${message("是否列出")}
										<input type="hidden" name="_isList" value="false" />
									</label>--] 
									&nbsp;&nbsp;&nbsp;&nbsp;
									<label>
										<input type="checkbox" name="isTop" value="true"[#if product.isTop] checked="checked"[/#if] />${message("是否置顶")}
										<input type="hidden" name="_isTop" value="false" />
									</label>
									&nbsp;&nbsp;&nbsp;&nbsp;
									<label>
										<input type="checkbox" name="isGift" value="true"[#if product.isGift] checked="checked"[/#if] />${message("是否赠品")}
										<input type="hidden" name="_isGift" value="false" />
									</label>
									   &nbsp;&nbsp;&nbsp;&nbsp;
									<label>
										<input type="checkbox" name="isViewOnly" value="true"  [#if product.isViewOnly] checked="checked"[/#if]/>${message("是否仅查看")}
										<input type="hidden" name="_isViewOnly" value="false" />
									</label>
									   &nbsp;&nbsp;&nbsp;&nbsp;
									<!-- <label>
										<input type="checkbox" name="isSale" value="true" [#if product.isSale] checked="checked"[/#if] />${message("是否在售")}
										<input type="hidden" name="_isSale" value="false" />
									</label>
									   &nbsp;&nbsp;&nbsp;&nbsp; -->
									<label>
										<input type="checkbox" name="isFilter" value="true" [#if product.isFilter] checked="checked"[/#if]/>${message("是否过滤")}
										<input type="hidden" name="_isFilter" value="false" />
									</label>
									   &nbsp;&nbsp;&nbsp;&nbsp;
									<label>
										<input type="checkbox" name="isEB" value="true" [#if product.isEB] checked="checked"[/#if]/>${message("电商专供")}
										<input type="hidden" name="_isEB" value="false" />
									</label>
								</td>
							</tr>
						</table>
						<div class="title-style">
			        	sbu:
			        	<div class="btns">
			        		<a href="javascript:void(0);" id="addSbu" class="button">选择SBU</a>
			        	</div>
			        	</div>
			        	<table id="table-sbu"></table>
					</div>
				</div>
				<div class="box-style fr" style="width:30%">
					<b class="box-tit">${message("12104")}</b>
					<div class="box-main" style="overflow:visible;">
					  <div class="upload-list tc" style="overflow:initial;">
						  [#list product.productImages as productImage]
                          	<div class="ul-box" style="padding:13px 0px 0px">
                              <div class="pic" style="margin-left:65px; width:80px"><a href="${productImage.source}" target="_blank"><img src="${productImage.thumbnail}"></a></div>
                               <b>&nbsp;</b>
                              <p>${message("排序")} <input type="text" name="productImages[${productImage_index}].order" class="txt sort" value="${productImage.order}"></p>
                              <a class="del delProductImage1" href="#"></a>
                              <input type="hidden" name="productImages[${productImage_index}].title" value="${productImage.title}">
                              <input type="hidden" name="productImages[${productImage_index}].source" value="${productImage.source}">
						      <input type="hidden" name="productImages[${productImage_index}].large" value="${productImage.large}">
						      <input type="hidden" name="productImages[${productImage_index}].medium" value="${productImage.medium}">
						      <input type="hidden" name="productImages[${productImage_index}].thumbnail" value="${productImage.thumbnail}">
						      <input type="hidden" name="productImages[${productImage_index}].newsImage" value="${productImage.newsImage}">
                          </div>
                          [/#list]
                          <a href="javascript:;" class="a-upload" id="addProductImage"></a>
					  </div>
					</div>
				</div>
			</div>
		</div>
		<div class="tabContent">
			<textarea id="editor" name="introduction" class="editor" style="width: 100%;">${product.introduction?html}</textarea>
		</div>
		<div class="tabContent">
		[#assign selectIndex=0]
		<table class="input input-edit" id="parameterTable">
		[#list product.productCategory.parameterGroups as parameterGroup]
                    <tr><td colspan="6"  style="font-weight: 900 !important;font-size: 14px;">${parameterGroup.name}</td></tr>
                    	[#list parameterGroup.parameters?chunk(3) as rows]
                    		[#assign size = rows?size]
                    		<tr>
                    			[#list rows as parameter]
                    				<th>${parameter.name}：</th>
                    				<td>
                    					   [#if parameter.searchType==2]
                    					   		<input type="hidden" name="productParamValues[${selectIndex}].parameter.id" value="${parameter.id}">
												<div class="nums-input ov">
													<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">
													<input type="text"  class="t"  name="productParamValues[${selectIndex}].value" value="${productPValues.get(parameter.id)}" minData="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" >
													<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">
												</div>                    					   		
                    					   [#elseif parameter.searchType==1]
					                            <select name="productParamValues[${selectIndex}].parameterValue.id" class="text">
					                            <option value="">${message("请选择")}</option>
					                            [#list parameter.parameterValues as parameterValue]
					                            	<option value="${parameterValue.id}" [#if parameterValues?seq_contains(parameterValue)]selected[/#if]>${parameterValue.value}</option>
					                            [/#list]
					                            </select>
					                        [#elseif parameter.searchType==0]
				                            		<input type="hidden" name="productParamValues[${selectIndex}].parameter.id" value="${parameter.id}">
				                            		<input type="text" name="productParamValues[${selectIndex}].value" value="${productPValues.get(parameter.id)}" class="text" btn-fun="clear">
				                            [/#if]
                    				</td>
                    				[#assign selectIndex=selectIndex+1]
                    			[/#list]
                    			[#if size<3]
	                    			[#list 1..(3-size) as i]
	                    				<th class="noBg"></th><td></td>
	                    			[/#list]
	                    		[/#if]
                    		</tr>
                    	[/#list]
        [/#list]
         </table>
		</div>
		<div class="tabContent">
			<a href="javascript:;" id="addProductParts" class="button">${message("添加选配项")}</a>
			<div class="clearfix"></div>
			<table id="productPartsTable" class="input mt15" style="width:100%">
					<thead>
						<tr>
							<th>
								&nbsp;
							</th>
							<th>
								${message("配置项")}
							</th>
							<th>
								${message("可选项")}
							</th>
							<th>
								${message("可选项图片")}
							</th>
							<th>
								${message("是否标配")}
							</th>
							<th style="width:120px;">
								引用可选项
							</th>
							<th>
								${message("操作")}
							</th>
						</tr>
					</thead>
					<tbody id="productPartsBody">
					
					[#list product.productParts as pParts]
					<tr class="productPartsTr">
						 
						 	<td>
								<input type="hidden" name="productParts[${pParts_index}].id" value="${pParts.id}">
							</td>
							<td>
								<input type="hidden" class="part_group_id" name="productParts[${pParts_index}].partsGroup.id" value="${pParts.partsGroup.id}">
								<input type="hidden" name="productParts[${pParts_index}].partsGroup.name" value="${pParts.partsGroup.name}">
								${pParts.partsGroup.name}
							</td>
							<td>
								[#if pParts.parts??]
									<input type="hidden" class="part_id" name="productParts[${pParts_index}].parts.id" value="${pParts.parts.id}">
									<input type="hidden" name="productParts[${pParts_index}].parts.name" value="${pParts.parts.name}">
									<input type="hidden"  name="productParts[${pParts_index}].name" value="${pParts.name}">
									${pParts.name}
								[#else]
									<input type="text" class="text" name="productParts[${pParts_index}].name" value="${pParts.name}">
								[/#if]
							</td>
							<td>
								[#--[#if pParts.image??]
									<span class="ico-options">
										<div class="ul-box">
										<div class="pic"><a href="${pParts.image}" target="_blank" style="width: 60px;">
			                        		<img src="${pParts.image}" style="width: 60px; height: 60px;">
			                        	</a></div>
			                        	<a class="delLine delImage" href="#"></a>
			                        	</div>
			                        	<input type="hidden" name="productParts[${pParts_index}].image" value="${pParts.image}"/>
									</span>
									<span class="ico-options" style="display: none;">
										<a href="javascript:;" class="a-upload" id="upload_image${pParts_index}"></a>
									</span>
								[#else]
								<span class="ico-options">
									<a href="javascript:;" class="a-upload" id="upload_image${pParts_index}"></a>
								</span>
								[/#if]--]
								[#if pParts.parts?? && pParts.parts.image!=null]
									[#assign pImage = pParts.parts.image]
								[#else]
									[#assign pImage = "/resources/images/default-img.jpg"]
								[/#if]
								<input type="hidden" name="productParts[${pParts_index}].image" value="${pImage}"/>
							</td>
							<td>
								<label onClick="chooseOne(this)">
									<input type="checkbox" name="productParts[${pParts_index}].isStandard" class="js_partsGroupId_${pParts.partsGroup.id}" value="true" [#if pParts.isStandard]checked="checked"[/#if]/>${message("是否标配")}
									<input type="hidden" name="_productParts[${pParts_index}].isStandard" value="false" />
								</label>
							</td>
							<td>
								<span class="search" style="position:relative">
									<input type="hidden"  name="productParts[${pParts_index}].super_parts.id" value="${pParts.super_parts.id}" class="text superPartsId superPart" [#if pParts.isStandard]disabled[/#if]/>
									<input type="text"  class="text superPartsName superPart" value="${pParts.super_parts.name}" maxlength="200"  onkeyup="clearSelect(this)" [#if pParts.isStandard]disabled[/#if]/>
									<input type="button" class="iconSearch superPart" value="" id="selectSuperParts" onclick="openSuperPartsWin(this)" [#if pParts.isStandard]disabled[/#if]>
								</span>
							</td>
							<td style="text-align:center;border-bottom: solid 1px #f7f7f7;">
								<a href="javascript:;" class="deleteParts">[${message("1002")}]</a>
							</td>
					</tr>
					[/#list]
					
				</tbody>
			</table>
		</div>
		<div class="tabContent attach_content" id="c-productAttach">
		
			<!-- ********************************************************************************************************* -->
			
			<div class="subnav">
	<div class="tit"><s class="attach_img"></s>附件分类</div>
  <div class="pro-tree ztree" id="js-menu">
  	<li id="js-menu_1" class="level0" tabindex="0" hidefocus="true" treenode=""><span id="js-menu_1_switch" title="" class="button level0 switch noline_docu" treenode_switch="" disabled="disabled"></span><a id="js-menu_1_a" class="level0" treenode_a="" onclick="" target="_blank" style="" title="全部产品"><span id="js-menu_1_ico" title="" treenode_ico="" class="button ico_docu" style="width:0px;height:0px;"></span><span id="js-menu_1_span">全部产品</span></a></li>
  </div>
  </div>
<label id="labBtn"></label>
  			<div class="attach_content_n">
  			[#list productAttachCategorys as productAttachCategory]
  			<div class="pro-cont attach_div a_${productAttachCategory.id}" style="display:none">
				<div class="specboxT">
				    <span>当前分类:<span id="category_name">${productAttachCategory.name}</span></span>
				    <a href="javascript:void(0);" class="iconButton addAttachment" style="float:right;margin-right: 0;margin-top: 0.5px;">增加附件</a>
				    <input class="att" value="${productAttachCategory.id}" type="hidden">
				</div>
				<div class="clearfix" ></div>
				<ul class="pro-c-list">
					[#list productAttachs as productAttach]
					[#if productAttach.product_attach_category==productAttachCategory.id]
					[#assign suf = productAttach.suffix?lower_case]
          			[#assign src = "/resources/images/"]
					<li style="margin:5px 10px 0 0">
						<div class="pic" onclick="addAttachDownloadTimes(&quot;${productAttach.url}&quot;,&quot;${productAttach.suffix}&quot;,&quot;${productAttach.file_name}&quot;)"><img  src="[#if suf=='jpg'||suf=='png'||suf=='gif'||suf=='jpeg'||suf=='bmp']${productAttach.thumbnail}[#elseif suf=='mp3']${src}audio.png[#elseif suf=='xls']${src}excel.png[#elseif suf=='exe']${src}exe.png[#elseif suf=='fla']${src}flash.png[#elseif suf=='html']${src}html.png[#elseif suf=='mp4']${src}mp4.png[#elseif suf=='pdf']${src}pdf.png[#elseif suf=='ppt']${src}ppt.png[#elseif suf=='txt']${src}txt.png[#elseif suf=='doc']${src}word.png[#elseif suf=='xml']${src}xml.png[#elseif suf=='zip']${src}zip.png[#elseif suf=='avi'||suf=='wmv'||suf=='rmvb'||suf=='3gp']${src}video.png[#else]${src}currency.png[/#if]" /></div>
          				<input type="text" style="width:69%" class="txt" name="productAttachs[${attachmentIndex}].name" value="${productAttach.name}" placeholder="请输入附件名称"/>.${productAttach.suffix}
						<textarea name="productAttachs[${attachmentIndex}].memo" class="txt" placeholder="请输入备注信息">${productAttach.memo}</textarea><i class="deleteAttachment del"></i>
						<input name="productAttachs[${attachmentIndex}].url" value="${productAttach.url}" type="hidden">
						<input name="productAttachs[${attachmentIndex}].id" value="${productAttach.id}" type="hidden">
						<input name="productAttachs[${attachmentIndex}].storeMember.id" value="${productAttach.store_member}" type="hidden">
						<input name="productAttachs[${attachmentIndex}].thumbnail" value="${productAttach.thumbnail}" type="hidden">
						<input name="productAttachs[${attachmentIndex}].suffix" value="${productAttach.suffix}" type="hidden">
						<input name="productAttachs[${attachmentIndex}].downloadTimes" value="[#if productAttach.download_times??]${productAttach.download_times}[#else]0[/#if]" type="hidden">
						<input type="hidden" name="productAttachs[${attachmentIndex}].productAttachCategory.id" value="${productAttachCategory.id}"/>
					</li>
					[#assign attachmentIndex = attachmentIndex+1]
					[/#if]
					[/#list]
				</ul>
			</div>
  			[/#list]
  			<div class="pro-cont attach_div a_" style="display:none">
				<div class="specboxT">
				    <span>当前分类:<span id="category_name">其他分类</span></span>
				    <a href="javascript:void(0);" class="iconButton addAttachment" style="float:right;margin-right: 0;margin-top: 0.5px;">增加附件</a>
				    <input class="att" value="" type="hidden">
				</div>
				<div class="clearfix"></div>
				<ul class="pro-c-list">
					[#list productAttachs as productAttach]
					[#if productAttach.product_attach_category??]
					[#else]
					[#assign suf = productAttach.suffix?lower_case]
          			[#assign src = "/resources/images/"]
					<li style="margin:5px 10px 0 0">
						<div class="pic" onclick="addAttachDownloadTimes(&quot;${productAttach.url}&quot;,&quot;${productAttach.suffix}&quot;,&quot;${productAttach.file_name}&quot;)"><img  src="[#if suf=='jpg'||suf=='png'||suf=='gif'||suf=='jpeg'||suf=='bmp']${productAttach.thumbnail}[#elseif suf=='mp3']${src}audio.png[#elseif suf=='xls'||suf=='xlsx']${src}excel.png[#elseif suf=='exe']${src}exe.png[#elseif suf=='fla']${src}flash.png[#elseif suf=='html']${src}html.png[#elseif suf=='mp4']${src}mp4.png[#elseif suf=='pdf']${src}pdf.png[#elseif suf=='ppt']${src}ppt.png[#elseif suf=='txt']${src}txt.png[#elseif suf=='doc'||suf=='docx']${src}word.png[#elseif suf=='xml']${src}xml.png[#elseif suf=='zip']${src}zip.png[#elseif suf=='avi'||suf=='wmv'||suf=='rmvb'||suf=='3gp']${src}video.png[#else]${src}currency.png[/#if]" /></div>
          				<input type="text" style="width:69%" class="txt" name="productAttachs[${attachmentIndex}].name" value="${productAttach.name}" placeholder="请输入附件名称"/>.${productAttach.suffix}
						<textarea name="productAttachs[${attachmentIndex}].memo" class="txt" placeholder="请输入备注信息">${productAttach.memo}</textarea><i class="deleteAttachment del"></i>
						<input name="productAttachs[${attachmentIndex}].url" value="${productAttach.url}" type="hidden">
						<input name="productAttachs[${attachmentIndex}].id" value="${productAttach.id}" type="hidden">
						<input name="productAttachs[${attachmentIndex}].storeMember.id" value="${productAttach.store_member}" type="hidden">
						<input name="productAttachs[${attachmentIndex}].thumbnail" value="${productAttach.thumbnail}" type="hidden">
						<input name="productAttachs[${attachmentIndex}].suffix" value="${productAttach.suffix}" type="hidden">
						<input name="productAttachs[${attachmentIndex}].downloadTimes" value="[#if productAttach.download_times??]${productAttach.download_times}[#else]0[/#if]" type="hidden">
						<input type="hidden" name="productAttachs[${attachmentIndex}].productAttachCategory.id" value=""/>
					</li>
					[#assign attachmentIndex = attachmentIndex+1]
					[/#if]
					[/#list]
				</ul>
			</div>
  		</div>
		
		
		<!-- ********************************************************************************************************* -->
		
			[#--[#list productAttachCategorys as productAttachCategory]
				<table class="input input-edit">
				<tr class="border-L1">
					<th>${productAttachCategory.name}:</th>
					<td colspan="7"><a href="javascript:;" class="button addAttachment">添加产品资料</a><input type="hidden"  class="att" value="${productAttachCategory.id}" /></td>
				</tr>
				<tr class="border-L1">
					<td colspan="8">
					<table class="input input-edit ta_c" style="width:100%">
						<tr class="title-tr">
						<th style="width:330px;max-width:330px">附件</th><th style="width:60%;max-width:60%">备注</th><th style="width:85px;max-width:85px">操作</th>
						</tr>
						<script type="text/javascript">
						$().ready(function() {
							var acIdHtml = '<input type="hidden" name="productAttachs[' + attachmentIndex + '].productAttachCategory.id" value="${productAttachCategory.id}" onChange="" \/>';
							var html='';
							var $tr = $("input.att[value='${productAttachCategory.id}']").closest("tr");
// 							var id = $tr.find("input[class='att']").val();
							[#list productAttachs as productAttach]
								[#if productAttach.product_attach_category==productAttachCategory.id]
				    				var url = "${productAttach.url}";
									var fileObj = getfileObj("${productAttach.file_name}");
									/**设置隐藏值*/
									var hideValues = {};
									hideValues['productAttachs['+attachmentIndex+'].url']=url;
									hideValues['productAttachs['+attachmentIndex+'].suffix']=fileObj.suffix;
									var str= createFileStr({
										url : url,
										fileName : fileObj.file_name,
										name : fileObj.name,
										suffix : fileObj.suffix,
										time : '',
										textName:'productAttachs['+attachmentIndex+'].name',
										hideValues:hideValues
									});
									var str_2='<td><div><textarea class="text file_memo" name="productAttachs['+attachmentIndex+'].memo" >${productAttach.memo}</textarea></div>'+acIdHtml+'</td>'+
									'<td  style="width:85px;max-width:85px"><a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a></td>';
							
									html += '<tr><td>'+str+'</td>'+str_2+'<\/tr>';	
									attachmentIndex ++;
								[/#if]
							[/#list]
							$tr.next().find("tr.title-tr").after(html);
						})
						
						</script>
					</table>
					</td>
				</tr>
				</table>
			[/#list]--]
			<!-- <table class="input input-edit">
				<tr class="border-L1">
					<th>${message("产品资料")}:</th>
					<td colspan="7"><a href="javascript:;" class="button addAttachment">添加产品资料</a></td>
				</tr>
				<tr class="border-L1">
					<td colspan="8">
					<table class="input input-edit ta_c" style="width:100%">
						<tr class="title-tr">
						<th style="width:330px;max-width:330px">附件</th><th style="width:60%;max-width:60%">备注</th><th style="width:85px;max-width:85px">操作</th>
						</tr>
					</table>
					</td>
				</tr>
			</table> -->
			
		</div>
		<div class="fixed-top">
			<a href="/product/product/add/${code}.jhtml?type=${type}" class="iconButton" id="addButton">
				<span class="addIcon">&nbsp;</span>${message("1001")}
			</a>
			<a href="javascript:void(0);" onclick="deleteProduct(this)" class="button cancleButton">${message("删除")}</a>
			<input type="submit" class="button sureButton" value="${message("1013")}" />
    		<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
	</form>
</body>
</html>