<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("新增会员价")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
function editQty(t,e){
	extractNumber(t,3,false,e)
}
function editPrice(t,e){
	extractNumber(t,2,false,e)
}
function setDate(){
	var start=$("input[name='startDate']").val();
	var end=$("input[name='endDate']").val();
	var $startDate = $("input.startDate");
	$startDate.each(function(){
		var $this = $(this);
		var $tr = $this.closest("tr");
		$tr.find(".startDate").val(start);
		$tr.find(".endDate").val(end);
	});
}
$().ready(function() {
	var $inputForm = $("#inputForm");
	var $selectMember = $("#selectMember");
	var $deleteProduct = $("a.deleteProduct");
	var $addProduct = $("#addProduct");
	var itemIndex = 0;
	
	// 表单验证
	$.validator.addClassRules({
		// price: {
		// 	required: true
		// },
		startDate: {
			required: true
		},
		endDate: {
			required: true
		},
		memberRankName: {
			required: true
		},
		saleOrgName: {
			required: true
		},
		start:{
			required: true
		},
		end:{
			required: true
		}
	});

	//打开选择产品界面
	$addProduct.click(function(){
		var saleOrgId = $("input.saleOrgId").val();
		if (saleOrgId==undefined || saleOrgId.length == 0) {
			$.message_alert("请选择机构");
			return false;
		}
		$addProduct.bindQueryBtn({
			type:'product',
			bindClick:false,
			title:'查询产品',
			url:'/product/product/selectProduct.jhtml?multi=2',
			callback:function(rows){
				if(rows.length>0){
					for (var i = 0; i < rows.length;i++) {
						var row = rows[i];
						//$mmGrid.removeRow();
						$mmGrid.addRow(row);
						setDate();
					}
				}
			}
		});	
	})
	
	//打开选择产品系列界面
	$("#addProductCategory").click(function(){	
		var count = $("input.saleOrgId").length;
		if(count<1){
			$.message_alert("请选择机构");
		}
		$addProduct.bindQueryBtn({
			type:'product',
			bindClick:false,
			title:'查询产品系列',
			url:'/product/product_category/select_productCategory.jhtml',
			callback:function(rows){
				if(rows.length>0){
					for (var i = 0; i < rows.length;i++) {
						var row = rows[i];
						row.product_category_id=row.id;
						row.product_category_name=row.name;
						row.id='';
						row.name='';
						$mmGrid.removeRow();
						$mmGrid.addRow(row);
						setDate();
					}
				}
			}
		});
	});
	var itemIndex = 0;
	var cols = [	
       {title:'${message("发货仓")}', align:'center',name:'account_type',width:80  ,renderer: function(val,item,rowIndex){
	    var html = '<select name="productPrices['+itemIndex+'].shippingWarehouse.id" class="text">'
		[#list shippingWarehouses as shippingWarehouse]
			+'<option value="${shippingWarehouse.id}" >${shippingWarehouse.value}</option> '
		[/#list]
		+'</select>';
	        return html;
        }},
       {title:'${message("产品系列")}', align:'center',width:120 , renderer: function(val,item,rowIndex){
	     return '<input type="hidden" name="productPrices['+itemIndex+'].productCategory.id" class="productId_'+item.product_category_id+' productId" value="'+item.product_category_id+'">'+item.product_category_name;
        }},
		{ title:'${message("产品名称")}', align:'center',width:100 , renderer: function(val,item,rowIndex){
			return '<input type="hidden" name="productPrices['+itemIndex+'].productStore.product.id" class="productId_'+item.id+' productId" value="'+item.id+'">'+item.name;
		}},
		{ title:'${message("产品型号")}', name:'model', align:'center',width:100},
		{ title:'${message("12211")}', name:'vonder_code', align:'center',width:150 },
		{ title:'${message("产品等级")}',name:'level_Id', align:'center', renderer: function(val,item,rowIndex,obj){
			var str='selected="selected"';
			var html='<select name="productPrices['+itemIndex+'].productLevel.id" class="text productGrade">';
				[#list productLevelList as products]
				if(${products.id}==item.level_Id){
					html+='<option value="${products.id}" '+str+' >${products.value}</option> ';
				}else{
					html+='<option value="${products.id}">${products.value}</option> ';
				}
				[/#list]
				html+='</select>';
			return html; 
		}},
        { title:'${message("是否返利")}', name:'is_rebate' ,align:'center', width:60, renderer: function(val,item,rowIndex, obj){
                var str='selected="selected"';
                var html='<select name="productPrices['+itemIndex+'].isRebate" class="text isRebate">';
                    html+='<option value="1" '+str+'>是</option> ';
                    html+='<option value="2" >否</option> ';
                html+='</select>';
                return html;
            }},
		{ title:'${message("产品描述")}', name:'description', align:'center',width:160 },
		{ title:'${message("经销商价格/平方米 ")}', align:'center',width:130,renderer: function(val,item,rowIndex){
				var html = '';
				[#if importSalePriceRole == 1]
				html = '<div class="nums-input ov">'+
            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice(this.nextSibling,event)">'+
            	'<input type="text"  class="t price productPrice"  name="productPrices['+itemIndex+'].storeMemberPrice" value="" minData="0" oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)" >'+
            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice(this.previousSibling,event)">'+
        	'</div>';
				[/#if]
        	return html;
		}},
		{ title:'${message("平台结算价格/平方米")}',align:'center',width:130,renderer: function(val,item,rowIndex){
				var html = '';
				[#if importSaleOrgPriceRole == 1]
			var html = '<div class="nums-input ov">'+
            	'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice(this.nextSibling,event)">'+
            	'<input type="text"  class="t price saleOrgPrice"  name="productPrices['+itemIndex+'].saleOrgPrice" value="" minData="0" oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)" >'+
            	'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice(this.previousSibling,event)">'+
        	'</div>';
				[/#if]
        	return html;
		}},
        { title:'${message("产品部结算价格/平方米")}',[#if  editProductOrgPrice == 0]hidden:true,[/#if]name:'product_org_price',width:130, align:'center',renderer:function(val,item,rowIndex,obj){
				var html = '';
				[#if importProductOrgPriceRole == 1]
        		var html = '<div class="nums-input ov">'+
                    '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice(this.nextSibling,event)">'+
                    '<input type="text"  class="t price productOrgPrice"  name="productPrices['+itemIndex+'].productOrgPrice" value="" minData="0" oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)" >'+
                    '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice(this.previousSibling,event)">'+
                    '</div>';
				[/#if]
                return html;
            }},

        { title:'${message("产品部出厂价")}',[#if  editProductFactoryPrice == 0]hidden:true,[/#if]name:'leave_factory_price',width:130, align:'center',renderer:function(val,item,rowIndex,obj){
				var html = '';
                [#if importProductFactoryPrice == 1]
        		var html = '<div class="nums-input ov">'+
                    '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice(this.nextSibling,event)">'+
                    '<input type="text"  class="t price leaveFactoryPrice"  name="productPrices['+itemIndex+'].leaveFactoryPrice" value="" minData="0" oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)" >'+
                    '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice(this.previousSibling,event)">'+
                    '</div>';
                [/#if]
                return html;
            }},
		{ title:'${message("开始时间")}', name:'', align:'center',renderer: function(val,item,rowIndex){
			return '<input  id="productPrices['+itemIndex+'].startDate" name="productPrices['+itemIndex+'].startDate" class="text startDate" value="${startDate}" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\',startDate:\'%y-%M-%d\', maxDate: \'#F{$dp.$D(\\\''+'productPrices['+itemIndex+'].endDate\\\')}\'});" type="text" btn-fun="clear"/>'
		}},
		{ title:'${message("结束时间")}', name:'', align:'center',renderer: function(val,item,rowIndex){
			return '<input  id="productPrices['+itemIndex+'].endDate" name="productPrices['+itemIndex+'].endDate" class="text endDate" value="${endDate}" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\',startDate:\'%y-%M-%d\', minDate: \'#F{$dp.$D(\\\''+'productPrices['+itemIndex+'].startDate\\\')}\'});" type="text" btn-fun="clear"/>'
		}},		
		{ title:'${message("状态")}', name:'status' , align:'center', width:60, renderer: function(val){
			var result = '已保存';
			return result;			
		}},
		{ title:'${message("操作")}', align:'center', width:60, renderer: function(val,item,rowIndex){
			itemIndex++;
			return '<a href="javascript:;" class="btn-delete" onclick="deleteProduct(this)">删除</a>';
		}},
	];
	$mmGrid = $('#table-m1').mmGrid({
		height:'auto',
        cols: cols,
        checkCol:false,
        fullWidthRows: true
    });
    
    $("#submit_button").click(function(){
		var count = $("input.productId").length;
		if(count<1){
			$.message_alert("请添加价格明细");
		}else{
			$("form").submit();
		}
	});
    
    $("form").bindAttribute({
    	isConfirm:true,
	    callback: function(resultMsg){
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.href= '/product/product_price_head/edit/${code}.jhtml?isCheck= 1&id='+resultMsg.objx;
			});
	    }
	 });
    
  //查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml?isSellSaleOrg=1',
		callback:function(rows) {
			if (rows.length > 0) {
				var row = rows[0];
				$("input.saleOrgId").val(row.id);
				$("input.saleOrgName").val(row.name);
				//清空价格类型
				$(".memberRankId").val('');
				$(".memberRankName").val('');
			}
		}
	});
});

	//查询价格类型
	$("#selectMemberRank").live("click",function(){
		var $this = $(this);
		var saleOrgId = $(".saleOrgId").val();
		if(saleOrgId==null || saleOrgId==''){
			$.message_alert('请先选择机构！');
			return false;
		}
		$this.bindQueryBtn({
			type:'memberRank',
			bindClick:false,
			title:'${message("查询价格类型")}',
			url:'/basic/member_rank/select_memberRank.jhtml?saleOrgId='+saleOrgId+"&sourceType=2",
			callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
					$("input.memberRankId").val(row.id);
					$("input.memberRankName").val(row.name);
				}
			}
		});
	})

function deleteProduct(e){
	var index = $(e).closest("tr").index();
	$.message_confirm('您确定要删除吗？',function(){
		$mmGrid.removeRow(index);
	})
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增价格表")}
	</div>
	<form id="inputForm" action="/product/product_price_head/save.jhtml" method="post" type="ajax" validate-type="validate">
		<div class="tabContent">
		<table class="input input-edit">
			<tr>
				<th>
					${message("价格单号")}:
				</th>
				<td>
					${productPriceHead.sn}
				</td>
				<th>
					${message("单据状态")}:
				</th>
				<td>
				</td>
				<th>
					${message("流程状态")}:
				</th>
				<td>
				</td>
				<th>
					${message("创建日期")}:
				</th>
				<td>
				</td>
			</tr>
			<tr>
				<th>
					<span class="requiredField">*</span>${message("价格类型")}:
				</th>
				<td>
					<span class="search" style="position:relative">
						<input type="hidden" name="memberRankId" class="text memberRankId" btn-fun="clear" value="${memberRankList.id}"/>
						<input type="text" name="memberRankName" class="text memberRankName" maxlength="200" onkeyup="clearSelect(this)" value="${memberRankList.name}" readOnly/>
						<input type="button" class="iconSearch" value="" id="selectMemberRank">
					</span>
				</td>
				 [#--  <td>
                	<select class="text" name="memberRankId">
						[#list memberRankList as memberRank]
						<option value="${memberRank.id}" [#if memberRank.isDefault]selected="selected"[/#if]>${memberRank.name}</option>
						[/#list]
					</select>
                </td> --]
				<th>${message("机构")}:</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value="${saleOrg.id}"/>
					<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" value="${saleOrg.name}" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectSaleOrg">
					</span>
				</td>
			    <th>
					${message("开始时间")}:
				</th>
				<td id="start">
					<input type="text" class="text start" id="startdate" name="startDate" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',maxDate: '#F{$dp.$D(\'enddate\')}'});" btn-fun="clear"/>
				</td>

				<th>
					${message("结束时间")}:
				</th>
				<td id="end">
					<input type="text" class="text end" id="enddate" name="endDate" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',minDate: '#F{$dp.$D(\'startdate\')}'});" btn-fun="clear" />
				</td>
			</tr>
		</table>
		<table class="input input-edit" style="width:100%;margin-top:5px;">
			 <tr class="border-L1">
				<th> ${message("价格明细")}:
				</th>
				<td colspan="7">
				<!--   <a href="javascript:;" id="addProductCategory" class="button">选择产品系列</a> -->
				  <a href="javascript:;" id="addProduct" class="button">选择产品</a>
				</td>
			</tr>
			<tr class="border-L1">
				<td colspan="8">
					<div>
						<table id="table-m1"></table>
					</div>
				</td>
			</tr>
		</table>
	</div>
	<div class="fixed-top">
		<input type="button" id="submit_button" class="button sureButton" value='${message("保存")}'>
		<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
	</div>
	</form>
</body>
</html>