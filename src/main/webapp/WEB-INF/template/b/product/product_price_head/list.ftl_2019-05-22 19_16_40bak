<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("价格表列表")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
function edit(id){
	parent.change_tab(0,'/product/product_price_head/edit/${code}.jhtml?id='+id+'&isCheck= ${isCheck}');
}
function add(){
	parent.change_tab(0,'/product/product_price_head/add/${code}.jhtml');
}
$().ready(function() {
	/**初始化多选的下拉框*/
	initMultipleSelect();
	
	var order_sttuss = {'0':'已保存', '1':'处理中', '2':'已生效', '3':'已失效'};
	var wf_state ={'0':'未启动','1':'审核中','2':'已完成','3':'驳回',}
	var productGrade = {'1':'优等品','2':'二等品','3':'一等品','4':'无等级',}
	var cols = [
		{ title:'${message("价格单号")}', name:'sn' , align:'center', renderer: function(val,item,rowIndex){
			return '<a href="javascript:void(0);" onClick="edit('+item.id+')" class="red">'+val+'</a>';
		}},
		{ title:'${message("价格类型")}', name:'member_rank_name', align:'center' },
		{ title:'${message("机构")}', name:'saleOrgName', align:'center' },
		{ title:'${message("发货仓")}', name:'warehouse_name' , align:'center',isLines:true},
		{ title:'${message("产品名称")}', name:'name' , align:'center',isLines:true},
		{ title:'${message("产品型号")}', name:'model' , align:'center',isLines:true},
		{ title:'${message("12211")}', name:'vonder_code' , align:'center',isLines:true },
		{title:'${message("产品系列")}',name:'product_category_name',align:'center',isLines:true},
		{ title:'${message("产品等级")}',name:'productGrade',align:'center',isLines:true,renderer: function(val,item,rowIndex){
			if (val != null && val != "") return productGrade[val];
		}},
		{ title:'${message("产品描述")}', name:'description', align:'center',width:160,isLines:true},
		{ title:'${message("经销商价格")}', name:'store_member_price' , align:'center',isLines:true , renderer: function(val,item){
			if(item.store_member_price!=null){
				return '<span class="red">'+currency(item.store_member_price,true)+'</span>';
			}
		}},
		{ title:'${message("平台结算价格")}', name:'sale_org_price' , align:'center',isLines:true , renderer: function(val,item){
			if(item.sale_org_price != null){
				return '<span class="red">'+currency(item.sale_org_price,true)+'</span>';
			}
		}},
		{ title:'${message("开始时间")}', name:'start_date' , align:'center',isLines:true,renderer:function(val){
			if(val !=null && val.length>10){
				var str = val;
				return str.substring(0,10);
			}
		}},
		{ title:'${message("结束时间")}', name:'end_date' , align:'center',isLines:true,renderer:function(val){
			if(val !=null && val.length>10){
				var str = val;
				return str.substring(0,10);
			}
		}},
		{ title:'${message("单据状态")}', name:'status' , align:'center', renderer: function(val){
			var result = order_sttuss[val];
			if(result!=undefined)return result;			
		}},
// 		{ title:'${message("流程状态")}', name:'wf_state' , align:'center', renderer: function(val){
// 			var result = wf_state[val];
// 			if(result!=undefined)return result;			
// 		}},
		
		{ title:'${message("创建日期")}', name:'create_date' ,align:'center' },

	];
	$mmGrid = $('#table-m1').mmGrid({
		autoLoad:true,
        cols: cols,
        fullWidthRows:true,
        lineRoot:"product_price_items",
        url: '/product/product_price_head/list_data.jhtml',
        method: 'post',
        params:function(){
        	return $("#listForm").serializeObject();
        },
        root: 'content',
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });
    
	$("#selectProduct").bindQueryBtn({
		type:'product',
		title:'${message("查询产品")}',
		url:'/product/product/selectProduct.jhtml'
	});
	  //查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml?isSellSaleOrg=1'
	});
	
	//查询价格等级
	$("#selectMemberRank").bindQueryBtn({
		type:'memberRank',
		title:'${message("查询价格等级")}',
		url:'/basic/member_rank/select_memberRank.jhtml?multi=2',
		callback:function(rows){
			if(rows.length>0){
				var mhtml="";
				if($("input[name='memberRankName']").val() == null){
					var all= "";
				}else{
					var all= $("input[name='memberRankName']").val();
				}
				
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".memberRankId_"+rows[i].id).length;
					if(idH > 0){
						$.message_alert('价格等级【'+rows[i].name+'】已添加');
						return false;
					}
				}
				for (var i = 0; i < rows.length;i++) {
					all =all +','+ rows[i].name;
					mhtml = '<div><input name="memberRankId" class="text memberRankId memberRankId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newclosePro(this)"></i></div>';
					$(".memberRank").append(mhtml);
				}
				$("input[name='memberRankName']").attr("value",all);
			}
		}
	});
	

});

function newclosePro(e){
	$(e).closest("div").remove();
	var allName2 = '';
	$(".memberRank > div").each(function(){
		allName2 = allName2 +','+  $(this).find("p").html();
	})
	$("input[name='memberRankName']").attr("value",allName2)
}

function productPriceImport(e){	
	excel_import(e,{
		title:"${message("产品会员价")}",
		url:"/product/product_price_head/import_excel.jhtml?code=${code}",
		template:"/resources/template/product/productPrice.xls",
		callback:function(){
			$("#searchBtn").click();
		}
	})
}	

//条件导出		    
function segmentedExport(e){
	var needConditions = false;//至少一个条件
	var page_url = '/product/product_price_head/to_condition_export.jhtml';//分页导出统计页面
	var url = '/product/product_price_head/condition_export.jhtml';//导出的方法
	conditions_export(e,{needConditions:needConditions,page_url:page_url,url:url});
}

//选择导出
function exportExcel(t){
	var param = $mmGrid.serializeSelectedIds();//参数
	var tip = '${message("请选择导出的会员价单！")}';//提示
	var url = '/product/product_price_head/selected_export.jhtml';//导出的方法
  	select_export(t,{tip:tip, param:param, url:url});
}

function shippingCheck(e){
	var data =$mmGrid.serializeSelectedIds();
	if(data.length==0){
		$.message_alert("${message("请选择需要审核的价格表")}");
		return false;
	}

	ajaxSubmit(e,{
		url:"/product/product_price_head/check.jhtml",
		method:"post",
		data:data,
		isConfirm:true,
		confirmText : '您确定要审核吗？',
		callback:function(resultMsg){
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				$mmGrid.load();
			});
		}
	
	});

}

function cancel(e){
	var data =$mmGrid.serializeSelectedIds();
	if(data.length==0){
		$.message_alert("${message("请选择需要失效的价格表")}");
		return false;
	}

	ajaxSubmit(e,{
		url:"/product/product_price_head/cancel.jhtml",
		method:"post",
		data:data,
		isConfirm:true,
		confirmText : '您确定要失效吗？',
		callback:function(resultMsg){
			$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				$mmGrid.load();
			});
		}
	
	});

}
</script>
</head>
<body>
	<form id="listForm" action="/product/product_price_head/list/${code}.jhtml" method="get">
	<input type="hidden" name="isCheck" id="isCheck" value="${isCheck}" >
	<input type="hidden" name="objTypeId" value="${objTypeId}" >
	<input type="hidden" name="objid" value="${objid}" >
		<div class="bar">
			<div class="buttonWrap">
				<a id="shengheButton" class="button sureButton" onclick="shippingCheck(this)"  >${message("审核")}</a>
				<a href="javascript:void(0);" class="button cancleButton" onclick="cancel(this)">${message("失效")}</a>
				<div class="flag-wrap flagImp-wrap">
					<a href="javascript:void(0);" class="iconButton" id="export1Button">
						<span class="impIcon">&nbsp;</span>导入导出
					</a>
					<ul class="flag-list">
						<li><a href="javascript:void(0)" onclick="productPriceImport(this)"><i class="flag-imp01"></i>${message("导入")}</a></li>
						<li><a href="javascript:void(0)" onclick="exportExcel(this)"><i class="flag-imp02"></i>${message("选择导出")}</a></li>
						<li><a href="javascript:void(0)" onclick="segmentedExport(this)"><i class="flag-imp02"></i>${message("条件导出")}</a></li>
					</ul>
				</div>
				[#if isCheck==1 && objid==null]
				<a href="javascript:add();" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
				[/#if]
			</div>
			<div id="searchDiv">
        	<div id="search-content" >
        		<dl>
        			<dt><p>${message("单据状态")}：</p></dt>
        			<dd>
        				<div class="checkbox-style">
							<a href="javascript:void(0);" onclick="clearText(this)" class="deleteText close"></a>
					       	<input type="text" class="text pointer doStatus" value="[#if isCheck??]${message("已保存;处理中;已生效")}[/#if]" autocomplete="off" />
				       		<div class="statusList cs-box" data-value="off">	
				       			<label><input  class="check js-iname" name="status" value="0" type="checkbox" checked/>${message("已保存")}</label>
				       			<label><input  class="check js-iname" name="status" value="1" type="checkbox" checked />${message("处理中")}</label>
				       			<label><input  class="check js-iname" name="status" value="2" type="checkbox" checked/>${message("已生效")}</label>
				       			<label><input  class="check js-iname" name="status" value="3" type="checkbox"/>${message("已失效")}</label>
				      		</div>
						</div>
        			</dd>
        		</dl>
        		<dl>
			    	<dt><p>${message("产品名称")}：</p></dt> 
		    		<dd>
						<span style="position:relative">
							<input type="hidden" name="productId" class="text productId" btn-fun="clear" value=""/>
							<input type="text" name="productName" class="text productName" maxlength="200" value="" onkeyup="clearSelect(this)"/>
							<input type="button" class="iconSearch" value="" id="selectProduct">
						</span>
		    		</dd>
			    </dl>
        		<dl>
			    	<dt><p>${message("机构名称")}：</p></dt> 
		    		<dd>
						<span style="position:relative">
							<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value=""/>
							<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" value="" onkeyup="clearSelect(this)"/>
							<input type="button" class="iconSearch" value="" id="selectSaleOrg">
						</span>
		    		</dd>
			    </dl>
			    
			    <dl>
						<dt><p>${message("价格类型")}:</p></dt>
	    				<dd>
	    					<span style="position:relative">
								<input class="text memberRankName" maxlength="200" type="text" name="memberRankName" value="" onkeyup="clearSelect(this)">
								<input type="button" class="iconSearch" value="" id="selectMemberRank">
								<div class="pupTitleName  memberRank"></div>
							</span>
	    				</dd></dl>
			    
			   <!--  <dl>
			    	<dt><p>${message("价格类型")}：</p></dt> 价格等级
			    		<dd>
        				<div class="checkbox-style">
							<a href="javascript:void(0);" onclick="clearText(this)" class="deleteText close"></a>
					       	<input type="text" class="text pointer doStatus" value="" autocomplete="off" />
				       		<div class="statusList cs-box" data-value="off">
				       			[#list memberRankList as memberRank]
				       			<label><input  class="check js-iname" name="memberRankId" value="${memberRank.id}" type="checkbox"/>${memberRank.name}</label>					
						        [/#list]	
				      		</div>
						</div>
        			</dd>
			    </dl> -->
                [#--<dl>
                    <dt><p>${message("发货仓")}：</p></dt>
                    <dd>
                        <div class="checkbox-style">
                            <a href="javascript:void(0);" onclick="clearText(this)" class="deleteText close"></a>
                            <input type="text" class="text pointer doStatus" value="" autocomplete="off" />
                            <div class="statusList cs-box" data-value="off">
                                <label><input  class="check js-iname" name="warehouse" value="平台仓" type="checkbox" />${message("平台仓")}</label>
                                <label><input  class="check js-iname" name="warehouse" value="总部仓" type="checkbox" />${message("总部仓")}</label>
                                <label><input  class="check js-iname" name="warehouse" value="中转仓" type="checkbox" />${message("中转仓")}</label>
                            </div>
                        </div>
                    </dd>
                </dl>--]

			   <dl>
			    	<dt><p>${message("发货仓")}：</p></dt> 
			    		<dd>
        				<div class="checkbox-style">
							<a href="javascript:void(0);" onclick="clearText(this)" class="deleteText close"></a>
					       	<input type="text" class="text pointer doStatus" value="" autocomplete="off" />
				       		<div class="statusList cs-box" data-value="off">
				       			[#list shippingWarehouses as shippingWarehouse]
				       			<label><input  class="check js-iname" name="warehouse" value="${shippingWarehouse.id}" type="checkbox"/>${shippingWarehouse.value}</label>					
						        [/#list]	
				      		</div>
						</div>
        			</dd>
			    </dl>


				<dl>
                    <dt><p>${message("产品等级")}:</p></dt>
                    <dd>
                        <select id="productGrade" name="productGrade" class="text" >
                            <option value=""></option>
                            <option value="1">优等品</option>
                            <option value="2">二等品</option>
                            <option value="3">一等品</option>
                            <option value="4">无等级</option>
                        </select>
                    </dd>
				</dl>

			    <dl>
					<dt><p>${message("开始时间")}:</p></dt>
						<dd class="date-wrap">
							<input id="startTime" name="firstTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
					    </dd>
                    <dt><p>--</p></dt>
					    <dt><p>${message("截止时间")}:</p></dt>
					    <dd class="date-wrap">
							<input id="endTime" name="lastTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});" type="text" btn-fun="clear"/>
					    </dd>
				</dl>
                <dl>
                    <dt><p>${message("创建时间")}：</p></dt>
                    <dd class="date-wrap">
                        <input id="createTimeID" name="createTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear"/>
                    </dd>
                    <dt>
                </dl>
			</div>
		<div class="search-btn" style="height:32px"><a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a></div>
		</div>
			
		</div>
		<div class="table-responsive">
	        <table id="table-m1"></table>
	        <div id="body-paginator">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</body>
</html>