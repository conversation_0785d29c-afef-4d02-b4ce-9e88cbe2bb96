<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<title>${message("价格表列表")}</title>
	<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
	<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
	<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
	<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
	<script type="text/javascript" src="/resources/js/base/request.js"></script>
	<script type="text/javascript" src="/resources/js/base/global.js"></script>
	<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="/resources/js/utils.js"></script>
	<script type="text/javascript" src="/resources/js/dynamicForm/mmGridConfiguration.js"></script>
	<script type="text/javascript" src="/resources/js/dynamicForm/formList.js"></script>
	<script type="text/javascript" src="/resources/js/dynamicForm/searchControlType.js"></script>
	<script type="text/javascript">
		$().ready(function() {
			//获取按钮控件
			getButtonHtml(0,'${dTemplates.id}');
			//获取筛选html
			parameterList('${userId}','${dTemplates.id}');
			//遍历列表表头
			traverseList('${userId}','${dTemplates.id}','${defaultQuery}','${dTemplates.pdfPath}','${dTemplates.excelPath}');
		});

		//查看
		function edit(id){
			parent.change_tab(0,'/product/product_price_head/edit/${code}.jhtml?id='+id+'&isCheck= ${isCheck}');
		}
		//新增
		function add(e){
			parent.change_tab(0,'/product/product_price_head/add/${code}.jhtml');
		}

		//审核
		function billCheck(e){
			var data =$mmGrid.serializeSelectedIds();
			if(data.length==0){
				$.message_alert("${message("请选择需要审核的价格表")}");
				return false;
			}
			var str = "您确定要审核吗？";
			var url = "/product/product_price_head/check.jhtml";
			check(e,data,str,url);
		}


		//作废
		function billCancel(e){
			var data =$mmGrid.serializeSelectedIds();
			if(data.length==0){
				$.message_alert("${message("请选择需要失效的价格表")}");
				return false;
			}
			var str = "您确定要失效吗？";
			var url = "/product/product_price_head/cancel.jhtml";
			cancel(e,data,str,url);
		}
	</script>
</head>
<body>
<form id="listForm" action="/product/product_price_head/list/${code}.jhtml" method="get">
	<input type="hidden" name="userId" class="userId"  value="${userId}"/>
	<input type="hidden" name="templateId" class="templatesId"  value="${dTemplates.id}"/>
	<input type="hidden" name="pagesize" class="pagesize" value="${pagesize}"/>
	<input type="hidden" name="$saleorgid" class="$saleorgid" value="${saleOrgId}"/>
	<div class="bar">
		<div class="buttonWrap"></div>
		<div id="searchDiv">
			<div id="search-content" >
				<table id="search-table"></table>
			</div>
			<div class="search-btn" style="height:32px">
				<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
			</div>
		</div>
	</div>
	<div class="table-responsive">
		<table id="table-m1"></table>
		<div id="body-paginator">
			<div id="paginator"></div>
		</div>
	</div>
</form>
</body>
</html>