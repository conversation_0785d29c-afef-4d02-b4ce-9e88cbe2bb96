<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("编辑价格表")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/utils.js"></script>
<script type="text/javascript">
function editQty(t,e){
	extractNumber(t,3,false,e)
}
function editPrice(t,e){
	extractNumber(t,2,false,e)
}
function setDate(){
	var start=$("input[name='startDate']").val();
	var end=$("input[name='endDate']").val();
	var $startDate = $("input.startDate");
	$startDate.each(function(){
		var $this = $(this);
		var $tr = $this.closest("tr");
		$tr.find(".startDate").val(start);
		$tr.find(".endDate").val(end);
	});
}
$().ready(function() {
	var $deleteProduct = $("a.deleteProduct");
	var itemIndex = 0;
	var $addProduct = $("#addProduct");
	
	$("#wf_area").load("/wf/wf.jhtml?wfid=${productPriceHead.wfId}");
	
    $.validator.addClassRules({
		startDate: {
			required: true
		},
		endDate: {
			required: true
		},
		// price: {
		// 	required: true
		// },
		memberRankId: {
			required: true
		},
		memberRankName: {
			required: true
		},
		saleOrgName: {
			required: true
		},
		start:{
			required: true
		},
		end:{
			required: true
		}
	});
	

	//打开选择产品界面
	$addProduct.click(function(){
		var saleOrgId = $("input.saleOrgId").val();
		if (saleOrgId==undefined || saleOrgId.length == 0) {
			$.message_alert("请选择机构");
			return false;
		}
		$addProduct.bindQueryBtn({
			type:'product',
			bindClick:false,
			title:'查询产品',
			url:'/product/product/selectProduct.jhtml',
			callback:function(rows){
				if(rows.length>0){
					for (var i = 0; i < rows.length;i++) {
						var row = rows[i];
						//$mmGrid.removeRow();
						$mmGrid.addRow(row,null,1);
					}
				}
			}
		});	
	})
	//打开选择产品系列界面
	$("#addProductCategory").click(function(){	
		var saleOrgId = $("input.saleOrgId").val();
		if (saleOrgId==undefined || saleOrgId.length == 0) {
			$.message_alert("请选择机构");
			return false;
		}
		$addProduct.bindQueryBtn({
			type:'product',
			bindClick:false,
			title:'查询产品系列',
			url:'/product/product_category/select_productCategory.jhtml',
			callback:function(rows){
				if(rows.length>0){
					var error = '';
					for (var i = 0; i < rows.length;i++) {
						var row = rows[i];
						row.product_category_id=row.id;
						row.product_category_name=row.name;
						row.id=null;
						row.name='';
						$mmGrid.removeRow();
						$mmGrid.addRow(row,null,1);
					}
				}
			}
		});
	})
	var order_sttuss = {'0':'已保存', '1':'处理中', '2':'已生效', '3':'已失效' ,'4':'产品部结算价已维护','5':'平台结算价已维护','6':'经销商价已维护'};
	var items = ${jsonStr};
	var cols = [
	    {title:'${message("发货仓")}', align:'center',name:'shipping_warehouse',width:80  ,renderer: function(val,item,rowIndex){
			var str='selected="selected"';
			var html = '<select name="productPrices['+itemIndex+'].shippingWarehouse.id" class="text">';
				[#list shippingWarehouses as shippingWarehouse]
				if(${shippingWarehouse.id}==val){
					html+='<option value="${shippingWarehouse.id}" '+str+' >${shippingWarehouse.value}</option> ';
				}else{
					html+='<option value="${shippingWarehouse.id}" >${shippingWarehouse.value}</option> ';
				}
				[/#list]
				 html+='</select>';
			return html;
	      }},
	    {title:'${message("产品系列")}', align:'center',width:120 , renderer: function(val,item,rowIndex){
	    		return '<input type="hidden" name="productPrices['+itemIndex+'].productCategory.id" class="productId_'+item.product_category_id+' productId" value="'+item.product_category_id+'">'+item.product_category_name;
	    	
	    }},
		{ title:'${message("产品名称")}', align:'center',width:100 , renderer: function(val,item,rowIndex,obj){
			var itemName =item.name;
			var itemId =item.id;
			var productId =item.product_id;
			if (item.name == null) {
				itemName = '';
			}
			if (item.id == null) {
				itemId = '';
			}
			if (item.product_id == null) {
				productId = '';
			}
			if(obj==undefined){
				return '<input type="hidden" name="productPrices['+itemIndex+'].productStore.product.id" class="productId_'+productId+'" value="'+productId+'"><input type="hidden" name="productPrices['+itemIndex+'].id" value="'+itemId+'">'+itemName;
			}else{
				return '<input type="hidden" name="productPrices['+itemIndex+'].productStore.product.id" class="productId_'+itemId+'" value="'+itemId+'"><input type="hidden" name="productPrices['+itemIndex+'].id" value="">'+itemName;
			}
		}},
		{ title:'${message("产品型号")}', name:'model', align:'center',width:100 },
		{ title:'${message("12211")}', name:'vonder_code', align:'center',width:150 },
		{ title:'${message("产品等级")}',name:'level_Id' ,align:'center', renderer: function(val,item,rowIndex){
			var str='selected="selected"';
			var html='<select name="productPrices['+itemIndex+'].productLevel.id" class="text productGrade">';
				[#list productLevelList as products]
				if(${products.id}==item.level_Id){
					html+='<option value="${products.id}" '+str+' >${products.value}</option> ';
				}else{
					html+='<option value="${products.id}">${products.value}</option> ';
				}
				[/#list]
				html+='</select>';
			return html;
		}},
        { title:'${message("是否返利")}', name:'is_rebate' ,align:'center', width:60, renderer: function(val,item,rowIndex, obj){
                if(val === '1'){
                    return '<span>是</span>';
                }else if(val === '2'){
                    return '<span>否</span>';
                }
            }},
        { title:'${message("产品描述")}', name:'description', align:'center',width:160 },
		{ title:'${message("经销商价格/平方米")}',name:'store_member_price',width:130, align:'center',renderer:function(val,item,rowIndex,obj){
				var html = '';
			var price = '';
			if(obj==undefined){
				price = item.store_member_price;
			}
				[#if importSalePriceRole == 1]
				var html = '<div class="nums-input ov">'+
            		'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice(this.nextSibling,event)">'+
            		'<input type="text"  class="t price productPrice"  name="productPrices['+itemIndex+'].storeMemberPrice" value="'+price+'" minData="0" oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)" >'+
            		'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice(this.previousSibling,event)">'+
        			'</div>';
        		return html;
				[/#if]
				[#if importSalePriceRole == 0]
				if(!val && val != 0){
					return html;
				}else{
					return '<span class="text red productPrice">'+currency(price,true)+'</span><input type="hidden"  class="t text storeMemberPrice"  name="productPrices['+itemIndex+'].storeMemberPrice" value="'+price+'" >'
				}
				[/#if]
			}},
		{ title:'${message("平台结算价格/平方米")}',[#if  seeSaleOrgPrice == 0]hidden:true,[/#if]name:'sale_org_price',width:130, align:'center',renderer:function(val,item,rowIndex,obj){
				var html = '';
			var price = '';
				if(val != null){
					price =val;
				}
			if(obj==undefined){
				price = item.sale_org_price;
			}
				[#if importSaleOrgPriceRole == 1]
				return  '<div class="nums-input ov">'+
						'<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice(this.nextSibling,event)">'+
						'<input type="text"  class="t price saleOrgPrice"  name="productPrices['+itemIndex+'].saleOrgPrice" value="'+price+'" minData="0" oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)" >'+
						'<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice(this.previousSibling,event)">'+
						'</div>';
				[/#if]
				[#if importSaleOrgPriceRole == 0]
				if(!val && val != 0){
					return html;
				}else{
					return '<span class="text red saleOrgPriceText">'+currency(price,true)+'</span><input type="hidden"  class="t text saleOrgPrice"  name="productPrices['+itemIndex+'].saleOrgPrice" value="'+price+'" >'
				}
				[/#if]
			}},
        { title:'${message("产品部结算价格/平方米")}',[#if  editProductOrgPrice == 0]hidden:true,[/#if] name:'product_org_price',width:130, align:'center',renderer:function(val,item,rowIndex,obj){
				var html = '';
        		var price = '';
                if(val != null){
					price =val;
				}
                if(obj==undefined){
                    price = item.product_org_price;
                }
				[#if importProductOrgPriceRole == 1]
                var html = '<div class="nums-input ov">'+
                    '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice(this.nextSibling,event)">'+
                    '<input type="text"  class="t price productOrgPrice"  name="productPrices['+itemIndex+'].productOrgPrice" value="'+price+'" minData="0" oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)" >'+
                    '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice(this.previousSibling,event)">'+
                    '</div>';
                return html;
				[/#if]
				[#if importProductOrgPriceRole == 0]
				if(!val && val != 0){
					return html;
				}else {
					return '<span class="text red productOrgPrice">'+currency(price,true)+'</span><input type="hidden"  class="t text productOrgPrice"  name="productPrices['+itemIndex+'].productOrgPrice" value="'+price+'" >'
				}
				[/#if]
        }},

		{ title:'${message("产品部出厂价")}',[#if  editProductFactoryPrice == 0]hidden:true,[/#if] name:'leave_factory_price',width:130, align:'center',renderer:function(val,item,rowIndex,obj){
				var html = '';
        		var price = '';
                if(val != null){
					price =val;
				}
                if(obj==undefined){
					price = item.leave_factory_price;
                }
                [#if importProductFactoryPrice == 1]
                var html = '<div class="nums-input ov">'+
                    '<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editPrice(this.nextSibling,event)">'+
                    '<input type="text"  class="t price leaveFactoryPrice"  name="productPrices['+itemIndex+'].leaveFactoryPrice" value="'+price+'" minData="0" oninput="editPrice(this,event)" onpropertychange="editPrice(this,event)" >'+
                    '<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editPrice(this.previousSibling,event)">'+
                    '</div>';
                return html;
                [/#if]
                [#if importProductFactoryPrice == 0]
                if(!val && val !== 0 && val != ''){
					return '<span class="text red leaveFactoryPrice">'+currency(price,true)+'</span><input type="hidden"  class="t text leaveFactoryPrice"  name="productPrices['+itemIndex+'].leaveFactoryPrice" value="'+price+'" >'
                }else {
                    return html;
                }
                [/#if]

        }},
		{ title:'${message("开始时间")}', name:'start_date', align:'center',renderer: function(val,item,rowIndex,obj){
			var start_date = '';
			if(obj==undefined){
				start_date = item.start_date;
				if(start_date!=null && start_date.length>10){
					start_date = start_date.substring(0,10);
				}
			}
			return '<input id="productPrices['+itemIndex+'].startDate" name="productPrices['+itemIndex+'].startDate" class="text startDate" value="'+start_date+'" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\',startDate:\'%y-%M-%d\', maxDate: \'#F{$dp.$D(\\\''+'productPrices['+itemIndex+'].endDate\\\')}\'});" type="text" btn-fun="clear"/>'
		}},
		{ title:'${message("结束时间")}', name:'end_date', align:'center',renderer: function(val,item,rowIndex,obj){
			var end_date = '';
			if(obj==undefined){
				end_date = item.end_date;
				if(end_date!=null && end_date.length>10){
					end_date = end_date.substring(0,10);
				}
			}
			return '<input id="productPrices['+itemIndex+'].endDate" name="productPrices['+itemIndex+'].endDate" class="text endDate" value="'+end_date+'" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\',startDate:\'%y-%M-%d\', minDate: \'#F{$dp.$D(\\\''+'productPrices['+itemIndex+'].startDate\\\')}\'});" type="text" btn-fun="clear"/>'
		}},		
		{ title:'${message("状态")}', name:'status' , align:'center', width:60, renderer: function(val){
			if(val=='')val = '0';
			var result = order_sttuss[val];
			if(result!=undefined)return result;			
		}},
		{ title:'${message("操作")}', align:'center', width:60, renderer: function(val,item,rowIndex,obj){
			var html='';
			if(obj != undefined || item.status == 0 || item.status == 4 || item.status == 5 || item.status == 6){
				html =  '<a href="javascript:;" class="btn-delete" onclick="deleteProduct(this)">删除</a>';
			}else if(item.status==2){
				html= '<a href="javascript:;" class="btn-delete" onclick="cancelLine(this,'+item.id+')">失效</a>';
			}else{
				html = '-';
			}
			itemIndex++;
			return html;
		}},
	];
	$mmGrid = $('#table-m1').mmGrid({
		height:'auto',
        cols: cols,
        items:items,
        fullWidthRows:true,
        checkCol: false,
        autoLoad: true
    });
    
    var orderFullLink_items = ${fullLink_json};
	var cols = [				
    	{ title:'${message("内容")}', name:'content' ,width:300,align:'center'},
		{ title:'${message("操作人")}', name:'operator_name',width:100 ,align:'center'},
		{ title:'${message("创建日期")}', name:'create_date' ,width:150 ,align:'center'}
	];
	$('#table-full').mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: cols,
        items:orderFullLink_items,
        checkCol: false,
        autoLoad: true
    });
    
    $("form").bindAttribute({
    	isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
			})
	    }
	 });
    
  //查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml?isSellSaleOrg=1',
		callback:function(rows) {
			if (rows.length > 0) {
				var row = rows[0];
				$("input.saleOrgId").val(row.id);
				$("input.saleOrgName").val(row.name);
				//清空价格类型
				$(".memberRankId").val('');
				$(".memberRankName").val('');
			}
		}
	});

	//查询价格类型
	$("#selectMemberRank").live("click",function(){
		var $this = $(this);
		var saleOrgId = $(".saleOrgId").val();
		if(saleOrgId==null || saleOrgId==''){
			$.message_alert('请先选择机构！');
			return false;
		}
		$this.bindQueryBtn({
			type:'memberRank',
			bindClick:false,
			title:'${message("查询价格类型")}',
			url:'/basic/member_rank/select_memberRank.jhtml?saleOrgId='+saleOrgId+"&sourceType=2",
			callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
					$("input.memberRankId").val(row.id);
					$("input.memberRankName").val(row.name);
				}
			}
		});
	})
});
function deleteProduct(e){
	var index = $(e).closest("tr").index();
	$.message_confirm('您确定要删除吗？',function(){
		$mmGrid.removeRow(index);
	})
}

function add(wf_index,url){
	location.href=url;
}

function subflag(){
$("#isSubmit").val(1);
}

function cancel(e){
	var str = '<div>您确定要&nbsp;&nbsp;<b>失效</b>&nbsp;&nbsp;当前会员价单吗？<br/>确定：将失效当前单据的所有产品会员价。<br/>取消：不做任何处理。</div>';
	$.message_confirm(str,function() {
		Mask();
		ajaxSubmit(e,{
			url: '/product/product_price_head/cancel.jhtml?ids=${productPriceHead.id}',
			method: "post",
			failCallback:function(resultMsg){				
	        	// 访问地址失败，或发生异常没有正常返回
	        	messageAlert(resultMsg);
	            UnMask();
	        },
			callback:function(resultMsg){
				location.reload(true);
			}
		})
	});
}

function cancelLine(e,id){
	var str = '<div>您确定要&nbsp;&nbsp;<b>失效</b>&nbsp;&nbsp;当前行的产品会员价吗？<br/>确定：将失效当前行的产品会员价。<br/>取消：不做任何处理。</div>';
	$.message_confirm(str,function() {
		Mask();
		ajaxSubmit(e,{
			url: '/product/product_price_head/cancelLine.jhtml?id='+id,
			method: "post",
			callback:function(resultMsg){
				//$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.reload(true);
				//})
			}
		})
	})

}


function check(e){
	var str = '<div>您确定要审核吗？</div>';
	$.message_confirm(str,function() {
		Mask();
		ajaxSubmit(e,{
			url: '/product/product_price_head/check.jhtml?ids=${productPriceHead.id}',
			method: "post",
			failCallback:function(resultMsg){				
	        	// 访问地址失败，或发生异常没有正常返回
	        	messageAlert(resultMsg);
	            UnMask();
	        },
			callback:function(resultMsg){
				location.reload(true);
			}
		});
	});
}
function check_wf(e){
	var $this = $(e);
	var $form = $("#inputForm");
	if($form.valid()){
			var url="/product/product_price_head/check_wf.jhtml";
			var data = $form.serialize();
			
			ajaxSubmit(e,{
				 url: '/wf/wf_obj_config/get_config.jhtml?obj_type_id=49&objid=${productPriceHead.id}',
				 method: "post",
				 callback:function(resultMsg){
				 	var rows = resultMsg.objx;
				 	if(rows.length==1){
				 		data = data+'&objConfId='+rows[0].id;
				 		ajaxSubmit('',{
							 url: url,
							 method: "post",
							 data: data,
							 callback:function(resultMsg){
								$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
									reflush_wf();
								});
							 }
						})
				 	}else{
				 		var str = '';
					 	for(var i=0;i<rows.length;i++){
					 		var row = rows[i];
					 		str+='<option value="'+row.id+'">'+row.wf_temp_name+'</option>';
					 	}
					 	var content = '<table class="input input-edit" style="width:100%">'
								+'<tbody><tr><th>流程模版</th>'
								+'<td>'
									+'<select class="text" id="objConfId">'
										+str
									+'</select>'
								+'</td>'
							+'</tr></tbody></table>';
						var $dialog_check = $.dialog({
							title:"会员价审核",
							height:'135',
							content: content,
							onOk:function(){
								var objConfId = $("#objConfId").val();
								if(objConfId=='' || objConfId == null){
									$.message_alert("请选择会员价模版");
									return false;
								}
								data = data+'&objConfId='+objConfId;
								
								ajaxSubmit($this,{
									 url: url,
									 method: "post",
									 data: data,
									 callback:function(resultMsg){
										$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
											reflush_wf();
										});
									 }
								})
								
							}
						});	
				 	}
				 	
					var h = 150;
					$dialog_check.css("top",h+"px");
				 }
			
		});
	}
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("查看价格表")}
	</div>
	<form id="inputForm" action="/product/product_price_head/update.jhtml" method="post" type="ajax" validate-type="validate" enctype="multipart/form-data">
		<input type="hidden" name="id" id="pid" value="${productPriceHead.id}" />
		<input type="hidden" name="isSubmit" id="isSubmit" value="" />
		 <div class="tabContent order-info">
		<table class="input input-edit">
			<tr>
				<th>
					${message("价格单号")}:
				</th>
				<td>
					${productPriceHead.sn}
				</td>
				<th>
					${message("单据状态")}:
				</th>
				<td>
					[#if productPriceHead.status == "0"]${message("已保存")}[/#if]
        			[#if productPriceHead.status == "1"]${message("处理中")}[/#if]
        			[#if productPriceHead.status == "2"]${message("已生效")}[/#if]
        			[#if productPriceHead.status == "3"]${message("已失效")}[/#if]
					[#if productPriceHead.status == "4"]${message("产品部结算价已维护")}[/#if]
					[#if productPriceHead.status == "5"]${message("平台结算价已维护")}[/#if]
					[#if productPriceHead.status == "6"]${message("经销商价已维护")}[/#if]
				</td>
				<th>
					${message("流程状态")}:
				</th>
				<td>
					[#if productPriceHead.wfState == "0"]${message("未启动")}[/#if]
        			[#if productPriceHead.wfState == "1"]${message("审核中")}[/#if]
        			[#if productPriceHead.wfState == "2"]${message("已完成")}[/#if]
        			[#if productPriceHead.wfState == "3"]${message("驳回")}[/#if]
				</td>
				<th>
					${message("创建日期")}:
				</th>
				<td>
					${productPriceHead.createDate?string("yyyy-MM-dd HH:mm:ss")}
				</td>
			</tr>
			<tr>
				<th>
					${message("价格类型")}:
				</th>
				<td>
					<span class="search" style="position:relative">
						<input type="hidden" name="memberRankId" class="text memberRankId"  value="${productPriceHead.memberRank.id}" btn-fun="clear" />
						<input type="text" name="memberRankName" class="text memberRankName" maxlength="200" onkeyup="clearSelect(this)"  value="${productPriceHead.memberRank.name}"/>
						<input type="button" class="iconSearch" value="" id="selectMemberRank">
					</span>
				</td>
				 [#-- <td>
						<select class="text" name="memberRankId">
							[#list memberRankList as memberRank]
							<option value="${memberRank.id}" [#if productPriceHead.memberRank.id ==memberRank.id]selected[/#if]>${memberRank.name}</option>
							[/#list]
						</select>
					</td>--]
				<th>${message("机构")}:</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value="${productPriceHead.saleOrg.id}"/>
					<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" value="${productPriceHead.saleOrg.name}" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectSaleOrg">
					</span>
				</td>
				 <th>
					${message("开始时间")}:
				</th>
				<td id="start">
					<input type="text" class="text start" id="startdate" name="startDate" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',maxDate: '#F{$dp.$D(\'enddate\')}'});" value="${productPriceHead.startDate}" btn-fun="clear"/>
				</td>
				
				<th>
					${message("结束时间")}:
				</th>
				<td id="end">
					<input type="text" class="text end"  id="enddate" name="endDate" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',minDate: '#F{$dp.$D(\'startdate\')}'});" value="${productPriceHead.endDate}"  btn-fun="clear"/>
				</td>
			</tr>
			
		</table>
		<table class="input input-edit" style="width:100%;margin-top:5px;">
			<tr class="border-L1">
				<th> ${message("价格明细")}:
				</th>
				<td colspan="7">
					[#if productPriceHead.status=="0"]
					   <!--  <a href="javascript:;" id="addProductCategory" class="button">选择产品系列</a> -->
						<a href="javascript:;" id="addProduct" class="button">选择产品</a>
					[/#if]
				</td>
			</tr>
			<tr class="border-L1">
				<td colspan="8">
					<div>
						<table id="table-m1"></table>
					</div>
				</td>
			</tr>
			<tr class="border-L2">
				<th>${message("全链路信息")}:</th>
				<td colspan="7"></td>
			</tr>
			<tr class="border-L2">
				<td colspan="8">
					<table id="table-full"></table>
				</td>
			</tr>
		</table>
		</div>
	<div class="fixed-top">
		<a href="javascript:void(0);" onclick="add(2,'/product/product_price_head/add/${code}.jhtml')" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>${message("新增")}</a>
		[#if productPriceHead.status ==0 || productPriceHead.status == 1]
			<input type="submit" id="submit_button" class="button sureButton" value="${message("保存")}">
			<a id="shengheButton" class="iconButton" onclick="check(this)" >
				<span class="ico-shengheIcon">&nbsp;</span>
				${message("审核")}
			</a>
			<a href="javascript:void(0);" class="button cancleButton" onclick="cancel(this)">
				${message("失效")}
			</a>
		[/#if]
		<input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
	</div>
	</form>
	<div id="wf_area" style="width:100%"></div>
</body>
</html>