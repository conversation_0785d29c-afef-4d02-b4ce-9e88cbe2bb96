<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("报表数据信息")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/layout.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
function add(){
	parent.change_tab(0,'/export/reportData/add/${code}.jhtml');
}
function edit(id){
	parent.change_tab(0,'/export/reportData/edit/${code}.jhtml?isEdit=${isEdit}&isStore=${isStore}&id='+id);
}
function reportDataCancel(e){

    var param = $mmGrid.serializeSelectedIds();
    if(param == ''){
        $.message_alert("请选择报表作废行");
        return;
	}

    ajaxSubmit(e,{
        url:'cancel.jhtml?'+ param,
        method:"post",
        //data:data,
        isConfirm:true,
        confirmText : '您确定要作废吗？',
        callback:function(resultMsg){
            $.message_timer(resultMsg.type,resultMsg.content,5000,function(){
                location.reload(true);
            })
        }

    });

}

	$().ready(function() {
		
		/**初始化多选下拉框*/
		initMultipleSelect();
		
		var cols = [
		{ title:'${message("客户名称")}', name:'customer_name' ,width:100, align:'center', renderer:function(val,item,rowIndex){
			return '<a href="javascript:void(0);" id="'+item.id+'" onClick="edit('+item.id+')" class="red">'+item.customer_name+'</a>';
		}},
		{ title:'${message("出库单编号")}', name:'delivery_order_number' ,width:100, align:'center'},
		{ title:'${message("仓库名称")}', name:'warehouse_name' ,width:100, align:'center'},
		{ title:'${message("出仓类别")}', name:'outbound_category' ,width:100, align:'center'},
		{ title:'${message("出库日期")}', name:'outbound_date' ,width:100, align:'center', renderer:function(val){
                if(val !=null && val.length>10){
                    var str = val;
                    return str.substring(0,10);
                }
            }},
		{ title:'${message("所属部门")}', name:'department' ,width:100, align:'center'},
		{ title:'${message("开单人")}', name:'single_person' ,width:100, align:'center'},
		{ title:'${message("销售跟单人")}', name:'merchandiser' ,width:100, align:'center'},
		{ title:'${message("第一会计审核人")}', name:'first_auditor' ,width:100, align:'center'},
		{ title:'${message("仓库理货人")}', name:'warehouse_keeper' ,width:100, align:'center'},
		{ title:'${message("货物编号")}', name:'goods_number' ,width:100, align:'center'},
		{ title:'${message("木种名称")}', name:'wood_name' ,width:100, align:'center'},
		{ title:'${message("规格")}', name:'specification' ,width:100, align:'center'},
		{ title:'${message("支数")}', name:'support_several' ,width:100, align:'center'},
		{ title:'${message("平方数")}', name:'square_number' ,width:100, align:'center'},
		{ title:'${message("基准价")}', name:'standard_price' ,width:100, align:'center'},
		{ title:'${message("优惠价差")}', name:'preferential_price' ,width:100, align:'center'},
		{ title:'${message("单价")}', name:'unit_price' ,width:100, align:'center'},
		{ title:'${message("金额")}', name:'amount' ,width:100, align:'center'},
		{ title:'${message("箱数")}', name:'carton_numbers' ,width:100, align:'center'},
        { title:'${message("含水率")}', name:'moisture_content' ,width:100, align:'center'},
        { title:'${message("色号")}', name:'colour_number' ,width:100, align:'center'},
        { title:'${message("新旧标识")}', name:'new_old_logo' ,width:100, align:'center'},
		{ title:'${message("厚度mm")}', name:'thickness' ,width:100, align:'center'},
		{ title:'${message("宽度mm")}', name:'breadth' ,width:100, align:'center'},
		{ title:'${message("长度mm")}', name:'length' ,width:100, align:'center'},
		{ title:'${message("货物类别")}', name:'goods_category' ,width:100, align:'center'},
		{ title:'${message("分析项目")}', name:'analysis' ,width:100, align:'center'},
		{ title:'${message("开单人")}', name:'single_person' ,width:100, align:'center'},
		{ title:'${message("销售跟单人")}', name:'analysis' ,width:100, align:'center'},
        { title:'${message("第二会计审核人")}', name:'second_auditor' ,width:100, align:'center'},
        { title:'${message("门卫")}', name:'doorkeeper' ,width:100, align:'center'},
        { title:'${message("核对单号")}', name:'check_number' ,width:100, align:'center'},
        { title:'${message("车号")}', name:'wagon_number' ,width:100, align:'center'},
        { title:'${message("柜号")}', name:'container_number' ,width:100, align:'center'},
        { title:'${message("封号")}', name:'seal_number' ,width:100, align:'center'},
		{ title:'${message("客户地址")}', name:'customer_address' ,width:100, align:'center'},
		{ title:'${message("电话号码")}', name:'phone_number' ,width:100, align:'center'},
		{ title:'${message("货物所属部门")}', name:'cargo_department' ,width:100, align:'center'},
        { title:'${message("预留1")}', name:'reserved1' ,width:100, align:'center'},
        { title:'${message("预留2")}', name:'reserved2' ,width:100, align:'center'},
        { title:'${message("预留3")}', name:'reserved3' ,width:100, align:'center'},
        { title:'${message("预留4")}', name:'reserved4' ,width:100, align:'center'},
        { title:'${message("预留5")}', name:'reserved5' ,width:100, align:'center'},
        { title:'${message("预留6")}', name:'reserved6' ,width:100, align:'center'},
        { title:'${message("预留7")}', name:'reserved7' ,width:100, align:'center'},
        { title:'${message("预留8")}', name:'reserved8' ,width:100, align:'center'},
        { title:'${message("预留9")}', name:'reserved9' ,width:100, align:'center'},
        { title:'${message("预留10")}', name:'reserved10' ,width:100, align:'center'},
        { title:'${message("预留11")}', name:'reserved11' ,width:100, align:'center'},
        { title:'${message("预留12")}', name:'reserved12' ,width:100, align:'center'},
        { title:'${message("预留13")}', name:'reserved13' ,width:100, align:'center'},
        { title:'${message("预留14")}', name:'reserved14' ,width:100, align:'center'},
        { title:'${message("预留15")}', name:'reserved15' ,width:100, align:'center'},
		{ title:'${message("创建日期")}', name:'create_date' ,width:120, align:'center'},
	];
	
	$mmGrid = $('#table-m1').mmGrid({
		autoLoad:true,
		fullWidthRows:true,
        cols: cols,
        url: '/export/reportData/list_data.jhtml',
	    params:function(){
        	return $("#listForm").serializeObject();
        },
	    plugins : [
            $('#paginator').mmPaginator()
        ]
    });

});

function customer_import(e){
	excel_import(e,{
		title:'${message("报表数据导入")}',
		url:'/export/reportData/reportData_excel.jhtml',
		template:'/resources/template/reportData/ReportData.xls',
		callback:function(){
			$("#searchBtn").click();
		}
	});
}

//条件导出		    
function segmentedExport(e){
    var needConditions = false;
    var page_url = '/export/reportData/to_condition_export.jhtml';//分页导出统计页面
    var url = '/export/reportData/reportData_conditionExport.jhtml';//导出的方法
    conditions_export(e,{needConditions:needConditions,page_url:page_url,url:url});
}

function conditions_export(e,options){
    if($(e).hasClass("disabled")){
        return false;
    }
    var settings ={
        title:'条件查询分页导出',
        needConditions:true,
        page_url:null,
        url:null,
    }
    $.extend(settings, options);
    if(settings.page_url==null || settings.url==null){
        return false;
    }

    var $conditions = $("#search-content").find("[name]");
    if(settings.needConditions){
        var isEmpty = true;
        for(var i=0;i<$conditions.length;i++){
            var $condition = $conditions.eq(i);
            if($condition[0].type=="checkbox"){
                if($condition.prop("checked") && $condition.val() != ""){
                    isEmpty = false;
                    break;
                }
            }else{
                if($conditions.eq(i).val() != ""){
                    isEmpty = false;
                    break;
                }
            }

        }
        if(isEmpty){
            $.message_alert('至少有一个搜索条件不为空');
            return false;
        }
    }

    $(e).addClass("disabled");
    var searchContent = $conditions.serialize();
    $.ajax({
        url:settings.page_url,
        type:"post",
        data:searchContent,
        dataType:"json",
        cache:false,
        success:function(lists){
            var exportTableId = "exportTable" + (new Date()).valueOf() + Math.floor(Math.random() * 1000000);
            var total=lists[lists.length-1].data.split("-")[1];
            var table='<table id="'+exportTableId+'" class="input exportTable" style="width:100%;border:1px solid #e9e9e9;margin-top:0px;margin-bottom:0px;" collapse><tr class="title"><th width="50%">分页</th><th width="50%" style="text-align:center">操作</th></tr>';
            for(var i=0;i<lists.length;i++){
                var  d= lists[i];
                var n=parseInt(i+1);
                table += '<tr><td>第'+n+'页('+d.data+')</td><td style="text-align:center;"><input type="button" value="导出此页" class="button exportBtn" page="'+n+'"/></td></tr>';
            }
            table+='</table>';
            var content='<div style="padding:10px;height:355px;overflow-y:auto;"><p>共查询出<span>'+total+'</span>条数据，每次导出页最多10000条，请点击你需要导出的分页数据！</p><div class="tableBox">'+table+'</div></div>';
            $.dialog({
                title:'条件查询分页导出',
                content:content,
                width:520,
                height:460,
                ok:null,
                cancel:'返回',
                onShow:function(){
                    $("#"+exportTableId+" .exportBtn").click(function(){
                        $(this).addClass("disabled").attr("disabled",true);
                        var page = $(this).attr("page");
                        var param = searchContent+"&page="+page;
                        var url = "";
                        if(settings.url.indexOf("?")>0){
                            url = settings.url+"&"+param;
                        }else{
                            url = settings.url+"?"+param;
                        }

                        location.href=url;
                        $(this).attr("disabled",false);
                    });

                },
                onCancel:function(){
                    $(e).removeClass("disabled");
                },
                onClose:function(){
                    $(e).removeClass("disabled");
                }
            })
        }
    })

}

//选择导出
function exportExcel(t){
	var param = $mmGrid.serializeSelectedIds();//参数
	var tip = '${message("请选择导出的报表数据！")}';//提示
	var url = '/export/reportData/selected_reportData.jhtml';//导出的方法
  	select_export(t,{tip:tip, param:param, url:url});
}

</script>
</head>
<body >
	<form id="listForm" action="/export/reportData/list/${code}.jhtml" method="get">
        [#--<input type="hidden" name="id" id="pid" value="${reportData.id}" />--]
		<div class="bar">
		<div class="buttonWrap">
			<div class="flag-wrap flagImp-wrap">
				<a href="javascript:void(0);" class="iconButton" id="export1Button">
					<span class="impIcon">&nbsp;</span>导入导出
				</a>

				<ul class="flag-list">
					<li><a href="javascript:void(0)" onclick="customer_import(this)"><i class="flag-imp01"></i>${message("导入")}</a></li>
					<li><a href="javascript:void(0)" onclick="exportExcel(this)"><i class="flag-imp02"></i>${message("选择导出")}</a></li>
					<li><a href="javascript:void(0)" onclick="segmentedExport(this)"><i class="flag-imp02"></i>${message("条件导出")}</a></li>
				</ul>
			</div>
 			<a href="javascript:add();" class="iconButton" id="addButton"><span class="addIcon">&nbsp;</span>
                ${message("新增")}
            </a>
            <a href="javascript:void(0);" class="iconButton" id="fenpeiButton" onclick="reportDataCancel(this)">
                <span class="fenpeiIcon">&nbsp;</span>作废
            </a>

		</div>
			<div class="search-btn">
				<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
			</div>
			<div id="searchDiv">
		        <div id="search-content" >

                    <dl>
                        <dt><p>${message("客户名称")}:</p></dt>
                        <dd>
                            <input class="text" maxlength="200" type="text" name="customerName" value=""  btn-fun="clear">
                        </dd>
                    </dl>
                    <dl>
                        <dt><p>${message("出库单编号")}:</p></dt>
                        <dd>
                            <input class="text" maxlength="200" type="text" name="deliveryOrderNumber" value=""  btn-fun="clear">
                        </dd>
                    </dl>
					<dl>
						<dt><p>${message("仓库名称")}:</p></dt>
						<dd>
							<input class="text" maxlength="200" type="text" name="warehouseName" value=""  btn-fun="clear">
						</dd>
					</dl>
					<dl>
						<dt><p>${message("出仓类别")}:</p></dt>
						<dd>
							<input class="text" maxlength="200" type="text" name="outboundCategory" value=""  btn-fun="clear">
						</dd>
					</dl>
					<dl>
						<dt><p>${message("出库日期")}:</p></dt>
                        <dd class="date-wrap">
						<input id="outboundDateStart" name="outboundDateStart" class="text" value=""
							   onClick="WdatePicker({dateFmt: 'yyyy-MM-dd'})"
							   type="text" btn-fun="clear"/>
                        </dd>

					</dl>
                    <dt><p>--</p></dt>
                    <dl>
                        <dd class="date-wrap">
                        <input id="outboundDateEnd" name="outboundDateEnd" class="text" value=""
                               onClick="WdatePicker({dateFmt: 'yyyy-MM-dd'})"
                               type="text" btn-fun="clear"/>
                        </dd>
                    </dl>
					<dl>
						<dt><p>${message("所属部门")}:</p></dt>
						<dd>
							<input class="text" maxlength="200" type="text" name="department" value=""  btn-fun="clear">
						</dd>
					</dl>
                    <dl>
                        <dt><p>${message("货物编号")}:</p></dt>
                        <dd>
                            <input class="text" maxlength="200" type="text" name="goodsNumber" value=""  btn-fun="clear">
                        </dd>
                    </dl>
                    <dl>
                        <dt><p>${message("木种名称")}:</p></dt>
                        <dd>
                            <input class="text" maxlength="200" type="text" name="woodName" value=""  btn-fun="clear">
                        </dd>
                    </dl>
                    <dl>
                        <dt><p>${message("货物类别")}:</p></dt>
                        <dd>
                            <input class="text" maxlength="200" type="text" name="goodsCategory" value=""  btn-fun="clear">
                        </dd>
                    </dl>
                    <dl>
                        <dt><p>${message("分析项目")}:</p></dt>
                        <dd>
                            <input class="text" maxlength="200" type="text" name="analysis" value=""  btn-fun="clear">
                        </dd>
                    </dl>
                    <dl>
                        <dt><p>${message("开单人")}:</p></dt>
                        <dd>
                            <input class="text" maxlength="200" type="text" name="singlePerson" value=""  btn-fun="clear">
                        </dd>
                    </dl>
                    <dl>
                        <dt><p>${message("销售跟单人")}:</p></dt>
                        <dd>
                            <input class="text" maxlength="200" type="text" name="merchandiser" value=""  btn-fun="clear">
                        </dd>
                    </dl>
                    <dl>
                        <dt><p>${message("第一会计审核人")}:</p></dt>
                        <dd>
                            <input class="text" maxlength="200" type="text" name="firstAuditor" value=""  btn-fun="clear">
                        </dd>
                    </dl>
                    <dl>
                        <dt><p>${message("仓库理货人")}:</p></dt>
                        <dd>
                            <input class="text" maxlength="200" type="text" name="warehouseKeeper" value=""  btn-fun="clear">
                        </dd>
                    </dl>

		        </div>
			</div>
			
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
	        <div id="body-paginator" style="text-align:left;">
	            <div id="paginator"></div>
	        </div>
		</div>
	</form>
</body>
</html>