<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("报表数据信息")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<style>
.upload-list {width:82px;display: inline-block;padding-top:0px;margin-right:10px;}
</style>
<script type="text/javascript">
function editPrice(t,e){
	extractNumber(t,2,true,e)
}

function isNum() {
    if ($("#phoneNumber").val() != '') {
        var reg = "^[0-9]*$";
        var pattern = new RegExp(reg);
        if (!pattern.test($("#phoneNumber").val())){
            $.message_alert("电话号码必须是数字类型");
            $("#phoneNumber").val("");
        }
    }
}

$().ready(function() {

    // 表单验证
    $("#inputForm").validate({
        rules: {
            customerName: "required",
            deliveryOrderNumber: "required",
            outboundDate: "required",
            firstAuditor: "required",
        },
    });

    $("form").bindAttribute({
        isConfirm: true,
        callback: function (resultMsg) {
            $.message_timer(resultMsg.type, resultMsg.content, 1000, function () {
                location.href = '/export/reportData/edit/${code}.jhtml?id=' + resultMsg.objx;
            })
        }
    });
})

</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增报表数据信息")}
	</div>
	<form id="inputForm" action="/export/reportData/save.jhtml" method="post" type="ajax" validate-type="validate">
		<ul id="tab" class="tab">
			<li>
				<input type="button" value="${message("基本信息")}" />
			</li>
		</ul>
		<div class="tabContent">
		<table class="input input-edit">
			<tr>
                <th>
                    <span class="requiredField">*</span>${message("客户名称")}:
                </th>
                <td>
                    <input type="text" name="customerName" class="text" value="" btn-fun="clear" maxlength="200" />
                </td>
                <th>
                    <span class="requiredField">*</span>${message("出库单编号")}:
                </th>
                <td>
                    <input type="text" name="deliveryOrderNumber" class="text " maxlength="200"  btn-fun="clear" />
                </td>
			    <th>
					${message("仓库名称")}:
				</th>
				<td>
					<input type="text" name="warehouseName" class="text " maxlength="200"  btn-fun="clear" />
				</td>
				<th>
					${message("出仓类别")}:
				</th>
				<td>
					<input type="text" name="outboundCategory" class="text" value="" btn-fun="clear" maxlength="200" />
				</td>
			</tr>

            <tr>
                <th>
                    <span class="requiredField">*</span>${message("出库日期")}:
                </th>
                <td>
                    <input type="text" name="outboundDate" class="text" value="" btn-fun="clear" onClick="WdatePicker({dateFmt: 'yyyy-MM-dd'})" />
                </td>
                <th>
				${message("所属部门")}:
                </th>
                <td>
                    <input type="text" name="department" class="text " maxlength="200"  btn-fun="clear" />
                </td>
                <th>
				${message("货物编号")}:
                </th>
                <td>
                    <input type="text" name="goodsNumber" class="text" value="" btn-fun="clear" maxlength="200" />
                </td>
                <th>
				${message("木种名称")}:
                </th>
                <td>
                    <input type="text" name="woodName" class="text " maxlength="200"  btn-fun="clear" />
                </td>
            </tr>

            <tr>
                <th>
				${message("规格")}:
                </th>
                <td>
                    <input type="text" name="specification" class="text " maxlength="200"  btn-fun="clear" />
                </td>
                <th>
				${message("等级")}:
                </th>
                <td>
                    <input type="text" name="grade" class="text" value="" btn-fun="clear" maxlength="200" />
                </td>
                <th>
				${message("支数")}:
                </th>
                <td>
                    <div class="nums-input ov">
                        <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"/>
                        <input type="text"  class="t"  name="supportSeveral" value=""  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" />
                        <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"/>
                    </div>
                </td>
                <th>
				${message("平方数")}:
                </th>
                <td>
                    <div class="nums-input ov">
                        <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"/>
                        <input type="text"  class="t"  name="squareNumber" value=""  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" />
                        <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"/>
                    </div>
                </td>
            </tr>

            <tr>
                <th>
				${message("基准价")}:
                </th>
                <td>
                    <div class="nums-input ov">
                        <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"/>
                        <input type="text"  class="t"  name="standardPrice" value=""  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" />
                        <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"/>
                    </div>
                </td>
                <th>
				${message("优惠价差")}:
                </th>
                <td>
                    <div class="nums-input ov">
                        <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"/>
                        <input type="text"  class="t"  name="preferentialPrice" value=""  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" />
                        <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"/>
                    </div>
                </td>
                <th>
				${message("单价")}:
                </th>
                <td>
                    <div class="nums-input ov">
                        <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"/>
                        <input type="text"  class="t"  name="unitPrice" value=""  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" />
                        <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"/>
                    </div>
                </td>
                <th>
				${message("金额")}:
                </th>
                <td>
                    <div class="nums-input ov">
                        <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"/>
                        <input type="text"  class="t"  name="amount" value=""  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" />
                        <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"/>
                    </div>
                </td>
			</tr>

            <tr>
                <th>
				${message("箱数")}:
                </th>
                <td>
                    <div class="nums-input ov">
                        <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"/>
                        <input type="text"  class="t"  name="cartonNumbers" value=""  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" />
                        <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"/>
                    </div>
                </td>
                <th>
				${message("含水率")}:
                </th>
                <td>
                    <input type="text" name="moistureContent" class="text" value="" btn-fun="clear" maxlength="200" />
                </td>
                <th>
				${message("色号")}:
                </th>
                <td>
                    <input type="text" name="colourNumber" class="text" value="" btn-fun="clear" maxlength="200" />
                </td>
                <th>
				${message("新旧标识")}:
                </th>
                <td>
                    <input type="text" name="newOldLogo" class="text " maxlength="200"  btn-fun="clear" />
                </td>
            </tr>

            <tr>
                <th>
				${message("厚度mm")}:
                </th>
                <td>
                    <div class="nums-input ov">
                        <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"/>
                        <input type="text"  class="t"  name="thickness" value=""  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" />
                        <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"/>
                    </div>
                </td>
                <th>
				${message("宽度mm")}:
                </th>
                <td>
                    <div class="nums-input ov">
                        <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"/>
                        <input type="text"  class="t"  name="breadth" value=""  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" />
                        <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"/>
                    </div>
                </td>
                <th>
				${message("长度mm")}:
                </th>
                <td>
                    <div class="nums-input ov">
                        <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"/>
                        <input type="text"  class="t"  name="length" value=""  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" />
                        <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"/>
                    </div>
                </td>
                <th>
				${message("货物类别")}:
                </th>
                <td>
                    <input type="text" name="goodsCategory" class="text " maxlength="200"  btn-fun="clear" />
                </td>
            </tr>

            <tr>
                <th>
				${message("分析项目")}:
                </th>
                <td>
                    <input type="text" name="analysis" class="text " maxlength="200"  btn-fun="clear" />
                </td>
                <th>
				${message("开单人")}:
                </th>
                <td>
                    <input type="text" name="singlePerson" class="text" value="" btn-fun="clear" maxlength="200" />
                </td>
                <th>
				${message("销售跟单人")}:
                </th>
                <td>
                    <input type="text" name="merchandiser" class="text" value="" btn-fun="clear" maxlength="200" />
                </td>
                <th>
                    <span class="requiredField">*</span>${message("第一会计审核人")}:
                </th>
                <td>
                    <input type="text" name="firstAuditor" class="text " maxlength="200"  btn-fun="clear" />
                </td>
            </tr>

            <tr>
                <th>
				${message("第二会计审核人")}:
                </th>
                <td>
                    <input type="text" name="secondAuditor" class="text" value="" btn-fun="clear" maxlength="200" />
                </td>
                <th>
				${message("仓库理货人")}:
                </th>
                <td>
                    <input type="text" name="warehouseKeeper" class="text " maxlength="200"  btn-fun="clear" />
                </td>
                <th>
				${message("门卫")}:
                </th>
                <td>
                    <input type="text" name="doorkeeper" class="text" value="" btn-fun="clear" maxlength="200" />
                </td>
                <th>
				${message("核对单号")}:
                </th>
                <td>
                    <input type="text" name="checkNumber" class="text " maxlength="200"  btn-fun="clear" />
                </td>
            </tr>

            <tr>
                <th>
				${message("车号")}:
                </th>
                <td>
                    <input type="text" name="wagonNumber" class="text" value="" btn-fun="clear" maxlength="200" />
                </td>
                <th>
				${message("柜号")}:
                </th>
                <td>
                    <input type="text" name="containerNumber" class="text " maxlength="200"  btn-fun="clear" />
                </td>
                <th>
				${message("封号")}:
                </th>
                <td>
                    <input type="text" name="sealNumber" class="text" value="" btn-fun="clear" maxlength="200" />
                </td>
                <th>
				${message("客户地址")}:
                </th>
                <td>
                    <input type="text" name="customerAddress" class="text " maxlength="200"  btn-fun="clear" />
                </td>
            </tr>

            <tr>
                <th>
				${message("电话号码")}:
                </th>
                <td>
                    <input type="text" <#--onblur="isNum();"--> id="phoneNumber" name="phoneNumber" class="text" value="" btn-fun="clear"
                           maxlength="200" />
                </td>
                <th>
				${message("货物所属部门")}:
                </th>
                <td>
                    <input type="text" name="cargoDepartment" class="text " maxlength="200"  btn-fun="clear" />
                </td>
                <th>
                ${message("预留1")}:
                </th>
                <td>
                    <input type="text" name="reserved1" class="text " maxlength="200"  btn-fun="clear" />
                </td>
                <th>
                ${message("预留2")}:
                </th>
                <td>
                    <input type="text" name="reserved2" class="text " maxlength="200"  btn-fun="clear" />
                </td>
            </tr>

            <tr>
                <th>
                ${message("预留3")}:
                </th>
                <td>
                    <input type="text" name="reserved3" class="text" value="" btn-fun="clear" maxlength="200" />
                </td>
                <th>
                ${message("预留4")}:
                </th>
                <td>
                    <input type="text" name="reserved4" class="text " maxlength="200"  btn-fun="clear" />
                </td>
                <th>
                ${message("预留5")}:
                </th>
                <td>
                    <input type="text" name="reserved5" class="text " maxlength="200"  btn-fun="clear" />
                </td>
                <th>
                ${message("预留6")}:
                </th>
                <td>
                    <input type="text" name="reserved6" class="text " maxlength="200"  btn-fun="clear" />
                </td>
            </tr>

            <tr>
                <th>
                ${message("预留7")}:
                </th>
                <td>
                    <input type="text" name="reserved7" class="text" value="" btn-fun="clear" maxlength="200" />
                </td>
                <th>
                ${message("预留8")}:
                </th>
                <td>
                    <input type="text" name="reserved8" class="text " maxlength="200"  btn-fun="clear" />
                </td>
                <th>
                ${message("预留9")}:
                </th>
                <td>
                    <input type="text" name="reserved9" class="text " maxlength="200"  btn-fun="clear" />
                </td>
                <th>
                ${message("预留10")}:
                </th>
                <td>
                    <input type="text" name="reserved10" class="text " maxlength="200"  btn-fun="clear" />
                </td>
            </tr>

            <tr>
                <th>
                ${message("预留11")}:
                </th>
                <td>
                    <input type="text" name="reserved11" class="text" value="" btn-fun="clear" maxlength="200" />
                </td>
                <th>
                ${message("预留12")}:
                </th>
                <td>
                    <input type="text" name="reserved12" class="text " maxlength="200"  btn-fun="clear" />
                </td>
                <th>
                ${message("预留13")}:
                </th>
                <td>
                    <input type="text" name="reserved13" class="text " maxlength="200"  btn-fun="clear" />
                </td>
                <th>
                ${message("预留14")}:
                </th>
                <td>
                    <input type="text" name="reserved14" class="text " maxlength="200"  btn-fun="clear" />
                </td>
            </tr>

            <tr>
                <th>
                ${message("预留15")}:
                </th>
                <td>
                    <input type="text" name="reserved15" class="text" value="" btn-fun="clear" maxlength="200" />
                </td>
                <th></th><td></td>
                <th></th><td></td>
                <th></th><td></td>
            </tr>

            <tr>
                <th>
				${message("备注")}:
                </th>
                <td colspan="7">
                    <textarea class="text" name="remark" maxlength="200"></textarea>
                </td>
            </tr>
		</table>

		<div class="fixed-top">
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}"/>
		</div>
	</form>
</body>
</html>