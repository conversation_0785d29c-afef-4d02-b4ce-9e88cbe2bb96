<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8" />
    <title>${message("报表数据信息")}</title>
    <link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
    <script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
    <script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
    <script type="text/javascript" src="/resources/js/base/dialog.js"></script>
    <script type="text/javascript" src="/resources/js/base/request.js"></script>
    <script type="text/javascript" src="/resources/js/base/global.js"></script>
    <script type="text/javascript" src="/resources/js/base/file.js"></script>
    <script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
    <link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>

    <style>
        .upload-list {width:82px;display: inline-block;padding-top:0px;margin-right:10px;}
    </style>
    <script type="text/javascript">
    function editQty(t,e,n){
        var rate = 3;
        if(n!=undefined)rate = n;
        extractNumber(t,rate,false,e)
    }

    function isNum() {
        if ($("#phoneNumber").val() != '') {
            var reg = "^[0-9]*$";
            var pattern = new RegExp(reg);
            if (!pattern.test($("#phoneNumber").val())){
                $.message_alert("电话号码必须是数字类型");
                $("#phoneNumber").val("");
            }
        }
    }

    $().ready(function() {

        // 表单验证
        $("#inputForm").validate({
            rules: {
                customerName: "required",
                deliveryOrderNumber: "required",
                outboundDate: "required",
                firstAuditor: "required",
            },
        });

        $("form").bindAttribute({
            isConfirm:true,
            callback: function(resultMsg){
                $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                    location.reload(true);
                });
            }
        });

        function save(e, flag){
            var $form = $("#inputForm");
            if($form.valid()){
                ajaxSubmit(e,{
                    url: '/export/reportData/update.jhtml',
                    data:$("#inputForm").serialize(),
                    method: "post",
                    isConfirm:true,
                    confirmText:str,
                    callback:function(resultMsg){
                        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                            location.href= 'b.jhtml?isStore='+isStore+'&id='+resultMsg.objx;
                        })
                    }
                })
            }
        }

    })


    </script>
</head>
<body>
<div class="pathh">
    &nbsp;${message("查看报表数据")}
</div>
<form id="inputForm" action="/export/reportData/update.jhtml" method="post" type="ajax" validate-type="validate">
    <input type="hidden" name="id" value="${reportData.id}" />
    <input type="hidden" name="isStore" id="isStore" value="${isStore }"/>
    <ul id="tab" class="tab">
        <li>
            <input type="button"  id="firstButton" value="${message("基本信息")}" />
        </li>
    </ul>
    <div class="tabContent">
        <table class="input input-edit">
            <colgroup></colgroup>

            <tr>
                <th>
                    <span class="requiredField">*</span>${message("客户名称")}:
                </th>
                <td>
                    <input type="text" name="customerName" class="text" value="${reportData.customerName}"
                           btn-fun="clear" maxlength="200" />
                </td>
                <th>
                    <span class="requiredField">*</span>${message("出库单编号")}:
                </th>
                <td>
                    <input type="text" name="deliveryOrderNumber" class="text " value="${reportData.deliveryOrderNumber}"  maxlength="200"  btn-fun="clear" />
                </td>
                <th>
				${message("仓库名称")}:
                </th>
                <td>
                    <input type="text" name="warehouseName" class="text " value="${reportData.warehouseName}" maxlength="200"  btn-fun="clear" />
                </td>
                <th>
				${message("出仓类别")}:
                </th>
                <td>
                    <input type="text" name="outboundCategory" class="text" value="${reportData.outboundCategory}" btn-fun="clear" maxlength="200" />
                </td>
            </tr>

            <tr>
                <th>
                    <span class="requiredField">*</span>${message("出库日期")}:
                </th>
                <td>
                    <input type="text" name="outboundDate" class="text" value="${reportData.outboundDate}" btn-fun="clear" onClick="WdatePicker({dateFmt: 'yyyy-MM-dd'})" />
                </td>
                <th>
				${message("所属部门")}:
                </th>
                <td>
                    <input type="text" name="department" value="${reportData.department}" class="text " maxlength="200"  btn-fun="clear" />
                </td>
                <th>
				${message("货物编号")}:
                </th>
                <td>
                    <input type="text" name="goodsNumber" value="${reportData.goodsNumber}" class="text" value="" btn-fun="clear" maxlength="200" />
                </td>
                <th>
				${message("木种名称")}:
                </th>
                <td>
                    <input type="text" name="woodName" value="${reportData.woodName}" class="text " maxlength="200"  btn-fun="clear" />
                </td>
            </tr>

            <tr>
                <th>
				${message("规格")}:
                </th>
                <td>
                    <input type="text" name="specification" value="${reportData.specification}" class="text " maxlength="200"  btn-fun="clear" />
                </td>
                <th>
				${message("等级")}:
                </th>
                <td>
                    <input type="text" name="grade" value="${reportData.grade}" class="text" value="" btn-fun="clear" maxlength="200" />
                </td>
                <th>
				${message("支数")}:
                </th>
                <td>
                    <div class="nums-input ov">
                        <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"/>
                        <input type="text"  class="t"  name="supportSeveral" value="${reportData.supportSeveral}"
                               oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" />
                        <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"/>
                    </div>
                </td>
                <th>
				${message("平方数")}:
                </th>
                <td>
                    <div class="nums-input ov">
                        <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"/>
                        <input type="text"  class="t"  name="squareNumber" value="${reportData.squareNumber}"  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" />
                        <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"/>
                    </div>
                </td>
            </tr>

            <tr>
                <th>
				${message("基准价")}:
                </th>
                <td>
                    <div class="nums-input ov">
                        <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"/>
                        <input type="text"  class="t"  name="standardPrice" value="${reportData.standardPrice}"  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" />
                        <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"/>
                    </div>
                </td>
                <th>
				${message("优惠价差")}:
                </th>
                <td>
                    <div class="nums-input ov">
                        <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"/>
                        <input type="text"  class="t"  name="preferentialPrice" value="${reportData.preferentialPrice}"  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" />
                        <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"/>
                    </div>
                </td>
                <th>
				${message("单价")}:
                </th>
                <td>
                    <div class="nums-input ov">
                        <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"/>
                        <input type="text"  class="t"  name="unitPrice" value="${reportData.unitPrice}"  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" />
                        <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"/>
                    </div>
                </td>
                <th>
				${message("金额")}:
                </th>
                <td>
                    <div class="nums-input ov">
                        <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"/>
                        <input type="text"  class="t"  name="amount" value="${reportData.amount}"  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" />
                        <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"/>
                    </div>
                </td>
            </tr>

            <tr>
                <th>
				${message("箱数")}:
                </th>
                <td>
                    <div class="nums-input ov">
                        <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"/>
                        <input type="text"  class="t"  name="cartonNumbers" value="${reportData.cartonNumbers}"  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" />
                        <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"/>
                    </div>
                </td>
                <th>
				${message("含水率")}:
                </th>
                <td>
                    <input type="text" name="moistureContent" class="text" value="${reportData.moistureContent}" btn-fun="clear" maxlength="200" />
                </td>
                <th>
				${message("色号")}:
                </th>
                <td>
                    <input type="text" name="colourNumber" class="text" value="${reportData.colourNumber}" btn-fun="clear" maxlength="200" />
                </td>
                <th>
				${message("新旧标识")}:
                </th>
                <td>
                    <input type="text" name="newOldLogo" class="text " value="${reportData.newOldLogo}" maxlength="200"  btn-fun="clear" />
                </td>
            </tr>

            <tr>
                <th>
				${message("厚度mm")}:
                </th>
                <td>
                    <div class="nums-input ov">
                        <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"/>
                        <input type="text"  class="t"  name="thickness" value="${reportData.thickness}"  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" />
                        <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"/>
                    </div>
                </td>
                <th>
				${message("宽度mm")}:
                </th>
                <td>
                    <div class="nums-input ov">
                        <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"/>
                        <input type="text"  class="t"  name="breadth" value="${reportData.breadth}"  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" />
                        <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"/>
                    </div>
                </td>
                <th>
				${message("长度mm")}:
                </th>
                <td>
                    <div class="nums-input ov">
                        <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)"/>
                        <input type="text"  class="t"  name="length" value="${reportData.length}"  oninput="editPrice (this,event)" onpropertychange="editPrice(this,event)" />
                        <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"/>
                    </div>
                </td>
                <th>
				${message("货物类别")}:
                </th>
                <td>
                    <input type="text" name="goodsCategory" class="text " value="${reportData.goodsCategory}" maxlength="200"  btn-fun="clear" />
                </td>
            </tr>

            <tr>
                <th>
				${message("分析项目")}:
                </th>
                <td>
                    <input type="text" name="analysis" class="text " value="${reportData.analysis}" maxlength="200"  btn-fun="clear" />
                </td>
                <th>
				${message("开单人")}:
                </th>
                <td>
                    <input type="text" name="singlePerson" class="text" value="${reportData.singlePerson}" btn-fun="clear" maxlength="200" />
                </td>
                <th>
				${message("销售跟单人")}:
                </th>
                <td>
                    <input type="text" name="merchandiser" class="text" value="${reportData.merchandiser}" btn-fun="clear" maxlength="200" />
                </td>
                <th>
                    <span class="requiredField">*</span>${message("第一会计审核人")}:
                </th>
                <td>
                    <input type="text" name="firstAuditor" class="text " value="${reportData.firstAuditor}" maxlength="200"  btn-fun="clear" />
                </td>
            </tr>

            <tr>
                <th>
				${message("第二会计审核人")}:
                </th>
                <td>
                    <input type="text" name="secondAuditor" class="text" value="${reportData.secondAuditor}" btn-fun="clear" maxlength="200" />
                </td>
                <th>
				${message("仓库理货人")}:
                </th>
                <td>
                    <input type="text" name="warehouseKeeper" class="text " value="${reportData.warehouseKeeper}" maxlength="200"  btn-fun="clear" />
                </td>
                <th>
				${message("门卫")}:
                </th>
                <td>
                    <input type="text" name="doorkeeper" class="text" value="${reportData.doorkeeper}" btn-fun="clear" maxlength="200" />
                </td>
                <th>
				${message("核对单号")}:
                </th>
                <td>
                    <input type="text" name="checkNumber" class="text " value="${reportData.checkNumber}" maxlength="200"  btn-fun="clear" />
                </td>
            </tr>

            <tr>
                <th>
				${message("车号")}:
                </th>
                <td>
                    <input type="text" name="wagonNumber" class="text" value="${reportData.wagonNumber}" btn-fun="clear" maxlength="200" />
                </td>
                <th>
				${message("柜号")}:
                </th>
                <td>
                    <input type="text" name="containerNumber" class="text " value="${reportData.containerNumber}" maxlength="200"  btn-fun="clear" />
                </td>
                <th>
				${message("封号")}:
                </th>
                <td>
                    <input type="text" name="sealNumber" class="text" value="${reportData.sealNumber}" btn-fun="clear" maxlength="200" />
                </td>
                <th>
				${message("客户地址")}:
                </th>
                <td>
                    <input type="text" name="customerAddress" class="text " value="${reportData.customerAddress}" maxlength="200"  btn-fun="clear" />
                </td>
            </tr>

            <tr>
                <th>
				${message("电话号码")}:
                </th>
                <td>
                    <input type="text" <#--onblur="isNum();"--> id="phoneNumber" name="phoneNumber" class="text" value="${reportData.phoneNumber}"
                           btn-fun="clear" maxlength="200" />
                </td>
                <th>
				${message("货物所属部门")}:
                </th>
                <td>
                    <input type="text" name="cargoDepartment" class="text " value="${reportData.cargoDepartment}" maxlength="200"  btn-fun="clear" />
                </td>
                <th>
                ${message("预留1")}:
                </th>
                <td>
                    <input type="text" name="reserved1" class="text " value="${reportData.reserved1}" maxlength="200"  btn-fun="clear" />
                </td>
                <th>
                ${message("预留2")}:
                </th>
                <td>
                    <input type="text" name="reserved2" class="text " value="${reportData.reserved2}" maxlength="200"  btn-fun="clear" />
                </td>
            </tr>

            <tr>
                <th>
                ${message("预留3")}:
                </th>
                <td>
                    <input type="text" name="reserved3" class="text" value="${reportData.reserved3}" btn-fun="clear" maxlength="200" />
                </td>
                <th>
                ${message("预留4")}:
                </th>
                <td>
                    <input type="text" name="reserved4" class="text " value="${reportData.reserved4}" maxlength="200"  btn-fun="clear" />
                </td>
                <th>
                ${message("预留5")}:
                </th>
                <td>
                    <input type="text" name="reserved5" class="text " value="${reportData.reserved5}" maxlength="200"  btn-fun="clear" />
                </td>
                <th>
                ${message("预留6")}:
                </th>
                <td>
                    <input type="text" name="reserved6" class="text " value="${reportData.reserved6}" maxlength="200"  btn-fun="clear" />
                </td>
            </tr>

            <tr>
                <th>
                ${message("预留7")}:
                </th>
                <td>
                    <input type="text" name="reserved7" class="text" value="${reportData.reserved7}" btn-fun="clear" maxlength="200" />
                </td>
                <th>
                ${message("预留8")}:
                </th>
                <td>
                    <input type="text" name="reserved8" class="text " value="${reportData.reserved8}" maxlength="200"  btn-fun="clear" />
                </td>
                <th>
                ${message("预留9")}:
                </th>
                <td>
                    <input type="text" name="reserved9" class="text " value="${reportData.reserved9}" maxlength="200"  btn-fun="clear" />
                </td>
                <th>
                ${message("预留10")}:
                </th>
                <td>
                    <input type="text" name="reserved10" class="text " value="${reportData.reserved10}" maxlength="200"  btn-fun="clear" />
                </td>
            </tr>

            <tr>
                <th>
                ${message("预留11")}:
                </th>
                <td>
                    <input type="text" name="reserved11" class="text" value="${reportData.reserved11}" btn-fun="clear" maxlength="200" />
                </td>
                <th>
                ${message("预留12")}:
                </th>
                <td>
                    <input type="text" name="reserved12" class="text " value="${reportData.reserved12}" maxlength="200"  btn-fun="clear" />
                </td>
                <th>
                ${message("预留13")}:
                </th>
                <td>
                    <input type="text" name="reserved13" class="text " value="${reportData.reserved13}" maxlength="200"  btn-fun="clear" />
                </td>
                <th>
                ${message("预留14")}:
                </th>
                <td>
                    <input type="text" name="reserved14" class="text " value="${reportData.reserved14}" maxlength="200"  btn-fun="clear" />
                </td>
            </tr>

            <tr>
                <th>
                ${message("预留15")}:
                </th>
                <td>
                    <input type="text" name="reserved15" class="text" value="${reportData.reserved15}" btn-fun="clear" maxlength="200" />
                </td>
                <th></th><td></td>
                <th></th><td></td>
                <th></th><td></td>
            </tr>

            <tr>
                <th>
				${message("备注")}:
                </th>
                <td colspan="7">
                    <textarea type="text" class="text" name="remark" maxlength="200">${reportData.remark}</textarea>
                </td>
            </tr>

        </table>
    </div>

    <div class="fixed-top">
        <input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
        <input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
    </div>
</form>
</body>
</html>