<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("用户列表")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript">
$().ready(function() {
	var h = $(window).height();
	$("iframe").height(h-$("#tab").outerHeight());
	
	var isEdit = "${isEdit}";
	
		var index = 1;
		var url ='/export/reportData/list/${code}.jhtml?menuId=${menuId}&isStore=${isStore}&isEdit='+isEdit;
		change_tab(index,url);
	
	
});

function change_tab(index,url,flag){
	$("#tab input").removeClass("current").eq(index).addClass("current");
	var $tabContent = $(".tabContent").hide();
	var $iframe = $tabContent.eq(index).show().find("iframe");
	if(flag==undefined){
		$iframe.attr("src",url);
	}else{
		 if($iframe.attr("src")=="" && url!=""){
			$iframe.attr("src",url);
		}
	}
}
</script>
</head>
<body style="overflow: hidden;">
	<div>
		<ul id="tab" class="tab tab-first">
			<li>
				<!-- 	            <input type="button" value="${message("常规")}" [#if isEdit==0]onclick="change_tab(0,'',1)"[#else]onclick="change_tab(0,'/member/store/add/${code}.jhtml',1)"[/#if]> -->
				<input type="button" value="${message("
				常规")}" onclick="change_tab(0,'',1)">
			</li>
			<li><input type="button" value="${message("
				列表")}" onclick="change_tab(1,'/export/reportData/list/${code}.jhtml?menuId=${menuId}&isEdit=${isEdit}',1)"></li>
		</ul>
		<div class="tabContent" style="display: none;">
			<iframe src="" style="width: 100%;"></iframe>
		</div>
		<div class="tabContent" style="display: none;">
			<iframe src="" style="width: 100%;"></iframe>
		</div>
	</div>
</body>
</html>