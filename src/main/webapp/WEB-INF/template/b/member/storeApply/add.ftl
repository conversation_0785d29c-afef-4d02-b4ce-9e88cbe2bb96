<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <title>${message("客户信息")}</title>
    <link href="/resources/css/common.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
    <script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
    <script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
    <script type="text/javascript" src="/resources/js/base/dialog.js"></script>
    <script type="text/javascript" src="/resources/js/base/request.js"></script>
    <script type="text/javascript" src="/resources/js/base/global.js"></script>
    <script type="text/javascript" src="/resources/js/base/file.js"></script>
    <script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
    <link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
    <style>
        label.fieldError {
            opacity: 1;
        }
    </style>
    <script type="text/javascript">
        $(function () {
            $('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete', 'off');
        });

        //这个是品牌保证金的回调函数
        function editCaution() {
            var needCautionPaid = $("#needCautionPaid").val();
            var realCautionPaid = $("#realCautionPaid").val();
            if (needCautionPaid != "" && realCautionPaid != "") {
                $("#unpaidCautionPaid").val(needCautionPaid - realCautionPaid);
            }
        }

        //这个是保证金的回调函数
        function editCautionMoney() {
            var $bInput = $("input.payable");
            $bInput.each(function () {
                var $tr = $(this).closest("tr");
                var payable = $(this).val();
                var paidIn = $tr.find(".paidIn").val();
                if (payable != "" && paidIn != "") {
                    $tr.find(".unpaid").val((payable - paidIn).toFixed(2));
                }
            });
        }

        function countCautionPaid() {
            var needCautionPaid = $("#needCautionPaid").val();
            var realCautionPaid = $("#realCautionPaid").val();
            if (isNaN(needCautionPaid)) {
                needCautionPaid = 0;
            }
            if (isNaN(realCautionPaid)) {
                realCautionPaid = 0;
            }
            $("#unpaidCautionPaid").val(accSub(needCautionPaid, realCautionPaid));
        }

        function editAccSub(t, e) {
            if (extractNumber(t, 0, true, e)) {
                countCautionPaid();
            }
        }

        function editQty(t, e, n) {
            var rate = 3;
            if (n != undefined) rate = n;
            extractNumber(t, rate, false, e)
        }

        // 没有小数位
        function editQtyEmpty(t, e, n) {
            var rate = 0;
            if (n != undefined) rate = n;
            extractNumber(t, rate, false, e)
        }

        $().ready(function () {
        
            var $inputForm = $("#inputForm");
            var $areaId = $("#areaId");//城市等级
            var $newAreaId = $("#newAreaId");
            var $bAreaId = $("#bAreaId");
            var $salesAreaId = $('#salesAreaId')
            var $typeSelect = $("#typeSelect");
            var $onlineShopTypeTr = $(".onlineShopTypeTr");
            var $bankCardLi = $("#bankCardLi");
            var $addRecviceAddress = $("#addRecviceAddress");
            var $addStoreContract = $("#addStoreContract");
            var $storeImage = $("#storeImage");
            
            // 初始化附件
			initAttach('storeApplyAttachs','addStoreAttach0','table-store-attach0','0');
			initAttach('storeApplyAttachs','addStoreAttach1','table-store-attach1','1');
			initAttach('storeApplyAttachs','addStoreAttach2','table-store-attach2','2');
			

            $("#openStore").click(function () {
                $("#openStore").bindQueryBtn({
                    type: 'store',
                    bindClick: false,
                    title: '${message("查询用户")}',
                    url: '/member/store_member/select_store_member.jhtml?multi=2&memberType=' + 0 + '&isSalesman=true',
                    callback: function (rows) {
                        if (rows.length > 0) {
                            var row = rows[0];
                            $("input[name='storeMemberId']").attr("value", row.id)
                            $("input[name='storeMemberName']").attr("value", row.name);
                            $(".storeMemberPhoneText").html(row.mobile);
                            $(".storeMemberPhone").val(row.mobile);
                        }
                    }
                });
            })
            
            //查询sbu
            $("#selectSbu").click(function(){
	            $("#selectSbu").bindQueryBtn({
	            	type:'sbug',
	            	bindClick: false,
	                title: '${message("查询SBU")}',
	                url:'/basic/sbu/select_sbu.jhtml?multi=2',
	                callback: function (rows) {
	                    if(rows.length>0){
							var mhtml="";
							if($("input[name='sbuIds']").val() == null){
								var all= "";
							}else{
								var all= $("input[name='sbuIds']").val();
							}
							
							for (var i = 0; i < rows.length;i++) {
								var idH = $(".sbuIds_"+rows[i].id).length;
								if(idH > 0){
									$.message_alert('SBU:【'+rows[i].name+'】已添加');
									return false;
								}
							}
							for (var i = 0; i < rows.length;i++) {
								all =all +','+ rows[i].name;
								mhtml = '<div><input name="sbuIds" class="text sbuIds sbuIds_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newclosePro(this,\'sbus\',\'sbuName\')"></i></div>';
								$(".sbus").append(mhtml);
							}
							$("input[name='sbuName']").attr("value",all);
						}
	                }
	            });
            });
            
			//查询经营组织
            $("#selectOrganization").click(function(){
	            $("#selectOrganization").bindQueryBtn({
	            	type:'organization',
	            	bindClick: false,
	                title: '${message("查询经营组织")}',
	                url:'/member/management/select_organization.jhtml',
	                callback: function (rows) {
	                    if(rows.length>0){
							var mhtml="";
							if($("input[name='organizationIds']").val() == null){
								var all= "";
							}else{
								var all= $("input[name='organizationIds']").val();
							}
							
							for (var i = 0; i < rows.length;i++) {
								var idH = $(".organizationIds_"+rows[i].id).length;
								if(idH > 0){
									$.message_alert('经营组织:【'+rows[i].name+'】已添加');
									return false;
								}
							}
							for (var i = 0; i < rows.length;i++) {
								all =all +','+ rows[i].name;
								mhtml = '<div><input name="organizationIds" class="text organizationIds organizationIds_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newclosePro(this,\'organizations\',\'organizationName\')"></i></div>';
								$(".organizations").append(mhtml);
							}
							$("input[name='organizationName']").attr("value",all);
						}
	                }
	            });
            });

            $("#selectMemberRank").live("click", function () {
                var $this = $(this);
                var saleOrgId = $(".saleOrgId").val();
                if (saleOrgId == null || saleOrgId == '') {
                    $.message_alert('请先选择机构！');
                    return false;
                }
                $this.bindQueryBtn({
                    type: 'memberRank',
                    bindClick: false,
                    title: '${message("查询价格类型")}',
                    url: '/basic/member_rank/select_memberRank.jhtml?saleOrgId=' + saleOrgId,
                    callback: function (rows) {
                        if (rows.length > 0) {
                            var row = rows[0];
                            $this.closest("tr").find(".memberRankName").val(row.name);
                            $this.closest("tr").find(".memberRankId").val(row.id);

                        }
                    }
                });
            })

            var option1 = {
                dataType: "json",
                uploadToFileServer: true,
                fileNumLimit: 1,
                uploadSize: "fileurl",
                callback: function (data) {
                    var date = new Date();
                    var year = date.getFullYear();
                    var month = date.getMonth() + 1;
                    var day = date.getDate();
                    var time = year + '-' + month + '-' + day;
                    var file_info = data[0].file_info;
                    var image = setting.file_image;
                    [@compress single_line = true]
                    var tHtml = '<span class="ico-options upload-list" >' +
                        '<div class="ul-box" style="width:80px;height: 80px;padding:0px;margin:0px">' +
                        '<div class="pic" style="width:82px;height:82px"><a href="' + file_info.url + '" target="_blank" style="width: 80px;">' +
                        '<img src="/resources/images/icon_xls.png" style="width: 80px; height: 80px;">' +
                        '</a></div>' +
                        '<a class="del deleteStoreImage" href="#"></a>' +
                        '</div>' +
                        '<input type="hidden" name="storeImage" value="' + file_info.url + '"/>' +
                        '</span>';
                    [/@compress]
                    $("#storeImage").parent().before(tHtml);
                    $("#storeImage").parent().remove();

                }
            }
            $("#storeImage").file_upload(option1);

            var $deleteStoreImage = $(".deleteStoreImage");
            $deleteStoreImage.live("click", function () {
                var $this = $(this);
                var html = '<span><a href="javascript:;" class="a-upload addProductImage" style="width:80px;margin-right:10px;height:80px;" id="storeImage"></a></span>';
                $(this).closest("span").before(html);
                $(this).closest("span").remove();
                $("#storeImage").file_upload(option1);
            });

            $("#needCautionPaid").blur(function () {
                editCaution();
            });
            $("#realCautionPaid").blur(function () {
                editCaution();
            });
            var $storeImage2 = $("#storeImage2");
            var option2 = {
                dataType: "json",
                uploadToFileServer: true,
                fileNumLimit: 1,
                uploadSize: "fileurl",
                callback: function (data) {
                    var date = new Date();
                    var year = date.getFullYear();
                    var month = date.getMonth() + 1;
                    var day = date.getDate();
                    var time = year + '-' + month + '-' + day;
                    var file_info = data[0].file_info;
                    var image = setting.file_image;
                    [@compress single_line = true]
                    var tHtml = '<span class="ico-options upload-list">' +
                        '<div class="ul-box" style="width:80px;height: 80px;padding:0px;margin:0px">' +
                        '<div class="pic"  style="width:82px;height:82px"><a href="' + file_info.url + '" target="_blank" style="width: 80px;">' +
                        '<img src="/resources/images/icon_xls.png" style="width: 80px; height: 80px;">' +
                        '</a></div>' +
                        '<a class="del deleteStoreImage2" href="#"></a>' +
                        '</div>' +
                        '<input type="hidden" name="storeImage2" value="' + file_info.url + '"/>' +
                        '</span>';
                    [/@compress]
                    $("#storeImage2").parent().before(tHtml);
                    $("#storeImage2").parent().remove();

                }
            }
            $("#storeImage2").file_upload(option2);

            var $deleteStoreImage2 = $(".deleteStoreImage2");
            $deleteStoreImage2.live("click", function () {
                var $this = $(this);
                var html = '<span><a href="javascript:;" class="a-upload addProductImage" style="width:80px;height:80px;" id="storeImage2"></a></span>';
                $(this).closest("span").before(html);
                $(this).closest("span").remove();
                $("#storeImage2").file_upload(option2);
            });


            //地区选择
            $areaId.lSelect();
            $bAreaId.lSelect();
            $newAreaId.lSelect();


//	$shopAddressId.lSelect();

            // 表单验证
            $inputForm.validate({
                rules: {
                    dealerName: "required",//经销商姓名
                    dealerSex: "required",//经销商性别
                    accountTypeCode: "required",//城市等级
                    saleOrgName: "required",//机构
                    name: "required",
                    headPhone: "required",
                    dealerGrade: "required",
                    businessTypeId: "required",//业务类型
                    alias: "required",
                    //fixedNumber: "required",
                    identity: "required",//身份证信息
                    platformProperty: "required",//平台性质
                    sbuId: "required",
                    contact: "required",//法人代表
                    storeApplySalesAreaName: "required",//销售区域
                    memberRankName: "required",//价格类型
                    headAddress: "required",//经销商地址
                    category: "required",//经销商类别
                    dealerBackground: "required",//经销商背景
                    dealerLevel: "required",//经销商等级
                    propagandaAwareness: "required",//宣传意识
                    brandAwareness: "required",//品牌意识
                    afterService: "required",//售后服务
                    salesChannelsVal1: "required",
                    salesChannelsVal2: "required",
                    salesChannelsVal3: "required",
                    salesChannelsVal4: "required",
                    salesChannelsVal5: "required",
                    salesChannelsVal6: "required",
                    marketersNumber: "required",
                    afterSaleNumber: "required",
                    installBodyNumber: "required",
                    warehouseArea: "required",//仓库面积
                    truck: "required",
                    smallCar: "required",
                    pcOrBroadband: "required",
                    
                    cashClientFlag: "required",//现金客户
                    subType: "required",//经销商子类型
                
                },
                messages: {
                    name: {
                        pattern: "${message("非法字符")}",
                        remote: "${message("已存在")}"
                    },
                    dealerBackground: {
                        required: "<br>请选择经销商背景"
                    },
                    propagandaAwareness: {
                        required: "<br>请选择宣传意识"
                    },
                    brandAwareness: {
                        required: "<br>请选择品牌意识"
                    },
                    afterService: {
                        required: "<br>请选择售后服务"
                    },
                    dealerLevel: {
                        required: "<br>请选择经销商等级"
                    },
                    pcOrBroadband: {
                        required: "<br>请选择电脑/宽带"
                    },
                    cashClientFlag: {
                        required: "<br>请选择是否现金客户"
                    },
                },
                errorPlacement: function (error, element) { //指定错误信息位置
                    if (element.is(':radio') || element.is(':checkbox')) { //如果是radio或checkbox
                        var eid = element.attr('name'); //获取元素的name属性
                        error.appendTo(element.parent().parent()); //将错误信息添加当前元素的父结点后面
                    } else {
                        error.insertAfter(element);
                    }
                }
            });

            $typeSelect.change(function () {
                if ($(this).val() == "onlineShop") {
                    $onlineShopTypeTr.show();
                    $onlineShopTypeTr.attr("disabled", false);
                } else {
                    $onlineShopTypeTr.hide();
                    $onlineShopTypeTr.attr("disabled", "true");
                }
                if ($(this).val() == "distributor") {
                    $("input[name=isReduceBalance]").attr("checked", false);
                    $("input[name=isReduceBalance]").attr("disabled", false);
                } else {
                    $("input[name=isReduceBalance]").attr("checked", false);
                    $("input[name=isReduceBalance]").attr("disabled", true);
                }
            });


            $("form").bindAttribute({
                isConfirm: true,
                callback: function (resultMsg) {
                    $.message_timer(resultMsg.type, resultMsg.content, 1000, function () {
                        location.href = '/member/storeApply/edit/${code}.jhtml?id=' + resultMsg.objx;

                    })
                }
            });

            //查询产品分类
            $("#addStoreProductType").bindQueryBtn({
                type: 'productType',
                title: '${message("查询产品分类")}',
                url: '/product/product_category/findTopCategory.jhtml',
                callback: function (row) {
                    $mmGrid2.addRow(row);
                }
            });

            //查询价格等级
//	$("#selectMemberRank").bindQueryBtn({
//		type:'memberRank',
//		title:'${message("查询价格类型")}',
//		url:'/basic/member_rank/select_memberRank.jhtml'
//	});
            //查询大客户等级
            $("#selectChangeMemberRankName").bindQueryBtn({
                type: 'changeMemberRank',
                title: '${message("查询大客户等级")}',
                url: '/basic/member_rank/select_memberRank.jhtml'
            });

			var saleOrgRegion = {
				[#list saleOrgRegions as saleOrgRegion]
				${saleOrgRegion.id}:"${saleOrgRegion.value}",
				[/#list]
			}

            //查询机构
            $("#selectSaleOrg").bindQueryBtn({
                type: 'saleOrg',
                title: '${message("查询机构")}',
                url: '/basic/saleOrg/select_saleOrg.jhtml?isSellSaleOrg=1',
                callback: function (rows) {
                    if (rows != null) {
                        var row = rows[0];
                        $(".saleOrgId").val(row.id);
                        $(".saleOrgName").val(row.name);
                        $(".salesPlatformId").val(row.id);
                        $(".salesPlatformName").text(row.name);
                        $(".salesPlatformName").val(row.name);
                        var value = row.value;
                        $(".platformProperty option").each(function () {
                            var a = $(this);
                            if (a.text() == value) {
                                a.attr("selected", true);
                                $("input[name='platformProperty']").val(a.val());
                            }
                        });
                        
                		var region = saleOrgRegion[row.region]==null?"":saleOrgRegion[row.region];
                        /*if(row.region == "1"){
                        	region = "南区";
                        }
                        if(row.region == "0"){
                        	region = "北区";
                        }*/
                        $('.region').text(region);
                        $('input[name="region"]').val(region);
                        
                    }
                }
            });

            $(".lineSelectArea").live("click", function () {
                var $this = $(this);
                $this.bindQueryBtn({
                    bindClick: false,
                    type: 'area',
                    title: '${message("查询地区")}',
                    url: '/basic/area/select_area.jhtml',
                    callback: function (rows) {
                        if (rows.length > 0) {
                            var $tr = $this.closest("tr");
                            $tr.find(".lineAreaId").val(rows[0].id);
                            $tr.find(".lineAreaName").val(rows[0].full_name);
                        }
                    }
                });
            })
            //查询销售区域
            $(".selectsalesArea").live("click", function () {
                var $this = $(this);
                $this.bindQueryBtn({
                    type: 'salesArea',
                    title: '${message("销售区域")}',
                    url: '/basic/sales_area/select_salesarea.jhtml',
                    callback: function (rows) {
                        if (rows.length > 0) {
                            var row = rows[0];
                            $this.closest("tr").find(".salesAreaName").val(row.name);
                            $this.closest("tr").find(".salesAreaId").val(row.id);
                        }
                    }
                });
            })
            $("#addStoreCautionMoney").click(function () {
                var row = {};
                $mmGrid4.addRow(row);
                editCautionMoney();
            });

            $("#addStoreBusinessRecord").click(function () {
                var row = {};
                $mmGrid3.addRow(row);
            });

            var countAddress = 0;
            $addRecviceAddress.click(function () {

                var fullArea ='';//地区全名
                $('.areaId_select option:selected').each(function (index) {
                    if($(this).html().indexOf('请选择') == -1){
                        fullArea += $(this).html();
                    }
                });

                var row = {
                    areaId: $newAreaId.val(),//地区id
                    fullArea: fullArea,//地区全名
                    salesAreaId: $salesAreaId.val(),//销售区域id
                    salesAreaName: $('#salesAreaName').val(),//销售区域名称
                };
                $mmGrid.addRow(row);
                $("#areaId" + countAddress).lSelect();
                countAddress = parseInt(countAddress) + 1
            })
            var cols = [
                {
                    title: '${message("收货人")}', align: 'center', width: 130, renderer: function (val, item, rowIndex) {
                        return '<input type="text" name="storeApplyAddress[' + countAddress + '].consignee" class="text countAddress" btn-fun="clear" />'
                    }
                },
                {
                    title: '${message("收货人电话")}',
                    align: 'center',
                    width: 130,
                    renderer: function (val, item, rowIndex) {
                        return '<input type="text" name="storeApplyAddress[' + countAddress + '].mobile" class="text  countAddress" btn-fun="clear" />'
                    }
                },

                {
                    title: '${message("收货地区")}', align: 'left', width: 180, renderer: function (val, item, rowIndex) {
                        html = '<span class="search ">' +
                            '<input class="text lineAreaId areaId" type="hidden" name="storeApplyAddress[' + countAddress + '].area.id" value="'+item.areaId+'">' +
                            '<input class="text countAddress lineAreaName" maxlength="200" type="text" readonly="readonly" value="'+item.fullArea+'" onkeyup="clearSelect(this)">' +
                            '<input type="button" class="iconSearch lineSelectArea" value="">' +
                            '</span>';
                        return html;
                        //return '<input type="hidden" class="countAddress" id="areaId'+countAddress+'" name="storeAddress['+countAddress+'].area.id" value="" treePath="" /> </span> </td>'
                    }
                },
                {
                    title: '${message("销售区域")}', align: 'left', width: 180, renderer: function (val, item, rowIndex) {
                        html = '<span class="search ">' +
                            '<input class="text linesalesAreaId salesAreaId" type="hidden" name="storeApplyAddress[' + countAddress + '].salesArea.id" value="'+item.salesAreaId+'">' +
                            '<input class="text linesalesAreaName salesAreaName " maxlength="200" type="text" readonly="readonly" value="'+item.salesAreaName+'" onkeyup="clearSelect(this)">' +
                            '<input type="button" class="iconSearch selectsalesArea" value="">' +
                            '</span>';
                        return html;
                    }
                },
                {
                    title: '${message("收货地址")}', align: 'center', width: 350, renderer: function (val, item, rowIndex) {
                        return '<input type="text" name="storeApplyAddress[' + countAddress + '].address" class="text countAddress" btn-fun="clear" maxlength="200" value="" />'
                    }
                },
                {
                    title: '${message("收货地区邮编")}', align: 'center', renderer: function (val, item, rowIndex) {
                        return '<input type="text" name="storeApplyAddress[' + countAddress + '].zipCode" class="text" btn-fun="clear" maxlength="200" value="" />'
                    }
                },
                {
                    title: '${message("地址类型")}',
                    align: 'center',
                    name: 'address_type',
                    width: 130,
                    renderer: function (val, item, rowIndex) {
                        var html = '<select name="storeApplyAddress[' + countAddress + '].addressType" class="text countAddress">'
                            + '<option value="0" >经销商地址</option> '
                            + '<option value="1">收货地址</option>'
                            + '<option value="2">收单地址</option>'
                            + '<option value="3" selected="selected">收单收货地址</option>' + '</select>';
                        return html;
                    }
                },
                {
                    title: '${message("设置")}', align: 'center', renderer: function (val, item, rowIndex) {
                        return '<label onClick="chooseOne(this)">' +
                            '<input type="checkbox" name="storeApplyAddress[' + countAddress + '].isDefault" class="isDefaultCheckbox" />${message("是否默认")}' +
                            '<input type="hidden" name="_storeApplyAddress[' + countAddress + '].isDefault" class="isDefault" value="false" /></label>'
                    }
                },

                {
                    title: '${message("操作")}', name: '', align: 'center', renderer: function (val, item, rowIndex) {
                        return '<a href="javascript:;" class="btn-delete" onclick="deleteAddress(this)">删除</a>'
                    }
                },

            ];
            $mmGrid = $('#table-m1').mmGrid({
                height: 'auto',
                cols: cols,
                checkCol: false,
                autoLoad: true,
                fullWidthRows: true,
            });

            /*
            var countStoreManager = 0;
            $addStoreManager.click(function(){
                var row={};
                $mmGrid1.addRow(row);
                countStoreManager=	parseInt(countStoreManager)+1;
            })
            var colss = [
                { title:'

            ${message("客户经理")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeManagers['+countStoreManager+'].manager" class="text countStoreManager" btn-fun="clear" />'
		}},
		
		{ title:'

            ${message("操作")}', name:'', align:'center',renderer: function(val,item,rowIndex){
			return '<a href="javascript:;" class="btn-delete" onclick="deleteStoreManager(this)">删除</a>'
		}}
		
	];
	  $mmGrid1 = $('#table-m2').mmGrid({
		height:'auto',
	    cols: colss,
	    checkCol:false,
	    fullWidthRows: true
	 }); 
	*/

            /**发票信息*/
            [#--
            var invoiceIndex = 0;
             $("#addInvoice").click(function(){
                    var row={};
                    $invoice_mmGrid.addRow(row);
                    invoiceIndex=	parseInt(invoiceIndex)+1;
            })
             var invoice_cols = [
                { title:'${message("发票抬头")}', name:'invoice_title', align:'center',width:100 ,renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyInvoiceInfos['+invoiceIndex+'].invoiceTitle" class="text" value="'+val+'" />'
                }},
                { title:'${message("银行账户")}', name:'zengzhishuiyhzh', align:'center',width:100 ,renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyInvoiceInfos['+invoiceIndex+'].zengzhishuiyhzh" class="text" value="'+val+'" />'
                }},
                { title:'${message("开户行名称")}', name:'kaihuyh', align:'center',width:100 ,renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyInvoiceInfos['+invoiceIndex+'].kaihuyh" class="text" value="'+val+'" />'
                }},
                { title:'${message("纳税人识别号")}', name:'nashuishibiehao', align:'center',width:100 ,renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyInvoiceInfos['+invoiceIndex+'].nashuishibiehao" class="text" value="'+val+'" />'
                }},
                { title:'${message("开票电话")}', name:'zhucedianhua', align:'center',width:100 ,renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyInvoiceInfos['+invoiceIndex+'].zhucedianhua" class="text" value="'+val+'" />'
                }},
                { title:'${message("开票地址")}', name:'zhucedizhi', align:'center',width:100 ,renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyInvoiceInfos['+invoiceIndex+'].zhucedizhi" class="text" value="'+val+'" />'
                }},
                { title:'${message("设置")}', align:'center',renderer: function(val,item,rowIndex){
                    return '<label onClick="chooseOne(this)">'+
                    '<input type="checkbox" name="storeApplyInvoiceInfos['+invoiceIndex+'].isDefault" class="isDefaultCheckbox" />${message("是否默认")}'+
                    '<input type="hidden" name="storeApplyInvoiceInfos['+invoiceIndex+'].isDefault" class="isDefault" value="false" /></label>'
                }},
                { title:'${message("操作")}', name:'', align:'center',width:10,renderer: function(val,item,rowIndex){
                    return '<a href="javascript:;" class="btn-delete" onclick="deleteInvoice(this)">删除</a>'
                }},

            ];
              $invoice_mmGrid = $('#table-invoice').mmGrid({
                height:'auto',
                cols: invoice_cols,
                checkCol:false,
                autoLoad:true,
                fullWidthRows: true
             });

            //客户联系人
              var countStoreContract = 0;
              $addStoreContract.click(function(){
              var row={};
              $mmGrid8.addRow(row);
              countStoreContract = parseInt(countStoreContract)+1;
              });
              var colss = [
                  { title:'${message("是否法人代表")}',name:'is_legal_person', align:'center',renderer: function(val,item,rowIndex){
                      return '<label onClick="chooseOne(this)">'+
                      '<input type="checkbox" name="storeApplyContract['+countStoreContract+'].isLegalPerson" class="isDefaultCheckbox" value="true"/>'+
                      '<input type="hidden" name="_storeApplyContract['+countStoreContract+'].isLegalPerson" class="isDefault" value="false" /></label>';
                  }},
                  { title:'${message("姓名")}', align:'center',name:'name',width:130 ,renderer: function(val,item,rowIndex){
                      return '<input type="text" name="storeApplyContract['+countStoreContract+'].name" class="text storeContractName"  onblur="showLegalPerson(this)" />'
                  }},
                  { title:'${message("性别")}', align:'center',name:'gender',width:130  ,renderer: function(val,item,rowIndex){
                      var html = '<select name="storeApplyContract['+countStoreContract+'].gender" class="text gender">'
                          +'<option value="1">男</option>'
                          +'<option value="2">女</option>'
                          +'</select>';
                      return html;
                  }},
                  { title:'${message("联系人类型")}', align:'center',name:'store_type',width:130  ,renderer: function(val,item,rowIndex){
                      var html = '<select name="storeApplyContract['+countStoreContract+'].storeType" class="text gender">'
                          +'<option value="1">业务联系人</option>'
                          +'<option value="2">店长</option>'
                          +'<option value="3">财务人员</option>'
                          +'<option value="4">物流人员</option>'
                          +'<option value="5">客服经理</option>'
                          +'<option value="6">其他联系人</option>'
                          +'<option value="7">职业经理人</option>'
                          +'<option value="0" >其他</option> '
                          +'</select>';
                      return html;
                  }},
                  { title:'${message("收货人")}', align:'center',name:'consignee',width:130 ,renderer: function(val,item,rowIndex){
                      return '<input type="text" name="storeApplyContract['+countStoreContract+'].consignee" class="text " />'
                  }},
                  { title:'${message("收货方")}', align:'center',name:'receiving_party',width:130 ,renderer: function(val,item,rowIndex){
                      return '<input type="text" name="storeApplyContract['+countStoreContract+'].receivingParty" class="text " />'
                  }},
                  { title:'${message("收单方")}', align:'center',name:'accept',width:130 ,renderer: function(val,item,rowIndex){
                      return '<input type="text" name="storeApplyContract['+countStoreContract+'].accept" class="text " />'
                  }},
                  { title:'${message("证件类型")}', align:'center',name:'certificate_type',width:130  ,renderer: function(val,item,rowIndex){
                      var html = '<select name="storeApplyContract['+countStoreContract+'].certificateType" class="text">'
                          +'<option value="1">身份证 </option>'
                          +'<option value="2">护照</option>'
                          +'<option value="3">军官证</option>'
                          +'<option value="4">港澳台同胞回乡证</option>'
                          +'<option value="5">学生证</option>'
                          +'<option value="0">其他证件</option> '
                          +'</select>';
                      return html;
                  }},
                  { title:'${message("移动电话")}', align:'center',name:'mobile',width:130 ,renderer: function(val,item,rowIndex){
                      return '<input type="text" name="storeApplyContract['+countStoreContract+'].mobile" class="text " />'
                  }},
                  { title:'${message("工作电话")}', align:'center',name:'workphone',width:130 ,renderer: function(val,item,rowIndex){
                      return '<input type="text" name="storeApplyContract['+countStoreContract+'].workphone" class="text " />'
                  }},
                  { title:'${message("家庭电话 ")}', align:'center',name:'homephone',width:130 ,renderer: function(val,item,rowIndex){
                      return '<input type="text" name="storeApplyContract['+countStoreContract+'].homephone" class="text " />'
                  }},
                  { title:'${message("QQ ")}', align:'center',name:'qq',width:130 ,renderer: function(val,item,rowIndex){
                      return '<input type="text" name="storeApplyContract['+countStoreContract+'].qq" class="text " />'
                  }},
                  { title:'${message("邮件 ")}', align:'center',name:'email',width:130 ,renderer: function(val,item,rowIndex){
                      return '<input type="text" name="storeApplyContract['+countStoreContract+'].email" class="text " />'
                  }},
                  { title:'${message("组织 ")}', align:'center',name:'salorg_name',width:130 ,renderer: function(val,item,rowIndex){
                      return '<input type="text" name="storeApplyContract['+countStoreContract+'].salorgName" class="text " />'
                  }},
                  { title:'${message("备注")}', align:'center',name:'memo',width:130 ,renderer: function(val,item,rowIndex){
                      return '<input type="text" name="storeApplyContract['+countStoreContract+'].memo" class="text " />'
                  }},
                  { title:'${message("是否失效")}', align:'center',name:'is_invalid',width:130  ,renderer: function(val,item,rowIndex){
                      var html = '<select name="storeApplyContract['+countStoreContract+'].isInvalid" class="text gender">'
                          +'<option value="1">是</option>'
                          +'<option value="0">否</option> '
                          +'</select>';
                      return html;
                  }},
                  { title:'${message("操作")}', name:'', align:'center',renderer: function(val,item,rowIndex){
                      return '<a href="javascript:;" class="btn-delete" onclick="deleteStoreContract(this)">删除</a>'
                  }}

              ];
              $mmGrid8 = $('#table-storecontract').mmGrid({
                  height:'auto',
                  cols: colss,
                  checkCol:false,
                  fullWidthRows: true
              }); */

              /**合作单位*/
             var cooperationIndex = 0;
             $("#addCooperation").click(function(){
                    var row={};
                    $cooperation_mmGrid.addRow(row);
                    cooperationIndex =	parseInt(cooperationIndex)+1;
             })
             var cooperation_cols = [
                 { title:'${message("记录类型")}', align:'center',name:'record_type',width:120,renderer: function(val,item,rowIndex){
                      var html = '<select name="storeApplyCooperation['+cooperationIndex+'].recordType" class="text">'
                          +'<option value="0">合作单位发布会</option>'
                          +'<option value="1">门店入驻</option>'
                          +'<option value="2">门店撤场</option>'
                          +'</select>';
                      return html;
                }},
                { title:'${message("商场")}', name:'market', align:'center',width:100 ,renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].market" class="text" value="'+val+'" />';
                }},
                { title:'${message("商场所在地")}', name:'marketlocal', align:'center',width:100 ,renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].marketlocal" class="text" value="'+val+'" />';
                }},
                { title:'${message("商场负责人")}', name:'market_leader', align:'center',width:100 ,renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].marketLeader" class="text" value="'+val+'" />';
                }},
                { title:'${message("负责人联系方式")}', name:'leader_phone', align:'center',width:100 ,renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].leaderPhone" class="text" value="'+val+'" />';
                }},
                { title:'${message("区域对接人")}', name:'area_point', align:'center',width:100 ,renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].areaPoint" class="text" value="'+val+'" />';
                }},
                { title:'${message("经销商")}', name:'dealer', align:'center',width:100,renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].dealer" class="text" value="'+val+'" />';
                }},
                { title:'${message("跟进事项")}', name:'follow_up', align:'center',width:100,renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].followUp" class="text" value="'+val+'" />';
                }},
                { title:'${message("跟进时间")}', align:'center',width:100,name:'follow_time',renderer: function(val,item,rowIndex){
                    return '<input name="storeApplyCooperation['+cooperationIndex+'].followTime" class="text" value="" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
                }},
                { title:'${message("门店位置")}', name:'position', align:'center',width:100,renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].position" class="text" value="'+val+'" />';
                }},
                { title:'${message("门店面积")}', name:'acreage', align:'center',width:100,renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].acreage" class="text" value="'+val+'" />';
                }},
                { title:'${message("沟通结果")}', name:'communicat_result', align:'center',width:100,renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].communicatResult" class="text" value="'+val+'" />';
                }},
                { title:'${message("关店原因")}', name:'close_ship_reason', align:'center',width:100,renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].closeShipReason" class="text" value="'+val+'" />';
                }},
                { title:'${message("加盟店/直营店")}', name:'join_store_type', align:'center',width:100,renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].joinStoreType" class="text" value="'+val+'" />';
                }},
                { title:'${message("通知时间")}', align:'center',width:100,name:'notice_time',renderer: function(val,item,rowIndex){
                    return '<input name="storeApplyCooperation['+cooperationIndex+'].noticeTime" class="text" value="" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
                }},
                { title:'${message("省份")}', name:'cooperation_province', align:'center',width:100,renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].cooperationProvince" class="text" value="'+val+'" />';
                }},
                { title:'${message("招商发布会地址")}', name:'conference_adress', align:'center',width:100,renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].conferenceAdress" class="text" value="'+val+'" />';
                }},
                { title:'${message("参会人员")}', name:'conferee', align:'center',width:100,renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].conferee" class="text" value="'+val+'" />';
                }},
                { title:'${message("经销商进驻意愿")}', name:'dominate', align:'center',width:100,renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].dominate" class="text" value="'+val+'" />';
                }},
                { title:'${message("是否进驻")}', align:'center',renderer: function(val,item,rowIndex){
                    var html = '<select name="storeApplyCooperation['+cooperationIndex+'].dealerIsIn" class="text">'
                      +'<option value="0">是</option>'
                      +'<option value="1">否</option>'
                      +'</select>';
                      return html;
                }},
                { title:'${message("租金")}', name:'rent', align:'center',width:100,renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].rent" class="text" value="'+val+'" />';
                }},
                { title:'${message("开业时间")}', align:'center',width:100,name:'opening_time',renderer: function(val,item,rowIndex){
                    return '<input name="storeApplyCooperation['+cooperationIndex+'].openingTime" class="text" value="" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
                }},
                { title:'${message("区域关店时间")}', align:'center',width:100,name:'area_close_time',renderer: function(val,item,rowIndex){
                    return '<input name="storeApplyCooperation['+cooperationIndex+'].areaCloseTime" class="text" value="" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
                }},
                { title:'${message("进驻时间")}', align:'center',width:100,name:'dealer_in_time',renderer: function(val,item,rowIndex){
                    return '<input name="storeApplyCooperation['+cooperationIndex+'].dealerInTime" class="text" value="" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
                }},
                { title:'${message("商场支持：（租金、展位)")}', align:'center',width:100,name:'market_support',renderer: function(val,item,rowIndex){
                    return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].marketSupport" class="text" value="'+val+'" />';
                }},
                { title:'${message("操作")}', name:'', align:'center',renderer: function(val,item,rowIndex){
                    return '<a href="javascript:;" class="btn-delete" onclick="deleteCooperation(this)">删除</a>'
                }}
            ];
            $cooperation_mmGrid = $('#table-cooperation').mmGrid({
                height:'auto',
                cols: cooperation_cols,
                checkCol:false,
                autoLoad:true,
                fullWidthRows: true
            });
            //客户sbu，价格类型
                $(".deleteSbu").live("click", function() {
                    var index = $(this).closest("tr").index();
                    $.message_confirm('您确定要删除吗？',function(){
                        $sbu_mmGrid.removeRow(index);
                    })
                });

                $addSbu.click(function(){
                    var row={};
                    $sbu_mmGrid.addRow(row);
                });

                var rowIndex=0;
                [#if sbu_json ==null]
                var items=[];
                [#else]
                var items=${sbu_json};
                [/#if]

                var sbu_cols = [
                    { title:'${message("sbu")}', name:'sbu_name' ,align:'center',renderer: function(val,item,rowIndex){
                        var sbuId = item.sbuId;
                        if(item.sbuId == undefined){
                            sbuId="";
                        }
                        if(item.sbu_name == undefined){
                            var html = '<span class="search ">'+
                                 '<input class="text storeSbuId" type="hidden" name="storeApplySbu['+rowIndex+'].sbu.id" value="">'+
                              '<input class="text storeSbuName" maxlength="200" type="text"  value="" onkeyup="clearSelect(this)">'+
                              '<input type="button"  class="iconSearch " value="" id="selectSbu">'+
                           '</span>';
                           return html;
                        }else{
                             html = '<span class="search ">'+
                                            '<input class="text storeSbuId" type="hidden" name="storeApplySbu['+rowIndex+'].sbu.id" value="'+sbuId+'">'+
                                         '<input class="text storeSbuName" maxlength="200" type="text"  value="'+item.sbu_name+'" onkeyup="clearSelect(this)">'+
                                         '<input type="button"  class="iconSearch " value="" id="selectSbu">'+
                                      '</span>';
                                  return html;
                        }
                    }},
                    { title:'${message("价格类型")}', name:'memberRank' ,align:'center',renderer: function(val,item,rowIndex){
                        var memberRankId = item.memberRankId;
                        if(item.memberRankId == undefined){
                            memberRankId="";
                        }
                        if(item.memberRank == undefined){
                        var html = '<span class="search ">'+
                                          '<input type="hidden" name="storeApplySbu['+rowIndex+'].memberRank.id" class="text memberRankId" id="memberRankId" btn-fun="clear"  value=""/>'+
                                       '<input type="text"  class="text memberRankName" maxlength="200" onkeyup="clearSelect(this)" id="memberRankName"  value=""/>'+
                                       '<input type="button" class="iconSearch" value="" id="selectMemberRank">'+
                                    '</span>';
                                    return html;
                                }else{
                                     html = '<span class="search ">'+
                                            '<input type="hidden" name="storeApplySbu['+rowIndex+'].memberRank.id" class="text memberRankId" id="memberRankId" btn-fun="clear"  value="'+memberRankId+'"/>'+
                                         '<input type="text"  class="text memberRankName" maxlength="200" onkeyup="clearSelect(this)" id="memberRankName"  value="'+item.memberRank+'"/>'+
                                         '<input type="button" class="iconSearch" value="" id="selectMemberRank">'+
                                      '</span>';
                                      return html;
                                }
                     }},
                    { title:'${message("设置")}', name:'is_default' ,align:'center',renderer:function(val,item,rowIndex, obj){
                           if(obj==undefined){
                                if(item.is_default){
                                    return '<label onClick="chooseOne(this)">'+
                                    '<input type="checkbox" name="sbuItemsDefault" class="isDefaultCheckbox" checked="true"/>是否默认'+
                                    '<input type="hidden" name="storeApplySbu['+rowIndex+'].isDefault" class="isDefault" value="1" /></label>';
                        }else{
                            return '<label onClick="chooseOne(this)">'+
                            '<input type="checkbox" name="sbuItemsDefault" class="isDefaultCheckbox" />是否默认'+
                            '<input type="hidden" name="storeApplySbu['+rowIndex+'].isDefault" class="isDefault" value="0" /></label>';
                        }
                        }else{
                        if($("input[value=1].isDefault").length>0){
                            return '<label onClick="chooseOne(this)">'+
                            '<input type="checkbox" name="sbuItemsDefault" class="isDefaultCheckbox" />是否默认'+
                            '<input type="hidden" name="storeSbu['+rowIndex+'].isDefault" class="isDefault" value="0" /></label>';
                        }else{
                            return '<label onClick="chooseOne(this)">'+
                            '<input type="checkbox" name="sbuItemsDefault" class="isDefaultCheckbox" checked/>是否默认'+
                            '<input type="hidden" name="storeSbu['+rowIndex+'].isDefault" class="isDefault" value="1" /></label>';
                        }
                    }
                    }},
                    { title:'${message("操作")}', align:'center', width:60, renderer:function(val,item){
                    return '<a href="javascript:;" class="deleteSbu btn-delete">删除</a>';
                    rowIndex++;
                    }},
                ];
                  $sbu_mmGrid = $('#table-sbu').mmGrid({
                    height:'auto',
                            cols: sbu_cols,
                            fullWidthRows:true,
                               items:items,
                            checkCol: false,
                            autoLoad: true,
                            callback:function(){

                            }
                 });
            --]

            //查询销售区域
            $("#selectStoreSalesArea").bindQueryBtn({
                type: 'salesArea',
                title: '${message("查询销售区域")}',
                url: '/basic/sales_area/select_salesarea.jhtml'
            });

            //查询销售品类
            $("#selectBusinessCategory").bindQueryBtn({
                type: 'productType',
                title: '${message("查询销售品类")}',
                url: '/product/product_category/findTopCategory.jhtml?multi=2',
                callback: function (rows) {
                    /* console.log("rows.length: " + rows.length);
                    console.log(rows); */
                    if (rows.length > 0) {
                        $('.productCategoryIds').remove()
                        var name = ''
                        var html = '';
                        for(var i = 0;i<rows.length;i++){
                            name += rows[i].name+ ","
                            html += '<input type="hidden" name="businessCategoryApplys['+i+'].productBigType.id" class="text productCategoryIds" value="' + rows[i].id +'"/>'
                        }
                        $(".salesCategory").val(name);
                        $(".salesCategory").after(html);
                    }
                }
            });

            // 初始化应缴品牌保证金
            handleAccountType();

            //经销商背景编辑
            $('input[type=radio][name=dealerBackground]').change(function () {
                // console.log(this.value)dealerBackgroundMemo
                var $input = $('input[name=dealerBackgroundMemo]')
                if (this.value == 3) {
                    $input.removeAttr("readonly")
                    $input.attr("placeholder", "请输入")
                } else if (this.value == 4) {
                    $input.attr("readonly", "true");
                    $input.attr("placeholder", "")
                    $input.val("");
                }
            });
        });

        var countAddress = 0;//
        var limitAddress = 0;//最多五个
        function addRecviceAddress() {
            /* 	var html='<tr id="TrAreaId2"><th> 收货人:</th> <td> <input type="text" name="storeAddress['+countAddress+'].consignee" class="text countAddress" btn-fun="clear" /> </td>'
                    +'<th> 收货人电话: </th> <td> <input type="text" name="storeAddress['+countAddress+'].mobile" class="text  countAddress" btn-fun="clear" /> </td>'
                    +'<th></th> <td></td><th></th> <td> <a href="javascript:;" class="button sureButton" onclick="deleteAddress(this)">删除</a></td></tr>'
                    +'<tr> <th> 收货地区: </th> <td colspan="3"> <span class="fieldSet" style="width:338px;display: block;">'
                    +'<input type="hidden" class="countAddress" id="areaId'+countAddress+'" name="storeAddress['+countAddress+'].area.id" value="" treePath="" /> </span> </td>'
                    +'<th> 收货地址: </th> <td colspan="3"> <input type="text" name="storeAddress['+countAddress+'].address" class="text countAddress" btn-fun="clear" maxlength="200" value="" />'
                    +'</td> </tr>';
                $("#addressAppend").parent().append(html);
                 $("#areaId"+countAddress).lSelect();
                 countAddress=parseInt(countAddress)+1;
                 limitAddress=parseInt(limitAddress)+1; */
            $mmGrid.addRow();
        }

        /* function deleteAddress(ele){
            $(ele).parent().parent().next().remove();
            $(ele).parent().parent().remove();
             limitAddress=parseInt(limitAddress)-1;
        } */
        function deleteStoreContract(e) {
            var index = $(e).closest("tr").index();
            $.message_confirm('您确定要删除吗？', function () {
                $mmGrid8.removeRow(index);
            })
        }

        function deleteCooperation(e) {
            var index = $(e).closest("tr").index();
            $.message_confirm('您确定要删除吗？', function () {
                $cooperation_mmGrid.removeRow(index);
            })
        }

        function deleteAddress(e) {
            var index = $(e).closest("tr").index();
            $.message_confirm('您确定要删除吗？', function () {
                $mmGrid.removeRow(index);
            })
        }

        function deleteStoreManager(e) {
            var index = $(e).closest("tr").index();
            $.message_confirm('您确定要删除吗？', function () {
                $mmGrid1.removeRow(index);
            })
        }

        function deleteInvoice(e) {
            var index = $(e).closest("tr").index();
            $.message_confirm('您确定要删除吗？', function () {
                $invoice_mmGrid.removeRow(index);
            })
        }

        function deleteProductType(e) {
            var index = $(e).closest("tr").index();
            $.message_confirm('您确定要删除吗？', function () {
                $mmGrid2.removeRow(index);
            })
        }

        function deleteBusinessRecord(e) {
            var index = $(e).closest("tr").index();
            $.message_confirm('您确定要删除吗？', function () {
                $mmGrid3.removeRow(index);
            })
        }

        function deleteCautionMoney(e) {
            var index = $(e).closest("tr").index();
            $.message_confirm('您确定要删除吗？', function () {
                $mmGrid4.removeRow(index);
                editCautionMoney();
            })
        }

        //
        function chooseOne(domEl) {
            var $this = $(domEl).find("input[type='checkbox']");
            var $tr = $this.closest("table");
            $tr.find("input.isDefaultCheckbox").prop("checked", false);
            $this.prop("checked", true);
            $tr.find("input.isDefault").val("false");
            $(domEl).find("input.isDefault").val("true");
            var $contact = $("input[name='contact']");
            var $thisTr = $this.closest("tr");
            var $contractNameVal = $thisTr.find("input.storeContractName").val();
            $contact.attr("value", $contractNameVal);
        }

        function showLegalPerson(e) {
            var $tr = $(e).closest("tr");
            var $isDefaultValue = $tr.find("input.isDefaultCheckbox").prop("checked");
            ;
            var $val = $(e).val();
            if ($isDefaultValue) {
                var $contact = $("input[name='contact']");
                $contact.attr("value", $val);
            }
        }

        function getMessage(e) {
            if ($("#name").val() == null || $("#name").val() == '') {
                $.message_alert('${message("请先填写客户名称")}');
                return false;
            }
            ajaxSubmit($(e), {
                method: 'get',
                url: "getMessage.jhtml?name=" + $("#name").val(),
                callback: function (data) {
                    var json_m = $.parseJSON(data.content);
                    console.log(json_m);
                    if (json_m.error_code == 300000) {
                        $.message_alert('${message("不存在该客户名称的信息")}');
                        return false;
                    }
                    if (json_m.error_code == 0) {
                        var map = json_m.result;
                        $.dialog({
                            title: "企业注册信息",
                            content: '<table id="message_content"></table>',
                            width: 950,
                            height: 400,
                            onShow: function () {
                                var message_items = [{"name": "注册号", "value": map.regNumber}, {
                                    "name": "注册地址",
                                    "value": map.regLocation
                                },
                                    {"name": "纳税人识别号", "value": map.taxNumber}, {
                                        "name": "法人",
                                        "value": map.legalPersonName
                                    },
                                    {"name": "登记机关", "value": map.regInstitute}, {"name": "行业", "value": map.industry},
                                    {"name": "经营范围", "value": map.businessScope}, {
                                        "name": "公司类型",
                                        "value": map.companyOrgType
                                    }];
                                var cols = [
                                    {title: '${message("信息名称")}', name: 'name', align: 'center', width: 100},
                                    {title: '${message("信息内容")}', name: 'value', align: 'center', width: 700},
                                ];
                                $message_mmGrid = $('#message_content').mmGrid({
                                    height: 'auto',
                                    cols: cols,
                                    items: message_items,
                                    checkCol: true,
                                    autoLoad: true,
                                    fullWidthRows: true
                                });
                            },
                            onOk: function () {
                                var rows = $message_mmGrid.selectedRows();
                                if (rows.length > 0) {
                                    var content = '';
                                    for (var i = 0; i < rows.length; i++) {
                                        var row = rows[i];
                                        if (i == rows.length - 1) {
                                            content += row.name + ':' + row.value;
                                        } else {
                                            content += row.name + ':' + row.value + ';  ';
                                        }
                                    }
                                    $("#introduction").val(content);
                                }
                            }
                        });
                    } else {
                        $.message_alert('${message("获取信息失败，请联系管理员")}');
                        return false;
                    }
                }
            });
        }


        function initProductTypeMmgrid() {
            var itemsp = [];
            //经营品类
            var colsp = [
                {
                    title: '${message("大类")}',
                    align: 'center',
                    name: "name",
                    width: 130,
                    renderer: function (val, item, rowIndex) {
                        var html = '<span class="productType' + item.bid + '"> </span>';
                        return html + '<input type="hidden" name="businessCategoryApplys[' + rowIndex + '].productBigType.id"  value="' + item.id + '" class="text countAddress" btn-fun="clear" />' + val;

                    }
                },
                {
                    title: '${message("中类")}',
                    align: 'center',
                    width: 130,
                    name: "cname",
                    renderer: function (val, item, rowIndex) {

                        var cid = item.cid;
                        if (cid == "undefined" || typeof (cid) == "undefined" || cid == null) {
                            cid = "";
                        }
                        var html = '<span class="search" style="position:relative">'
                            + '<input type="hidden" class="productCenterTypeId" name="businessCategoryApplys[' + rowIndex + '].productCenterType.id"  value="' + cid + '" >'
                            + '<input class="text productCenterTypeName" maxlength="200" type="text"  value="' + item.cname + '"  readonly>'
                            + '<input type="button" class="iconSearch" value=""  onclick="findChildrenCategory(this,' + item.id + ')" line_index="' + rowIndex + '">'
                            + '</span>';

                        return html;

                    }
                },
                {
                    title: '${message("操作")}', name: '', align: 'center', renderer: function (val, item, rowIndex) {
                        return '<a href="javascript:;" class="btn-delete" onclick="deleteProductType(this)">删除</a>'
                    }
                }

            ];
            $mmGrid2 = $('#table-p1').mmGrid({
                height: 'auto',
                cols: colsp,
                items: itemsp,
                fullWidthRows: true,
                checkCol: false,
                autoLoad: true
            });
        }

        function initCautionMoneyMmgrid() {
            var itemsc = [];
            //经营品类
            var colsc = [
                /* 		{ title:'${message("编号")}', align:'center', name:'sn',width:130 ,renderer: function(val,item,rowIndex){
			return '';
		}}, */
                {
                    title: '${message("账户类型")}',
                    align: 'center',
                    name: 'account_type',
                    width: 130,
                    renderer: function (val, item, rowIndex) {
                        var html = '<select name="cautionMoneieApplys[' + rowIndex + '].accountType" class="text">'
                            + '<option value="2">品牌保证金账户</option>'
                            + '<option value="1" >现金账户</option> '
                            + '<option value="3">返利账户</option>';
                        return html;
                    }
                },
                {
                    title: '${message("应缴")}',
                    align: 'center',
                    name: 'payable',
                    width: 110,
                    renderer: function (val, item, rowIndex) {
                        var html = '<div class="nums-input ov fl" style="width: 108px;"> '
                            + ' <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editCautionMoney()">'
                            + ' <input type="text"  class="t payable"  name="cautionMoneieApplys[' + rowIndex + '].payable" value="" minData="0" maxdata="********" onBlur="editCautionMoney()" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'
                            + ' <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editCautionMoney()">'
                            + ' </div>';

                        return html;
                    }
                },
                {
                    title: '${message("实缴")}',
                    align: 'center',
                    name: 'paid_in',
                    width: 110,
                    renderer: function (val, item, rowIndex) {
                        var html = '<div class="nums-input ov fl" style="width: 108px;"> '
                            + ' <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editCautionMoney()">'
                            + ' <input type="text"  class="t paidIn"  name="cautionMoneieApplys[' + rowIndex + '].paidIn" value="" onBlur="editCautionMoney()" minData="0" maxdata="********" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'
                            + ' <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editCautionMoney()">'
                            + ' </div>';

                        return html;
                    }
                },
                {
                    title: '${message("欠缴")}',
                    align: 'center',
                    name: 'unpaid',
                    width: 110,
                    renderer: function (val, item, rowIndex) {
                        var html = ' <input type="text"  class="text unpaid"  name="cautionMoneieApplys[' + rowIndex + '].unpaid" readonly="readonly"  >'

                        return html;
                    }
                },
                {
                    title: '${message("缴纳说明")}',
                    align: 'center',
                    name: 'paidNote',
                    width: 110,
                    renderer: function (val, item, rowIndex) {
                        var html = '<input  class="text" name="cautionMoneieApplys[' + rowIndex + '].paidNote" value="" btn-fun="clear"/>';
                        return html;
                    }
                },
                {
                    title: '${message("创建时间")}',
                    align: 'center',
                    name: 'create_date',
                    width: 130,
                    renderer: function (val, item, rowIndex) {
                        return '<input name="cautionMoneieApplys[' + rowIndex + '].createDate" class="text" value="${nowDate}"  type="text" btn-fun="clear" readonly="readonly"/>'
                    }
                },
                {
                    title: '${message("操作")}', name: '', align: 'center', renderer: function (val, item, rowIndex) {
                        return '<a href="javascript:;" class="btn-delete" onclick="deleteCautionMoney(this)">删除</a>'
                    }
                }

            ];
            $mmGrid4 = $('#table-p3').mmGrid({
                height: 'auto',
                cols: colsc,
                items: itemsc,
                fullWidthRows: true,
                checkCol: false,
                autoLoad: true,
                callback: function () {
                    editCautionMoney();
                }
            });
        }


        function initBusinessRecordMmgrid() {
            var itemsb = [];
            //经营品类
            var colsb = [
                {
                    title: '${message("奖惩日期")}',
                    align: 'center',
                    name: 'warn_or_award_date',
                    width: 130,
                    renderer: function (val, item, rowIndex) {

                        return '<input name="businessRecordApplys[' + rowIndex + '].warnOrAwardDate" class="text" value="" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>'
                    }
                },
                {
                    title: '${message("奖励分类")}',
                    align: 'center',
                    name: 'award_type',
                    width: 130,
                    renderer: function (val, item, rowIndex) {
                        var html = '<select name="businessRecordApplys[' + rowIndex + '].awardType" class="text">'
                            + '<option value=""></option>'
                            + '<option value="1">门店返利</option> '
                            + '<option value="2">奖车</option>'
                            + ' <option value="3">其他</option>';
                        return html;
                    }
                },
                {
                    title: '${message("惩罚分类")}',
                    align: 'center',
                    name: 'warn_type',
                    width: 130,
                    renderer: function (val, item, rowIndex) {
                        var html = '<select name="businessRecordApplys[' + rowIndex + '].warnType" class="text">'
                            + '<option value=""></option>'
                            + '<option value="1">侵权</option>'
                            + ' <option value="2">窜货</option> '
                            + '<option value="3">其他</option>';
                        return html;
                    }
                },
                {
                    title: '${message("奖惩事由")}',
                    align: 'center',
                    name: 'reason',
                    width: 130,
                    renderer: function (val, item, rowIndex) {
                        return '<input class="text" name="businessRecordApplys[' + rowIndex + '].reason" value="" />';
                    }
                },
                {
                    title: '${message("公司发出函件")}',
                    align: 'center',
                    width: 130,
                    name: 'letters',
                    renderer: function (val, item, rowIndex) {
                        var html = '<select name="businessRecordApplys[' + rowIndex + '].letters" class="text">'
                            + '<option value=""></option>'
                            + '<option value="1">整改通知函</option> '
                            + '<option value="2">不续约通知</option>'
                            + ' <option value="3">整改通知书</option>'
                            + '<option value="4">终止通知书</option>';
                        return html;

                    }
                },
                {
                    title: '${message("发出时间")}',
                    align: 'center',
                    width: 130,
                    name: 'send_date',
                    renderer: function (val, item, rowIndex) {

                        return '<input name="businessRecordApplys[' + rowIndex + '].sendDate" class="text" value="" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
                    }
                },
                {
                    title: '${message("经销商收到时间")}',
                    align: 'center',
                    name: 'receive_date',
                    width: 130,
                    renderer: function (val, item, rowIndex) {

                        return '<input name="businessRecordApplys[' + rowIndex + '].receiveDate" class="text" value="" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
                    }
                },
                {
                    title: '${message("处理档案编号")}',
                    align: 'center',
                    name: 'sn',
                    width: 130,
                    renderer: function (val, item, rowIndex) {
                        return '';
                    }
                },
                {
                    title: '${message("金额")}',
                    align: 'center',
                    name: 'amount',
                    width: 150,
                    renderer: function (val, item, rowIndex) {
                        var html = '<div class="nums-input ov fl" style="width: 163px;"> '
                            + ' <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">'
                            + ' <input type="text"  class="t"  name="businessRecordApplys[' + rowIndex + '].amount" value="" minData="0" maxdata="********" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'
                            + ' <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">'
                            + ' </div>';

                        return html;
                    }
                },
                {
                    title: '${message("备注")}',
                    align: 'center',
                    name: 'note',
                    width: 130,
                    renderer: function (val, item, rowIndex) {
                        return '<input class="text" name="businessRecordApplys[' + rowIndex + '].note" value="" />';
                    }
                },
                {
                    title: '${message("操作")}', name: '', align: 'center', renderer: function (val, item, rowIndex) {
                        return '<a href="javascript:;" class="btn-delete" onclick="deleteBusinessRecord(this)">删除</a>'
                    }
                }

            ];
            $mmGrid3 = $('#table-p2').mmGrid({
                height: 'auto',
                cols: colsb,
                items: itemsb,
                fullWidthRows: true,
                checkCol: false,
                autoLoad: true
            });
        }

        function initMmGrid() {
            if ($("#productClick").val() == 1) {
                initProductTypeMmgrid();
                initBusinessRecordMmgrid();
                initCautionMoneyMmgrid()
                $("#productClick").val(0);
            }
        }

        function findChildrenCategory(e, id) {
            //查询产品分类
            var url = 'http://cloud.etwowin.com.cn/product/product_category/findChildren.jhtml?id=' + id;
            var $search = $(e).closest(".search");
            var iframeId = "iframeId" + (new Date()).valueOf() + Math.floor(Math.random() * 1000000);
            var $dialog = $.dialog({
                title: '${message("查询子级分类")}',
                width: 1200,
                height: 508,
                content: "<iframe  id='" + iframeId + "' src='" + url + "' width='100%'  height='" + 420 + "px'><\/iframe>",
                onOk: function () {
                    var rows = $("#" + iframeId)[0].contentWindow.childMethod();
                    var elem = $(".productType" + rows[0].id);
                    if (elem.length == 0) {
                        $search.find(".productCenterTypeId").val(rows[0].id);
                        $search.find(".productCenterTypeName").val(rows[0].name);
                        $search.addClass("productType" + rows[0].id);
                    } else {
                        $.message_alert('${message("此分类已添加")}');
                        return false;
                    }
                }
            });
        }

        
        /*
         *处理城市等级，选择对应的等级改变应缴品牌保证金
         */
        function handleAccountType(e) {
        	var arr = [];
       		[#list storeAccount as account]
	        	var atc = new Object();
       			atc.value = "${account.value}";
       			atc.money = "${account.remark}";
       			arr.push(atc);
       		[/#list]
       		//console.log(arr);	
            var typeVal = $('#accountTypeCode option:selected').text();
            for(i=0;i<arr.length;i++){
            	var obj = arr[i];
            	if( typeVal == obj.value ){
	            	$('#needCautionPaid').val(obj.money);
	                $('#needCautionPaid').trigger('input');
            	}
            }
        }

        /**
         * 身份证输入控制
         * @param e
         */
        function checkIdCard(e) {
            var validStr = '0123456789xX'
            var str = e.value;
            var strArray = str.split('')
            var last = strArray[strArray.length-1]
            if(validStr.indexOf(last) == -1){
                strArray[strArray.length-1] = ''
                str = strArray.join('')
                $(e).val(str)
            }
        }
        //删除弹框选中的值
        function newclosePro(e,classname,inputname){
			$(e).closest("div").remove();
			var allName2 = '';
			$("."+classname+" > div").each(function(){
				allName2 = allName2 +','+  $(this).find("p").html();
			})
			$("input[name='"+inputname+"']").attr("value",allName2)
		}
		
		/**
		 * 初始化附件，每个参数都是必填的
		 * @param paramAttachs 后台实体类接收附件的参数名
		 * @param addAttachIdName 前端页面添加附件按钮的id值
		 * @param tableIdName 前端页面table中的id值 
		 * @param type 后台用来区分不同类型附件
		 */
	    var index = 0;
		function initAttach(paramAttachs, addAttachIdName, tableIdName, type) {
			var attachCols = [				
		    	{ title:'${message("附件")}',name:'content',width:260,align:'center',renderer:function(val,item,rowIndex){
		    		var url = item.url;
					var fileObj = getfileObj(item.name);
					/**设置隐藏值*/
					var hideValues = {};
					hideValues[paramAttachs+'['+index+'].url']=url;
					hideValues[paramAttachs+'['+index+'].suffix']=fileObj.suffix;
					hideValues[paramAttachs+'['+index+'].type']=type;  // 装修验收照片附件1
					
					return createFileStr({
						url : url,
						fileName : fileObj.file_name,
						name : fileObj.name,
						suffix : fileObj.suffix,
						time : '',
						textName: paramAttachs+'['+index+'].name',
						hideValues:hideValues
					});
		    	}},
				{ title:'${message("备注")}', name:'memo' ,width:580 ,align:'center', renderer: function(val,item,rowIndex){
					return '<div><textarea class="text" name="'+paramAttachs+'['+index+'].memo" >'+val+'</textarea></div>';
				}},
		    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
		    		index++;
					return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
				}}
			];
			var $grid=$('#'+tableIdName).mmGrid({
				fullWidthRows:true,
				height:'auto',
		        cols: attachCols,
		        checkCol: false,
		        autoLoad: true
		    });
		    
		    var $addAttach = $("#"+addAttachIdName);
			var attachIdnex = 0;
			var option = {
				dataType: "json",
			    uploadToFileServer:true,
			    uploadSize: "fileurl",
		        callback : function(data){
		        	var date = new Date();
					var year = date.getFullYear();
					var month = date.getMonth()+1;
					var day = date.getDate();
					var time = year+'-'+month+'-'+day;
			        for(var i=0;i<data.length;i++){
						var row = data[i].file_info;
						$grid.addRow(row,null,1);
			        }
		        }
		    }
		    $addAttach.file_upload(option);
			
			/* 删除附件 */
		    var $deleteAttachment = $(".deleteAttachment");
			$deleteAttachment.live("click", function() {
				var $this = $(this);
				$this.closest("tr").remove();
			});
		}

    </script>
</head>
<body>
<div class="pathh">
    &nbsp;${message("新增客户加盟信息")}
</div>
<form id="inputForm" action="/member/storeApply/save.jhtml" method="post" type="ajax" validate-type="validate">
    <input type="hidden" id="productClick" value="1"/>
    <ul id="tab" class="tab">
        <li>
            <input type="button" value="${message("基本信息")}"/>
        </li>
        <!-- <li>
				<input type="button" onclick="initMmGrid()" value="${message("其他信息")}" />
			</li> -->
    </ul>
    
    <div class="tabContent">
        <!--基本信息开始-->
        <table class="input input-edit">
            <!--
			<tr>
                <th>${message("单号")}:</th>
                <td></td>
                <<th>${message("单据状态")}:</th>
                <td></td> 
		    </tr>
		    -->
            <tr>
                <th><span class="requiredField">*</span>${message("区域")}:</th>
                <td>
                	<span class="region"></span>
                	<input type="hidden" name="region" value=""/>
                </td>
                <th><span class="requiredField">*</span>${message("地区")}:</th>
                <td colspan="3">
                    <input type="hidden" id="newAreaId" name="headNewArea.id"/>
                </td>
                <th>${message("乡镇")}:</th>
                <td>
                    <input type="text" class="text" name="countryName" value="" btn-fun="clear"/>
                </td>
            </tr>
            <tr>
                <!--
				<th>${message("授权编号")}:</th>
				<td>
					<input type="text" name="grantCode" class="text" value="${store.grantCode}" btn-fun="clear" maxlength="200" />
				</td>
				-->
                <th><span class="requiredField">*</span>${message("经销商姓名")}:</th>
                <td>
                    <input type="text" name="dealerName" class="text" value="" btn-fun="clear" maxlength="200"/>
                </td>
                <th><span class="requiredField">*</span>${message("经销商性别")}:</th>
                <td>
                    <select name="dealerSex" class="text">
                        <option value=""></option>
                        <option value="0">男</option>
                        <option value="1">女</option>
                    </select>
                </td>
                <th><span class="requiredField">*</span>${message("城市等级")}:</th>
                <td>
                    <select name="accountTypeCode" id="accountTypeCode" class="text" onchange="handleAccountType(this)">
                        <option value=""></option>
                        <option value="0">${message("省级")}</option>
                        <option value="1">${message("地市级")}</option>
                        <option value="2">${message("区县级")}</option>
                        <option value="3">${message("乡镇级")}</option>
                    </select>
                </td>
                <th><span class="requiredField">*</span>${message("机构")}:</th>
                <td>
                    <span class="search" style="position:relative">
                        <input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value=""/>
                        <input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200"
                               onkeyup="clearSelect(this)" value="" readOnly/>
                        <input type="button" class="iconSearch" value="" id="selectSaleOrg"/>
                    </span>
                </td>
            </tr>
            <tr>
                <th><span class="requiredField">*</span>${message("客户名称")}:</th>
                <td>
                    <input type="text" name="name" id="name" class="text" placeholder="**省**市**县经销商+姓名" maxlength="200" btn-fun="clear"/>
                </td>
                <th><span class="requiredField">*</span>${message("手机号")}:</th>
                <td>
                    <input type="text" class="text" name="headPhone" value="" maxlength="11" oninput="editQty (this,event)"
                           btn-fun="clear"/>
                </td>
                <th><span class="requiredField">*</span>${message("经销商学历")}:</th>
                <td>
                    <!-- <input type="text" name="dealerGrade" class="text" value="" btn-fun="clear" maxlength="200" /> -->
                    <select name="dealerGrade" class="text">
                        <option value=""></option>
                        <option value="小学">${message("小学")}</option>
                        <option value="中学">${message("中学")}</option>
                        <option value="大专">${message("大专")}</option>
                        <option value="本科">${message("本科")}</option>
                        <option value="研究生">${message("研究生")}</option>
                        <option value="硕士">${message("硕士")}</option>
                        <option value="博士">${message("博士")}</option>
                        <option value="博士后">${message("博士后")}</option>
                    </select>
                </td>
                <th><span class="requiredField">*</span>${message("业务类型")}:</th>
                <td>
                    <select id="businessTypeId" name="businessTypeId" class="text">
                    	<option value=""></option>
                        [#list businessTypes as businessType]
                            <option value="${businessType.id}">${businessType.value}</option>
                        [/#list]
                    </select>
                </td>
            </tr>
            <tr>
                <th><span class="requiredField">*</span>${message("客户简称")}:</th>
                <td>
                    <input type="text" name="alias" class="text " placeholder="**市**县+姓名" maxlength="200" btn-fun="clear"/>
                </td>
                <th>${message("固定号码")}:</th>
                <td>
                    <input type="text" class="text" name="fixedNumber" maxlength="11" value="" oninput="editQty (this,event)"
                           btn-fun="clear"/>
                </td>
                <th>${message("公司性质")}:</th>
                <td>
                    <select name="companyType" class="text">
                    	<option value=""></option>
                        <option value="0">独立公司</option>
                        <option value="1">合伙公司</option>
                        <option value="2">个体工商户</option>
                    </select>
                </td>
                <th>${message("总经销商")}:</th>
                <td>
                    <input type="text" class="text" name="franchisee" value="" btn-fun="clear"/>
                </td>
            </tr>
            <tr>
                <th><span class="requiredField">*</span>${message("身份证信息")}:</th>
                <td>
                    <input type="text" class="text" name="identity" value="" oninput="checkIdCard (this)" btn-fun="clear"/>
                </td>
                <th><span class="requiredField">*</span>${message("平台性质")}:</th>
                <td>
                    <select name="platformProperty" class="text platformProperty"  >
                        <option value=""></option>
                        [#list saleOrgTypes as saleOrgType]
                            <option value="${saleOrgType.id}" >${saleOrgType.value}</option>
                        [/#list]
                    </select>
                </td>
                <th><span class="requiredField">*</span>${message("销售平台")}:</th>
                <td>
					<span class="search" style="position:relative">
					<input type="hidden" name="salesPlatformId" class="text salesPlatformId" btn-fun="clear" value=""/>
					<input type="text" name="salesPlatformName" class="text salesPlatformName" maxlength="200"
                           onkeyup="clearSelect(this)" value="" readOnly style="border:0px;"/>
					</span>
                </td>
                <th><span class="requiredField">*</span>${message("区域经理")}:</th>
                <td>
				 	<span class="search" style="position:relative">
                    <input type="hidden" name="storeMemberId" class="text storeMemberId"
                           [#if "${storeMember}" != 0]value="${storeMember.id}" [#else]value=""[/#if] btn-fun="clear"/>
                    <input type="text" name="storeMemberName" class="text storeMemberName"
                           [#if "${storeMember}" != 0]value="${storeMember.name}" [#else]value=""[/#if] maxlength="200"
                           onkeyup="clearSelect(this)" readOnly/>
                    <input type="button" class="iconSearch" value="" id="openStore"/>
                    </span>
                </td>
            </tr>
            <tr>
                <th><span class="requiredField">*</span>${message("sbu")}:</th>
                <td>
                    <select id="sbu" name="sbuId" class="text sbuId">
                        <option value=""></option>
                        [#list sbus as sbu]
                            <option value="${sbu.id}">${sbu.value}</option>
                        [/#list]
                    </select>
                </td>
                <th><span class="requiredField">*</span>${message("法人代表")}:</th>
                <td>
                    <input type="text" class="text" name="contact" value="" btn-fun="clear"/>
                </td>
                <th><span class="requiredField">*</span>${message("销售区域")}:</th>
                <td>
					<span class="search" style="position:relative">
					<input type="hidden" name="storeApplySalesAreaId" id="salesAreaId" class="text salesAreaId" btn-fun="clear"
                           value=""/>
					<input type="text" name="storeApplySalesAreaName" class="text salesAreaName" id="salesAreaName" maxlength="200"
                           onkeyup="clearSelect(this)" value="" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectStoreSalesArea"/>
					</span>
                </td>
                <th>${message("区域经理电话")}:</th>
                <td>
                    <span class="storeMemberPhoneText">${storeMember.member.mobile}</span>
                    <input type="hidden" class="text storeMemberPhone" name="storeMemberPhone"
                           value="${storeMember.member.mobile}" btn-fun="clear"/>
                </td>
            </tr>
            <tr>
                [#--
                <th>${message("ERP客户编码")}:</th>
                <td>
                    <input type="hidden" name="outTradeNo" class="text " btn-fun="clear" maxlength="200" />
                </td>
                --]
                <th>${message("合同主体")}:</th>
                <td>
                    [#--					<select name="contractSubject" class="text  contractSubject">--]
                    [#--						<option value=""></option>--]
                    [#--						[#list contractList as contract]--]
                    [#--							<option value="${contract.contract_body}"  [#if store.contractSubject == contract.contract_body]selected[/#if]>${contract.contract_body}</option>--]
                    [#--						[/#list]--]
                    [#--					</select>--]
                    <input type="text" name="contractSubject" class="text" value="${store.contractSubject}"
                           btn-fun="clear" maxlength="200"/>
                </td>
                <th>${message("经销商关系说明")}:</th>
                <td>
                    <input type="text" class="text" name="dealerRelationShip" value="" btn-fun="clear"/>
                </td>
                [#--
                <th>${message("设置")}:</th>
                <td>
                <label>
                    <input type="checkbox" name="isEnabled" value="true" checked="checked"/>${message("是否启用")}
                    <input type="hidden" name="_isEnabled" value="false" />
                </label>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <input type="hidden" name="isReduceBalance" value="true" />
                <!-- ${message("订单下达是否校验可发货余额")}:
                <label>
                    <input type="checkbox" name="isReduceBalance" value="true" checked="checked"/>${message("检验")}
                    <input type="hidden" name="_isReduceBalance" value="false"/>
                    </label> -->
                </td>
                --]
                [#--

                --]
                <th><span class="requiredField">*</span>${message("价格类型")}:</th>
                <td>
				<span class="search" style="position:relative">
					 <input type="hidden" name="memberRankId" class="text memberRankId" id="memberRankId"
                            btn-fun="clear"/>
					 <input type="text" name="memberRankName" class="text memberRankName" maxlength="200"
                            onkeyup="clearSelect(this)" id="memberRankName" readOnly/>
					 <input type="button" class="iconSearch" value="" id="selectMemberRank"/>
				</span>
                </td>

                <th><span class="requiredField">*</span>${message("经销商地址")}:</th>
                <td colspan="3">
                    <input type="text" class="text" name="headAddress" value="" btn-fun="clear"/>
                </td>
            </tr>
            <tr>
            	<th>
            		<span class="requiredField">*</span>${message("经营组织(多选)")}:
            	</th>
            	<td>
            		<span class="search" style="position:relative">
                        <input type="text" name="organizationName" class="text organizationName" maxlength="200"
                               onkeyup="clearSelect(this)" value=",${osName}" readOnly/>
                        <input type="button" class="iconSearch" value="" id="selectOrganization"/>
                        <div class="pupTitleName  organizations">
                        	[#list os as o]
                        	<div><input name="organizationIds" class="text organizationIds organizationIds_${o.id}" type="hidden" value="${o.id}"><p>${o.name}</p><i class="close" onclick="newclosePro(this,'organizations','organizationName')"></i></div>
	                        [/#list]
                        </div>
                    </span>
            	</td>
            	<th><span class="requiredField">*</span>${message("经销商类别")}:</th>
				<td>
					<select class="text" name="category" id="category"> 
						<option></option>
						<option value="0">${message("一城多商")}</option>
						<option value="1">${message("一城一商")}</option>
					</select>
				</td>
            </tr>
            
        </table>
        <div class="title-style">${message("经销商资质")}:</div>
        <table class="input input-edit">
            <tr>
                <th><span class="requiredField">*</span>${message("经销商背景")}:</th>
                <td colspan="3">
                    <label>
                    	<input type="radio" name="dealerBackground" class="dealerBackground" value="4"/>
                    	经营多品类品牌
                    </label>&nbsp;&nbsp;
                    <label>
                    	<input type="radio" name="dealerBackground" class="dealerBackground" value="1"/>
                    	 只经营地板
                    </label>&nbsp;&nbsp;
                    <label>
                    	<input type="radio" name="dealerBackground" class="dealerBackground" value="2"/>
                    	非建材销售商家
                    </label>&nbsp;&nbsp;
                    <label>
                    	<input type="radio" name="dealerBackground" class="dealerBackground" value="3"/>
                    	其他
                    </label>
                    <input class="t acreage" name="dealerBackgroundMemo" type="text" style="width:100px;border-bottom:1px solid #dcdcdc;text-align:center;"/>    
                           
                </td>
                <th><span class="requiredField">*</span>${message("经销商等级")}:</th>
                <td colspan="3">
                    <label><input type="radio" name="dealerLevel" value="0"/>资金雄厚，有建材经验</label>&nbsp;&nbsp;&nbsp;
                    <label><input type="radio" name="dealerLevel" value="1"/>资金一般，有潜力</label>&nbsp;&nbsp;&nbsp;
                    <label><input type="radio" name="dealerLevel" value="2"/>资金紧凑，可扶持</label>&nbsp;&nbsp;&nbsp;
                    <label><input type="radio" name="dealerLevel" value="3"/>其他</label>
                </td>
            </tr>
            <tr>
                <th><span class="requiredField">*</span>${message("宣传意识")}:</th>
                <td>
                    <label><input type="radio" name="propagandaAwareness" value="0"/>优</label>&nbsp;&nbsp;
                    <label><input type="radio" name="propagandaAwareness" value="1"/>良</label>&nbsp;&nbsp;
                    <label><input type="radio" name="propagandaAwareness" value="2"/>一般</label>&nbsp;&nbsp;
                    <label><input type="radio" name="propagandaAwareness" value="3"/>差</label>&nbsp;&nbsp;
                    <label><input type="radio" name="propagandaAwareness" value="4"/>无</label>
                </td>
                <th><span class="requiredField">*</span>${message("品牌意识")}:</th>
                <td>
                    <label><input type="radio" name="brandAwareness" value="0"/>优</label>&nbsp;&nbsp;
                    <label><input type="radio" name="brandAwareness" value="1"/>良</label>&nbsp;&nbsp;
                    <label><input type="radio" name="brandAwareness" value="2"/>一般</label>&nbsp;&nbsp;
                    <label><input type="radio" name="brandAwareness" value="3"/>差</label>&nbsp;&nbsp;
                    <label><input type="radio" name="brandAwareness" value="4"/>无</label>
                </td>
                <th><span class="requiredField">*</span>${message("售后服务")}:</th>
                <td>
                    <label><input type="radio" name="afterService" value="0"/>优</label>&nbsp;&nbsp;
                    <label><input type="radio" name="afterService" value="1"/>良</label>&nbsp;&nbsp;
                    <label><input type="radio" name="afterService" value="2"/>一般</label>&nbsp;&nbsp;
                    <label><input type="radio" name="afterService" value="3"/>差</label>&nbsp;&nbsp;
                    <label><input type="radio" name="afterService" value="4"/>无</label>
                </td>
            </tr>
            <tr>
                <th><span class="requiredField">*</span>${message("销售渠道")}:</th>
                <td colspan="7">
                    <label>
                        <span>零售</span>
                        <input class="t acreage" name="salesChannelsVal1" value="" mindata="0" maxdata="********"
                               oninput="editQty(this,event)" onpropertychange="editQtyEmpty(this,event)" type="text"
                               style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
                        <span>%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    </label>
                    <label>
                        <span>装饰公司</span>
                        <input class="t acreage" name="salesChannelsVal2" value="" mindata="0" maxdata="********"
                               oninput="editQty(this,event)" onpropertychange="editQtyEmpty(this,event)" type="text"
                               style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
                        <span>%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    </label>
                    <label>
                        <span>电商</span>
                        <input class="t acreage" name="salesChannelsVal3" value="" mindata="0" maxdata="********"
                               oninput="editQty(this,event)" onpropertychange="editQtyEmpty(this,event)" type="text"
                               style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
                        <span>%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    </label>
                    <label>
                        <span>工程</span>
                        <input class="t acreage" name="salesChannelsVal4" value="" mindata="0" maxdata="********"
                               oninput="editQty(this,event)" onpropertychange="editQtyEmpty(this,event)" type="text"
                               style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
                        <span>%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    </label>
                    <label>
                        <span>促销活动</span>
                        <input class="t acreage" name="salesChannelsVal5" value="" mindata="0" maxdata="********"
                               oninput="editQty(this,event)" onpropertychange="editQtyEmpty(this,event)" type="text"
                               style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
                        <span>%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    </label>
                    <label>
                        <span>其他</span>
                        <input class="t acreage" name="salesChannelsVal6" value="" mindata="0" maxdata="********"
                               oninput="editQty(this,event)" onpropertychange="editQtyEmpty(this,event)" type="text"
                               style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
                        <span>%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    </label>
                </td>
            </tr>
            <tr>
                <th><span class="requiredField">*</span>${message("人员配置")}:</th>
                <td colspan="7">
                    <span>营销人员</span>
                    <input class="t acreage" name="marketersNumber" value="" mindata="0" maxdata="********"
                           oninput="editQtyEmpty(this,event)" onpropertychange="editQtyEmpty(this,event)" type="text"
                           style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
                    <span>名，</span>
                    <span>售后人员</span>
                    <input class="t acreage" name="afterSaleNumber" value="" mindata="0" maxdata="********"
                           oninput="editQtyEmpty(this,event)" onpropertychange="editQtyEmpty(this,event)" type="text"
                           style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
                    <span>名，</span>
                    <span>安装工人</span>
                    <input class="t acreage" name="installBodyNumber" value="" mindata="0" maxdata="********"
                           oninput="editQtyEmpty(this,event)" onpropertychange="editQtyEmpty(this,event)" type="text"
                           style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
                    <span>名，</span>
                    <span>仓库面积</span>
                    <input class="t acreage" name="warehouseArea" value="" mindata="0" maxdata="********"oninput="editQty(this,event)"
                           onpropertychange="editQty(this,event)" type="text"
                           style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
                    <span>㎡</span>
                </td>
            </tr>
            <tr>
                <th><span class="requiredField">*</span>${message("门店设施")}:</th>
                <td colspan="5">
                    <span>车辆配置：货车</span>
                    <input class="t acreage" name="truck" value="" mindata="0" maxdata="********" oninput="editQtyEmpty(this,event)"
                           onpropertychange="editQtyEmpty(this,event)" type="text"
                           style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
                    <span>辆， 小车</span>
                    <input class="t acreage" name="smallCar" value="" mindata="0" maxdata="********" oninput="editQtyEmpty(this,event)"
                           onpropertychange="editQtyEmpty(this,event)" type="text"
                           style="width:60px;border-bottom:1px solid #dcdcdc;text-align:center;"/>
                    <span>辆， 电脑/宽带：</span>
                    <label><input type="radio" name="pcOrBroadband" value="1"/>有</label>&nbsp;&nbsp;
                    <label><input type="radio" name="pcOrBroadband" value="0"/>没有</label>&nbsp;&nbsp;
                </td>
            </tr>
            <tr>
                <!-- <th>${message("客户介绍")}:</th>
				<td colspan="7">
					<textarea name="introduction" id="introduction" class="text"></textarea>
				</td> -->
                <th>${message("客户说明")}:</th>
                <td colspan="7">
                    <textarea name="introduction" id="introduction" class="text"></textarea>
                </td>
            </tr>

            <tr>
				<th>${message("提供经销商缴纳保证金凭证")}:</th>
        		<td>
        			<a href="javascript:;" id="addStoreAttach0" class="button">添加附件</a>
        		</td>
        		<th>${message("经销商加盟申请表")}:</th>
        		<td>
        			<a href="javascript:;" id="addStoreAttach1" class="button">添加附件</a>
        		</td>
        		<th>${message("一城多商")}:</th>
        		<td>
        			<a href="javascript:;" id="addStoreAttach2" class="button">添加附件</a>
        		</td>
        		<th></th>
        		<td></td>
			</tr>
        </table>
        <span>${message("提供经销商缴纳保证金凭证")}</span>
        <table id="table-store-attach0" style="width:850px"></table>
        <span>${message("经销商加盟申请表")}</span>
	    <table id="table-store-attach1" style="width:850px"></table>
	    <span>${message("一城多商")}</span>
	    <table id="table-store-attach2" style="width:850px"></table>
        <div class="title-style">${message("缴费")}:</div>
        <table class="input input-edit">
            <tr>
                <th>${message("应缴品牌保证金")}:</th>
                <td>
                    <input type="text" class="text" id="needCautionPaid" name="needCautionPaid" value="" minData="0" maxdata="********"
                           oninput="editAccSub(this,event)" onpropertychange="editAccSub(this,event)"
                           readonly="readonly" style="border:0px;text-align: center;"/>
                </td>
                <th>${message("实缴品牌保证金")}:</th>
                <td>
                    <div class="nums-input ov fl">
                        <input type="button" class="b decrease" value="-" onMouseDown="decrease(this,event)"
                               onMouseUp="editAccSub(this,event)"/>
                        <input type="text" class="t" id="realCautionPaid" name="realCautionPaid" value="" minData="0" maxdata="********"
                               oninput="editAccSub(this,event)" onpropertychange="editAccSub(this,event)"/>
                        <input type="button" value="+" class="b increase" onMouseDown="increase(this,event)"
                               onMouseUp="editAccSub(this,event)"/>
                    </div>
                </td>
                <th><span class="requiredField">*</span>${message("现金客户")}:</th>
                <td>
                    <label><input type="radio" name="cashClientFlag" value="true"/>是</label>&emsp;
                    <label><input type="radio" name="cashClientFlag" value="false"/>否</label>
                </td>
                <th>${message("货币")}:</th>
                <td>
                    <select name="currencyCode" class="text">
                        <option value="人民币">${message("人民币")}</option>
                    </select>
                </td>
            </tr>
            <tr>
                <th>${message("欠缴品牌保证金")}:</th>
                <td>
                    <input class="text" style="border: 0px;text-align:center;color:red;" name="unpaidCautionPaid"
                           id="unpaidCautionPaid" readonly="readonly"/>
                </td>
                <th>${message("门店数量")}:</th>
                <td>
                    <span id="numberStores"></span>
                </td>
                <th>${message("经销商类型")}:</th>
                <td>
                    <select name="distributorType" class="text">
                        <option value="0">${message("国内经销商")}</option>
                        <option value="1">${message("国际经销商")}</option>
                        <option value="2">${message("国产产品经销商")}</option>
                        <option value="3">${message("进口产品经销商")}</option>
                    </select>
                </td>
                <th>${message("销量保证金")}:</th>
                <td>
                    <div class="nums-input ov fl">
                        <input type="button" class="b decrease" value="-" onMouseDown="decrease(this,event)"/>
                        <input type="text" class="t" name="salesDeposit" value="" minData="0" maxdata="********"
                               oninput="editQty (this,event)" onpropertychange="editQty(this,event)"/>
                        <input type="button" value="+" class="b increase" onMouseDown="increase(this,event)"/>
                    </div>
                </td>
            </tr>
            <tr>
                <th><span class="requiredField">*</span>${message("经销商子类型")}:</th>
                <td>
                    <select name="subType" class="text">
                        <option value="">${message("请选择")}</option>
                        <option value="0">${message("总经销商")}</option>
                        <option value="1">${message("省会城市经销商")}</option>
                        <option value="3">${message("经销商")}</option>
                        <option value="4">${message("分销商")}</option>
                        <option value="5">${message("乡镇运营商")}</option>
                        <option value="6">${message("工程代理商")}</option>
                        <option value="7">${message("一城多商")}</option>
                    </select>
                </td>
                <th>${message("税率")}:</th>
                <td>
                    <div class="nums-input ov fl" style="width: 163px;">
                        <input type="button" class="b decrease" value="-" onMouseDown="decrease(this,event)"/>
                        <input type="text" class="t" name="taxRate" value="13" minData="0" maxdata="********"
                               oninput="editQty (this,event)" onpropertychange="editQty(this,event)"/>
                        <input type="button" value="+" class="b increase" onMouseDown="increase(this,event)"/>
                    </div>
                    <div style="line-height: 30px;width: 22px;" class="fl tc">%</div>
                </td>
                <th>${message("缴纳情况/异常说明")}:</th>
                <td>
                    <input type="text" class="text" name="paymentStatus" value="" btn-fun="clear"/>
                </td>
                <td class="hidden">
                    <select name="store_type">
                        [#list types as value]
                            <option value="${value}" [#if value==4]selected[/#if]>${message('11111111'+value)}</option>
                        [/#list]
                    </select>
                </td>
            </tr>
        </table>
        <table class="input input-edit" style="width:100%;margin-top:5px;">
            <tr class="barnk border-L1" id="addressAppend">
                <th><span class="requiredField">*</span>${message("添加收货地址")}</th>
                <td colspan="7">
                    <a href="javascript:;" class="button" id="addRecviceAddress">${message("添加")}</a>
                </td>
            </tr>
            <tr class="border-L1">
                <td colspan="8">
                    <div>
                        <table id="table-m1"></table>
                    </div>
                </td>
            </tr>
        </table>

</div>

<!-- 其他信息 -->
<div class="tabContent">
    <table class="input input-edit">
    
    </table>
    <table class="input input-edit" style="width:100%;margin-top:5px;">

</table>
        </div>
<div class="fixed-top">
    [#if companyInfoId==35]
    <input type="button" onclick="getMessage(this)" class="button sureButton" value="${message("查询企业注册信息")}" />
    [/#if]
    <input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
    <input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
</div>
</form>
</body>
</html>