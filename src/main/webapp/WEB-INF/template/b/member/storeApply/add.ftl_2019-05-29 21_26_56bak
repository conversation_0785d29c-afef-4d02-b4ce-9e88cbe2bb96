<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("客户信息")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<style>
.upload-list {width:82px;display: inline-block;padding-top:0px;margin-right:10px;}
</style>
<script type="text/javascript">
//这个是品牌保证金的回调函数
function editCaution(){
	var needCautionPaid = $("#needCautionPaid").val();
	var realCautionPaid = $("#realCautionPaid").val();
	if(needCautionPaid != "" && realCautionPaid != ""){
		$("#unpaidCautionPaid").val(needCautionPaid-realCautionPaid);
	}
}
//这个是保证金的回调函数
function editCautionMoney(){
	var $bInput = $("input.payable");
	$bInput.each(function(){
        var $tr = $(this).closest("tr");
        var payable=$(this).val();
        var paidIn=$tr.find(".paidIn").val();
    	if(payable != "" && paidIn != ""){
    		$tr.find(".unpaid").val((payable-paidIn).toFixed(2));
    	}
	});
}

function countCautionPaid(){
	var needCautionPaid = $("#needCautionPaid").val();
	var realCautionPaid = $("#realCautionPaid").val();
	if(isNaN(needCautionPaid)){
		needCautionPaid = 0;
	}
	if(isNaN(realCautionPaid)){
		realCautionPaid = 0;
	}
	$("#unpaidCautionPaid").val(accSub(needCautionPaid,realCautionPaid));
}

function editAccSub(t,e){
	if(extractNumber(t,0,true,e)){
		countCautionPaid();
	}
}

function editQty(t,e,n){
	var rate = 3;
	if(n!=undefined)rate = n;
	extractNumber(t,rate,false,e)
}
$().ready(function() {
	var $inputForm = $("#inputForm");
	var $areaId = $("#areaId");
	var $newAreaId = $("#newAreaId");
	var $bAreaId = $("#bAreaId");
	var $typeSelect = $("#typeSelect");
	var $onlineShopTypeTr = $(".onlineShopTypeTr");
	var $bankCardLi = $("#bankCardLi");
	var $addRecviceAddress = $("#addRecviceAddress");
	var $addStoreManager = $("#addStoreManager");
	var $addStoreContract = $("#addStoreContract");
	var $addSbu = $("#addSbu");
	var $storeImage = $("#storeImage");
	
	   
	 $("#openStore").click(function(){
	    	
	    	$("#openStore").bindQueryBtn({
		        type:'store',
		        bindClick:false,
		        title:'${message("查询用户")}',
		        url:'/member/store_member/select_store_member.jhtml?multi=2&memberType='+0,
		        callback:function(rows){
		            if(rows.length>0){
		            	var row = rows[0];
		            
		                $("input[name='storeMemberId']").attr("value",row.id)
		            	
		             $("input[name='storeMemberName']").attr("value",row.name);
		              
		            }
		        }   
		    });
	    
	    })
	    
	    
	    $("#selectMemberRank").live("click",function(){
		  var $this = $(this);
    	 var saleOrgId = $(".saleOrgId").val();
    	 if(saleOrgId==null || saleOrgId==''){
    		 $.message_alert('请先选择机构！');
             return false;
    	 }
    	 $this.bindQueryBtn({
	        type:'memberRank',
	        bindClick:false,
	        title:'${message("查询价格类型")}',
	        url:'/basic/member_rank/select_memberRank.jhtml?saleOrgId='+saleOrgId,
	        callback:function(rows){
	            if(rows.length>0){
	            	var row = rows[0];
	            	$this.closest("tr").find(".memberRankName").val(row.name);
					$this.closest("tr").find(".memberRankId").val(row.id);
	              
	            }
	        }   
	    });
    
    })
	
	var option1 = {
		dataType: "json",
	    uploadToFileServer:true,
	    fileNumLimit:1,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        var file_info = data[0].file_info;
	        var image = setting.file_image;
	        [@compress single_line = true]
	    	var tHtml='<span class="ico-options upload-list" >'+
								'<div class="ul-box" style="width:80px;height: 80px;padding:0px;margin:0px">'+
	                        	'<div class="pic" style="width:82px;height:82px"><a href="'+file_info.url+'" target="_blank" style="width: 80px;">'+
	                        		'<img src="/resources/images/icon_xls.png" style="width: 80px; height: 80px;">'+
	                        	'</a></div>'+
	                        	'<a class="del deleteStoreImage" href="#"></a>'+
	                        	'</div>'+
	                        	'<input type="hidden" name="storeImage" value="'+file_info.url+'"/>'+
							'</span>';
	    	[/@compress]                   
	        $("#storeImage").parent().before(tHtml);
		    $("#storeImage").parent().remove();
			
        }
    }
    $("#storeImage").file_upload(option1);
    
     var $deleteStoreImage = $(".deleteStoreImage");
	$deleteStoreImage.live("click", function() {
		var $this = $(this);
		var html = '<span><a href="javascript:;" class="a-upload addProductImage" style="width:80px;margin-right:10px;height:80px;" id="storeImage"></a></span>';
		$(this).closest("span").before(html);
		$(this).closest("span").remove();
		$("#storeImage").file_upload(option1);
	});
	
	$("#needCautionPaid").blur(function(){
		editCaution();
	});
	$("#realCautionPaid").blur(function(){
		editCaution();
	});
	var $storeImage2 = $("#storeImage2");
	var option2 = {
		dataType: "json",
	    uploadToFileServer:true,
	    fileNumLimit:1,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        var file_info = data[0].file_info;
	        var image = setting.file_image;
	        [@compress single_line = true]
	    	var tHtml='<span class="ico-options upload-list">'+
								'<div class="ul-box" style="width:80px;height: 80px;padding:0px;margin:0px">'+
	                        	'<div class="pic"  style="width:82px;height:82px"><a href="'+file_info.url+'" target="_blank" style="width: 80px;">'+
	                        		'<img src="/resources/images/icon_xls.png" style="width: 80px; height: 80px;">'+
	                        	'</a></div>'+
	                        	'<a class="del deleteStoreImage2" href="#"></a>'+
	                        	'</div>'+
	                        	'<input type="hidden" name="storeImage2" value="'+file_info.url+'"/>'+
							'</span>';
	    	[/@compress]                   
	        $("#storeImage2").parent().before(tHtml);
		   $("#storeImage2").parent().remove();
			
        }
    }
    $("#storeImage2").file_upload(option2);
    
     var $deleteStoreImage2 = $(".deleteStoreImage2");
	$deleteStoreImage2.live("click", function() {
		var $this = $(this);
		var html = '<span><a href="javascript:;" class="a-upload addProductImage" style="width:80px;height:80px;" id="storeImage2"></a></span>';
		$(this).closest("span").before(html);
		$(this).closest("span").remove();
		$("#storeImage2").file_upload(option2);
	});
	
	
	
	//地区选择
	$areaId.lSelect();
	$bAreaId.lSelect();
	$newAreaId.lSelect();
	$typeSelect.change(function(){
	  if($(this).val() == "onlineShop") {
	  	$onlineShopTypeTr.show();
	  	$onlineShopTypeTr.attr("disabled",false);
	  }
	  else {
	  	$onlineShopTypeTr.hide();
	  	$onlineShopTypeTr.attr("disabled","true");
	  }
	  if($(this).val() == "distributor") {
	 	$("input[name=isReduceBalance]").attr("checked",false);
	  	$("input[name=isReduceBalance]").attr("disabled",false);
	  }
	  else {
	  	$("input[name=isReduceBalance]").attr("checked",false);
	  	$("input[name=isReduceBalance]").attr("disabled",true);
	  }
	}); 
		
	// 表单验证
	
	

	// 表单验证
	$inputForm.validate({
		rules: {
    		saleOrgId: "required",
    		name: "required",
    		store_type:"required",
    		memberRankId: "required",
    		saleOrgName: "required",
    		memberRankName:"required",
    		alias:"required",
    		platformProperty:"required",
    		salesPlatformName:"required",
    		accountTypeCode:"required",
    		storeMemberId:"required",
    		salesAreaName:"required"
    					
    		/* accountTypeCode:"required",
    		platformProperty:"required",
    		salesPlatform:"required",
    		territoryName:"required",
    		dealerName:"required",
    		grantCode:"required" */
    		//areaId: "required",
    		//address: "required"
		},
		messages: {
			name: {
				pattern: "${message("非法字符")}",
				remote: "${message("已存在")}"
			}
		}
	});	
	$.validator.addClassRules({
		countAddress: {
			required: true,
			
		}
	});
	
	$("form").bindAttribute({
		isConfirm:true,
	    callback: function(resultMsg){
	        $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
				location.href= '/member/storeApply/edit/${code}.jhtml?id='+resultMsg.objx;
				
			})
	    }
	 });
	
	//查询产品分类
	$("#addStoreProductType").bindQueryBtn({
		type:'productType',
		title:'${message("查询产品分类 ")}',
		url:'/product/product_category/findTopCategory.jhtml',
		callback:function(row){
			$mmGrid2.addRow(row);
		}
	});
	
	//查询价格等级
//	$("#selectMemberRank").bindQueryBtn({
//		type:'memberRank',
//		title:'${message("查询价格类型")}',
//		url:'/basic/member_rank/select_memberRank.jhtml'
//	});
	//查询大客户等级
	$("#selectChangeMemberRankName").bindQueryBtn({
		type:'changeMemberRank',
		title:'${message("查询大客户等级")}',
		url:'/basic/member_rank/select_memberRank.jhtml'
	});
	
	//查询sbu
    $("#selectSbu").live("click",function(){
    	var $this = $(this);
    	$this.bindQueryBtn({
	        type:'sbu',
	        title:'${message("查询sbu")}',
	        url:'/basic/sbu/select_sbu.jhtml',
	        callback:function(rows){
	            if(rows.length>0){
	            	var row = rows[0];
	            	$this.closest("tr").find(".storeSbuName").val(row.name);
					$this.closest("tr").find(".storeSbuId").val(row.id);
	              
	            }
	        }   
	    });
    })
	//查询机构
	$("#selectSalesPlatform").bindQueryBtn({
		type:'salesPlatform',
		title:'${message("查询销售平台")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml?isSellSaleOrg=1'
	});
	
	//查询机构
	$("#selectSaleOrg").bindQueryBtn({
		type:'saleOrg',
		title:'${message("查询机构")}',
		url:'/basic/saleOrg/select_saleOrg.jhtml?isSellSaleOrg=1'
	});
	
	$(".lineSelectArea").live("click",function(){
		var $this = $(this);
		$this.bindQueryBtn({
			bindClick:false,
			type:'area',
			title:'${message("查询地区")}',
			url:'/basic/area/select_area.jhtml',
			callback:function(rows){
				if(rows.length>0){
					var $tr =$this.closest("tr");
					$tr.find(".lineAreaId").val(rows[0].id);
					$tr.find(".lineAreaName").val(rows[0].full_name);
				}
			}
		});
	})
			 //查询销售区域	
		$(".selectsalesArea").live("click",function(){
		var $this = $(this);
		$this.bindQueryBtn({
			type:'salesArea',
			title:'${message("销售区域")}',
			url:'/basic/sales_area/select_salesarea.jhtml',
			callback:function(rows){
				if(rows.length>0){
					var row = rows[0];
					$this.closest("tr").find(".salesAreaName").val(row.name);
					$this.closest("tr").find(".salesAreaId").val(row.id);
				}
			}
		});
	})
	$("#addStoreCautionMoney").click(function(){
		var row={};
		$mmGrid4.addRow(row);
		editCautionMoney();
	});
	
	$("#addStoreBusinessRecord").click(function(){
		var row={};
		$mmGrid3.addRow(row);
	});
	
	var countAddress = 0;
	$addRecviceAddress.click(function(){
		var row={};
		$mmGrid.addRow(row);
		$("#areaId"+countAddress).lSelect();
		countAddress=	parseInt(countAddress)+1
	})
	var cols = [	
		{ title:'${message("收货人")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyAddress['+countAddress+'].consignee" class="text countAddress" btn-fun="clear" />'
		}},
		{ title:'${message("收货人电话")}', align:'center',width:130,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyAddress['+countAddress+'].mobile" class="text  countAddress" btn-fun="clear" />'
		} },
		
		{ title:'${message("收货地区")}', align:'left',width:300,renderer: function(val,item,rowIndex){
			html = '<span class="search ">'+
					   	  '<input class="text lineAreaId areaId" type="hidden" name="storeApplyAddress['+countAddress+'].area.id" value="">'+
						  '<input class="text countAddress lineAreaName" maxlength="200" type="text"  value="" onkeyup="clearSelect(this)">'+
						  '<input type="button" class="iconSearch lineSelectArea" value="">'+
					   '</span>';
			   return html;
			//return '<input type="hidden" class="countAddress" id="areaId'+countAddress+'" name="storeAddress['+countAddress+'].area.id" value="" treePath="" /> </span> </td>'
		}},
		{ title:'${message("销售区域")}', align:'left',width:300,renderer: function(val,item,rowIndex){
			html = '<span class="search ">'+
					   	  '<input class="text linesalesAreaId salesAreaId" type="hidden" name="storeApplyAddress['+countAddress+'].salesArea.id" value="">'+
						  '<input class="text linesalesAreaName salesAreaName " maxlength="200" type="text"  value="" onkeyup="clearSelect(this)">'+
						  '<input type="button" class="iconSearch selectsalesArea" value="">'+
					   '</span>';
			   return html;
		}},
		{ title:'${message("收货地址")}', align:'center',width:130,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyAddress['+countAddress+'].address" class="text countAddress" btn-fun="clear" maxlength="200" value="" />'
		}},
		{ title:'${message("收货地区邮编")}', align:'center',renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyAddress['+countAddress+'].zipCode" class="text" btn-fun="clear" maxlength="200" value="" />'
		}},
		{ title:'${message("地址类型")}', align:'center',name:'address_type',width:130  ,renderer: function(val,item,rowIndex){			
			var html = '<select name="storeApplyAddress['+countAddress+'].addressType" class="text countAddress">'
				+'<option value="0" >经销商地址</option> '
				+'<option value="1">收货地址</option>'
				+'<option value="2">收单地址</option>'
				+'<option value="3" selected="selected">收单收货地址</option>'+'</select>';
			return html;
		}},
		{ title:'${message("设置")}', align:'center',renderer: function(val,item,rowIndex){
			return '<label onClick="chooseOne(this)">'+
			'<input type="checkbox" name="storeApplyAddress['+countAddress+'].isDefault" class="isDefaultCheckbox" />${message("是否默认")}'+
			'<input type="hidden" name="_storeApplyAddress['+countAddress+'].isDefault" class="isDefault" value="false" /></label>'
		} },
		
		{ title:'${message("操作")}', name:'', align:'center',renderer: function(val,item,rowIndex){
			return '<a href="javascript:;" class="btn-delete" onclick="deleteAddress(this)">删除</a>'
		}},
		
	];
	  $mmGrid = $('#table-m1').mmGrid({
		height:'auto',
	    cols: cols,
	    checkCol:false,
	    fullWidthRows: true
	 });
	var countStoreManager = 0;
	$addStoreManager.click(function(){
		var row={};
		$mmGrid1.addRow(row);
		countStoreManager=	parseInt(countStoreManager)+1;
	})
	var colss = [	
		{ title:'${message("客户经理")}', align:'center',width:130 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeManagers['+countStoreManager+'].manager" class="text countStoreManager" btn-fun="clear" />'
		}},
		
		{ title:'${message("操作")}', name:'', align:'center',renderer: function(val,item,rowIndex){
			return '<a href="javascript:;" class="btn-delete" onclick="deleteStoreManager(this)">删除</a>'
		}}
		
	];
	  $mmGrid1 = $('#table-m2').mmGrid({
		height:'auto',
	    cols: colss,
	    checkCol:false,
	    fullWidthRows: true
	 });
	 
	 /**发票信息*/
	 var invoiceIndex = 0;
	 $("#addInvoice").click(function(){
			var row={};
			$invoice_mmGrid.addRow(row);
			invoiceIndex=	parseInt(invoiceIndex)+1;
	})
	 var invoice_cols = [
		{ title:'${message("发票抬头")}', name:'invoice_title', align:'center',width:100 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyInvoiceInfos['+invoiceIndex+'].invoiceTitle" class="text" value="'+val+'" />'
		}},
		{ title:'${message("银行账户")}', name:'zengzhishuiyhzh', align:'center',width:100 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyInvoiceInfos['+invoiceIndex+'].zengzhishuiyhzh" class="text" value="'+val+'" />'
		}},
		{ title:'${message("开户行名称")}', name:'kaihuyh', align:'center',width:100 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyInvoiceInfos['+invoiceIndex+'].kaihuyh" class="text" value="'+val+'" />'
		}},
		{ title:'${message("纳税人识别号")}', name:'nashuishibiehao', align:'center',width:100 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyInvoiceInfos['+invoiceIndex+'].nashuishibiehao" class="text" value="'+val+'" />'
		}},
		{ title:'${message("开票电话")}', name:'zhucedianhua', align:'center',width:100 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyInvoiceInfos['+invoiceIndex+'].zhucedianhua" class="text" value="'+val+'" />'
		}},
		{ title:'${message("开票地址")}', name:'zhucedizhi', align:'center',width:100 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyInvoiceInfos['+invoiceIndex+'].zhucedizhi" class="text" value="'+val+'" />'
		}},
		{ title:'${message("设置")}', align:'center',renderer: function(val,item,rowIndex){
			return '<label onClick="chooseOne(this)">'+
			'<input type="checkbox" name="storeApplyInvoiceInfos['+invoiceIndex+'].isDefault" class="isDefaultCheckbox" />${message("是否默认")}'+
			'<input type="hidden" name="storeApplyInvoiceInfos['+invoiceIndex+'].isDefault" class="isDefault" value="false" /></label>'
		}},
		{ title:'${message("操作")}', name:'', align:'center',width:10,renderer: function(val,item,rowIndex){
			return '<a href="javascript:;" class="btn-delete" onclick="deleteInvoice(this)">删除</a>'
		}},
		
	];
	  $invoice_mmGrid = $('#table-invoice').mmGrid({
		height:'auto',
	    cols: invoice_cols,
	    checkCol:false,
	    autoLoad:true,
	    fullWidthRows: true
	 });
	  
	//客户联系人
	  var countStoreContract = 0;
	  $addStoreContract.click(function(){
	  var row={};
	  $mmGrid8.addRow(row);
	  countStoreContract = parseInt(countStoreContract)+1;
	  });
	  var colss = [
	  	{ title:'${message("是否法人代表")}',name:'is_legal_person', align:'center',renderer: function(val,item,rowIndex){
	  		return '<label onClick="chooseOne(this)">'+
	  		'<input type="checkbox" name="storeApplyContract['+countStoreContract+'].isLegalPerson" class="isDefaultCheckbox" value="true"/>'+
	  		'<input type="hidden" name="_storeApplyContract['+countStoreContract+'].isLegalPerson" class="isDefault" value="false" /></label>';
	  	}},
	  	{ title:'${message("姓名")}', align:'center',name:'name',width:130 ,renderer: function(val,item,rowIndex){
	  		return '<input type="text" name="storeApplyContract['+countStoreContract+'].name" class="text storeContractName"  onblur="showLegalPerson(this)" />'
	  	}},
	  	{ title:'${message("性别")}', align:'center',name:'gender',width:130  ,renderer: function(val,item,rowIndex){			
	  		var html = '<select name="storeApplyContract['+countStoreContract+'].gender" class="text gender">'
	  			+'<option value="1">男</option>'
	  			+'<option value="2">女</option>'
	  			+'</select>';
	  		return html;
	  	}},
	  	{ title:'${message("联系人类型")}', align:'center',name:'store_type',width:130  ,renderer: function(val,item,rowIndex){			
	  		var html = '<select name="storeApplyContract['+countStoreContract+'].storeType" class="text gender">'
	  			+'<option value="1">业务联系人</option>'
	  			+'<option value="2">店长</option>'
	  			+'<option value="3">财务人员</option>'
	  			+'<option value="4">物流人员</option>'
	  			+'<option value="5">客服经理</option>'
	  			+'<option value="6">其他联系人</option>'
	  			+'<option value="7">职业经理人</option>'
	  			+'<option value="0" >其他</option> '
	  			+'</select>';
	  		return html;
	  	}},
	  	{ title:'${message("收货人")}', align:'center',name:'consignee',width:130 ,renderer: function(val,item,rowIndex){
	  		return '<input type="text" name="storeApplyContract['+countStoreContract+'].consignee" class="text " />'
	  	}},
	  	{ title:'${message("收货方")}', align:'center',name:'receiving_party',width:130 ,renderer: function(val,item,rowIndex){
	  		return '<input type="text" name="storeApplyContract['+countStoreContract+'].receivingParty" class="text " />'
	  	}},
	  	{ title:'${message("收单方")}', align:'center',name:'accept',width:130 ,renderer: function(val,item,rowIndex){
	  		return '<input type="text" name="storeApplyContract['+countStoreContract+'].accept" class="text " />'
	  	}},
	  	{ title:'${message("证件类型")}', align:'center',name:'certificate_type',width:130  ,renderer: function(val,item,rowIndex){			
	  		var html = '<select name="storeApplyContract['+countStoreContract+'].certificateType" class="text">'
	  			+'<option value="1">身份证 </option>'
	  			+'<option value="2">护照</option>'
	  			+'<option value="3">军官证</option>'
	  			+'<option value="4">港澳台同胞回乡证</option>'
	  			+'<option value="5">学生证</option>'
	  			+'<option value="0">其他证件</option> '
	  			+'</select>';
	  		return html;
	  	}},
	  	{ title:'${message("移动电话")}', align:'center',name:'mobile',width:130 ,renderer: function(val,item,rowIndex){
	  		return '<input type="text" name="storeApplyContract['+countStoreContract+'].mobile" class="text " />'
	  	}},
	  	{ title:'${message("工作电话")}', align:'center',name:'workphone',width:130 ,renderer: function(val,item,rowIndex){
	  		return '<input type="text" name="storeApplyContract['+countStoreContract+'].workphone" class="text " />'
	  	}},
	  	{ title:'${message("家庭电话 ")}', align:'center',name:'homephone',width:130 ,renderer: function(val,item,rowIndex){
	  		return '<input type="text" name="storeApplyContract['+countStoreContract+'].homephone" class="text " />'
	  	}},
	  	{ title:'${message("QQ ")}', align:'center',name:'qq',width:130 ,renderer: function(val,item,rowIndex){
	  		return '<input type="text" name="storeApplyContract['+countStoreContract+'].qq" class="text " />'
	  	}},
	  	{ title:'${message("邮件 ")}', align:'center',name:'email',width:130 ,renderer: function(val,item,rowIndex){
	  		return '<input type="text" name="storeApplyContract['+countStoreContract+'].email" class="text " />'
	  	}},
	  	{ title:'${message("组织 ")}', align:'center',name:'salorg_name',width:130 ,renderer: function(val,item,rowIndex){
	  		return '<input type="text" name="storeApplyContract['+countStoreContract+'].salorgName" class="text " />'
	  	}},
	  	{ title:'${message("备注")}', align:'center',name:'memo',width:130 ,renderer: function(val,item,rowIndex){
	  		return '<input type="text" name="storeApplyContract['+countStoreContract+'].memo" class="text " />'
	  	}},
	  	{ title:'${message("是否失效")}', align:'center',name:'is_invalid',width:130  ,renderer: function(val,item,rowIndex){			
	  		var html = '<select name="storeApplyContract['+countStoreContract+'].isInvalid" class="text gender">'
	  			+'<option value="1">是</option>'
	  			+'<option value="0">否</option> '
	  			+'</select>';
	  		return html;
	  	}},
	  	{ title:'${message("操作")}', name:'', align:'center',renderer: function(val,item,rowIndex){
	  		return '<a href="javascript:;" class="btn-delete" onclick="deleteStoreContract(this)">删除</a>'
	  	}}
	  	
	  ];
	  $mmGrid8 = $('#table-storecontract').mmGrid({
	  	height:'auto',
	  	cols: colss,
	  	checkCol:false,
	  	fullWidthRows: true
	  });   
	  
	  /**合作单位*/
	 var cooperationIndex = 0;
	 $("#addCooperation").click(function(){
			var row={};
			$cooperation_mmGrid.addRow(row);
			cooperationIndex =	parseInt(cooperationIndex)+1;
	})
	var cooperation_cols = [
		 { title:'${message("记录类型")}', align:'center',name:'record_type',width:120,renderer: function(val,item,rowIndex){			
	  		var html = '<select name="storeApplyCooperation['+cooperationIndex+'].recordType" class="text">'
	  			+'<option value="0">合作单位发布会</option>'
	  			+'<option value="1">门店入驻</option>'
	  			+'<option value="2">门店撤场</option>'
	  			+'</select>';
	  		return html;
		}},
		{ title:'${message("商场")}', name:'market', align:'center',width:100 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].market" class="text" value="'+val+'" />';
		}},
		{ title:'${message("商场所在地")}', name:'marketlocal', align:'center',width:100 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].marketlocal" class="text" value="'+val+'" />';
		}},
		{ title:'${message("商场负责人")}', name:'market_leader', align:'center',width:100 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].marketLeader" class="text" value="'+val+'" />';
		}},
		{ title:'${message("负责人联系方式")}', name:'leader_phone', align:'center',width:100 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].leaderPhone" class="text" value="'+val+'" />';
		}},
		{ title:'${message("区域对接人")}', name:'area_point', align:'center',width:100 ,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].areaPoint" class="text" value="'+val+'" />';
		}},
		{ title:'${message("经销商")}', name:'dealer', align:'center',width:100,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].dealer" class="text" value="'+val+'" />';
		}},
		{ title:'${message("跟进事项")}', name:'follow_up', align:'center',width:100,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].followUp" class="text" value="'+val+'" />';
		}},
		{ title:'${message("跟进时间")}', align:'center',width:100,name:'follow_time',renderer: function(val,item,rowIndex){
			return '<input name="storeApplyCooperation['+cooperationIndex+'].followTime" class="text" value="" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
		}},
		{ title:'${message("门店位置")}', name:'position', align:'center',width:100,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].position" class="text" value="'+val+'" />';
		}},
		{ title:'${message("门店面积")}', name:'acreage', align:'center',width:100,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].acreage" class="text" value="'+val+'" />';
		}},
		{ title:'${message("沟通结果")}', name:'communicat_result', align:'center',width:100,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].communicatResult" class="text" value="'+val+'" />';
		}},
		{ title:'${message("关店原因")}', name:'close_ship_reason', align:'center',width:100,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].closeShipReason" class="text" value="'+val+'" />';
		}},
		{ title:'${message("加盟店/直营店")}', name:'join_store_type', align:'center',width:100,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].joinStoreType" class="text" value="'+val+'" />';
		}},
		{ title:'${message("通知时间")}', align:'center',width:100,name:'notice_time',renderer: function(val,item,rowIndex){
			return '<input name="storeApplyCooperation['+cooperationIndex+'].noticeTime" class="text" value="" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
		}},
		{ title:'${message("省份")}', name:'cooperation_province', align:'center',width:100,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].cooperationProvince" class="text" value="'+val+'" />';
		}},
		{ title:'${message("招商发布会地址")}', name:'conference_adress', align:'center',width:100,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].conferenceAdress" class="text" value="'+val+'" />';
		}},
		{ title:'${message("参会人员")}', name:'conferee', align:'center',width:100,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].conferee" class="text" value="'+val+'" />';
		}},
		{ title:'${message("经销商进驻意愿")}', name:'dominate', align:'center',width:100,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].dominate" class="text" value="'+val+'" />';
		}},
		{ title:'${message("是否进驻")}', align:'center',renderer: function(val,item,rowIndex){
			var html = '<select name="storeApplyCooperation['+cooperationIndex+'].dealerIsIn" class="text">'
  			+'<option value="0">是</option>'
  			+'<option value="1">否</option>'
  			+'</select>';
  			return html;
		}},
		{ title:'${message("租金")}', name:'rent', align:'center',width:100,renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].rent" class="text" value="'+val+'" />';
		}},	
		{ title:'${message("开业时间")}', align:'center',width:100,name:'opening_time',renderer: function(val,item,rowIndex){
			return '<input name="storeApplyCooperation['+cooperationIndex+'].openingTime" class="text" value="" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
		}},
		{ title:'${message("区域关店时间")}', align:'center',width:100,name:'area_close_time',renderer: function(val,item,rowIndex){
			return '<input name="storeApplyCooperation['+cooperationIndex+'].areaCloseTime" class="text" value="" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
		}},
		{ title:'${message("进驻时间")}', align:'center',width:100,name:'dealer_in_time',renderer: function(val,item,rowIndex){
			return '<input name="storeApplyCooperation['+cooperationIndex+'].dealerInTime" class="text" value="" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
		}},
		{ title:'${message("商场支持：（租金、展位)")}', align:'center',width:100,name:'market_support',renderer: function(val,item,rowIndex){
			return '<input type="text" name="storeApplyCooperation['+cooperationIndex+'].marketSupport" class="text" value="'+val+'" />';
		}},
		{ title:'${message("操作")}', name:'', align:'center',renderer: function(val,item,rowIndex){
			return '<a href="javascript:;" class="btn-delete" onclick="deleteCooperation(this)">删除</a>'
		}}
	];
	$cooperation_mmGrid = $('#table-cooperation').mmGrid({
		height:'auto',
		cols: cooperation_cols,
		checkCol:false,
		autoLoad:true,
		fullWidthRows: true
	});
	//客户sbu，价格类型
		$(".deleteSbu").live("click", function() {
			var index = $(this).closest("tr").index();
			$.message_confirm('您确定要删除吗？',function(){
				$sbu_mmGrid.removeRow(index);
			})
		});
		 
		$addSbu.click(function(){
			var row={};
			$sbu_mmGrid.addRow(row);
		});
		
		var rowIndex=0;
		[#if sbu_json ==null]
		var items=[];
		[#else]
		var items=${sbu_json};
		[/#if]
		
		var sbu_cols = [
						{ title:'${message("sbu")}', name:'sbu_name' ,align:'center',renderer: function(val,item,rowIndex){
							var sbuId = item.sbuId;
							if(item.sbuId == undefined){
								sbuId="";
							}
	    						if(item.sbu_name == undefined){
	    							var html = '<span class="search ">'+
	    						   	  '<input class="text storeSbuId" type="hidden" name="storeApplySbu['+rowIndex+'].sbu.id" value="">'+
	    							  '<input class="text storeSbuName" maxlength="200" type="text"  value="" onkeyup="clearSelect(this)">'+
	    							  '<input type="button"  class="iconSearch " value="" id="selectSbu">'+
	    						   '</span>';
	    				   		return html;
	    						}else{
	    							 html = '<span class="search ">'+
	    	    						   	  '<input class="text storeSbuId" type="hidden" name="storeApplySbu['+rowIndex+'].sbu.id" value="'+sbuId+'">'+
	    	    							  '<input class="text storeSbuName" maxlength="200" type="text"  value="'+item.sbu_name+'" onkeyup="clearSelect(this)">'+
	    	    							  '<input type="button"  class="iconSearch " value="" id="selectSbu">'+
	    	    						   '</span>';
	    	    				   		return html;
	    						}
	    		    		}},
	    		    		{ title:'${message("价格类型")}', name:'memberRank' ,align:'center',renderer: function(val,item,rowIndex){
	    		    			var memberRankId = item.memberRankId;
	    						if(item.memberRankId == undefined){
	    							memberRankId="";
	    						}
	    		    			if(item.memberRank == undefined){
								var html = '<span class="search ">'+
		    						   	  '<input type="hidden" name="storeApplySbu['+rowIndex+'].memberRank.id" class="text memberRankId" id="memberRankId" btn-fun="clear"  value=""/>'+
		    							  '<input type="text"  class="text memberRankName" maxlength="200" onkeyup="clearSelect(this)" id="memberRankName"  value=""/>'+
		    							  '<input type="button" class="iconSearch" value="" id="selectMemberRank">'+
		    						   '</span>';
		    				   		return html;
	    		    			}else{
	    		    				 html = '<span class="search ">'+
	  	    						   	  '<input type="hidden" name="storeApplySbu['+rowIndex+'].memberRank.id" class="text memberRankId" id="memberRankId" btn-fun="clear"  value="'+memberRankId+'"/>'+
	  	    							  '<input type="text"  class="text memberRankName" maxlength="200" onkeyup="clearSelect(this)" id="memberRankName"  value="'+item.memberRank+'"/>'+
	  	    							  '<input type="button" class="iconSearch" value="" id="selectMemberRank">'+
	  	    						   '</span>';
	  	    				   		return html;
	    		    			}
		    				}},
			    		{ title:'${message("设置")}', name:'is_default' ,align:'center',renderer:function(val,item,rowIndex, obj){
			   				if(obj==undefined){
									if(item.is_default){
										return '<label onClick="chooseOne(this)">'+
										'<input type="checkbox" name="sbuItemsDefault" class="isDefaultCheckbox" checked="true"/>是否默认'+
										'<input type="hidden" name="storeApplySbu['+rowIndex+'].isDefault" class="isDefault" value="1" /></label>';
							}else{
								return '<label onClick="chooseOne(this)">'+
								'<input type="checkbox" name="sbuItemsDefault" class="isDefaultCheckbox" />是否默认'+
								'<input type="hidden" name="storeApplySbu['+rowIndex+'].isDefault" class="isDefault" value="0" /></label>';
							}
							}else{
							if($("input[value=1].isDefault").length>0){
								return '<label onClick="chooseOne(this)">'+
								'<input type="checkbox" name="sbuItemsDefault" class="isDefaultCheckbox" />是否默认'+
								'<input type="hidden" name="storeSbu['+rowIndex+'].isDefault" class="isDefault" value="0" /></label>';
							}else{
								return '<label onClick="chooseOne(this)">'+
								'<input type="checkbox" name="sbuItemsDefault" class="isDefaultCheckbox" checked/>是否默认'+
								'<input type="hidden" name="storeSbu['+rowIndex+'].isDefault" class="isDefault" value="1" /></label>';
							}
						}
			    		}},
			    		{ title:'${message("操作")}', align:'center', width:60, renderer:function(val,item){
						return '<a href="javascript:;" class="deleteSbu btn-delete">删除</a>';
						rowIndex++;
						}},
		];
		  $sbu_mmGrid = $('#table-sbu').mmGrid({
			height:'auto',
			        cols: sbu_cols,
			        fullWidthRows:true,
			           items:items,
			        checkCol: false,
			        autoLoad: true,
			        callback:function(){
			     
			        }
		 });
	//查询销售区域
	$("#selectStoreSalesArea").bindQueryBtn({
		type:'salesArea',
		title:'${message("查询销售区域")}',
		url:'/basic/sales_area/select_salesarea.jhtml'
	});
	
	//查询销售品类
	$("#selectBusinessCategory").bindQueryBtn({
		type:'productType',
		title:'${message("查询销售品类")}',
		url:'/product/product_category/findTopCategory.jhtml',
		callback:function(rows){
			if(rows.length>0){
				var row = rows[0];
				$(".salesCategory").val(row.name);
			}
		}
	});
	  
});

var countAddress=0;//
var limitAddress=0;//最多五个
function addRecviceAddress(){
/* 	var html='<tr id="TrAreaId2"><th> 收货人:</th> <td> <input type="text" name="storeAddress['+countAddress+'].consignee" class="text countAddress" btn-fun="clear" /> </td>' 
		+'<th> 收货人电话: </th> <td> <input type="text" name="storeAddress['+countAddress+'].mobile" class="text  countAddress" btn-fun="clear" /> </td>' 
		+'<th></th> <td></td><th></th> <td> <a href="javascript:;" class="button sureButton" onclick="deleteAddress(this)">删除</a></td></tr>' 
		+'<tr> <th> 收货地区: </th> <td colspan="3"> <span class="fieldSet" style="width:338px;display: block;">' 
		+'<input type="hidden" class="countAddress" id="areaId'+countAddress+'" name="storeAddress['+countAddress+'].area.id" value="" treePath="" /> </span> </td>' 
		+'<th> 收货地址: </th> <td colspan="3"> <input type="text" name="storeAddress['+countAddress+'].address" class="text countAddress" btn-fun="clear" maxlength="200" value="" />'
		+'</td> </tr>';
	$("#addressAppend").parent().append(html);
	 $("#areaId"+countAddress).lSelect();
	 countAddress=parseInt(countAddress)+1;
	 limitAddress=parseInt(limitAddress)+1; */
	$mmGrid.addRow();
}
/* function deleteAddress(ele){
	$(ele).parent().parent().next().remove();
	$(ele).parent().parent().remove();
	 limitAddress=parseInt(limitAddress)-1;
} */
	function deleteStoreContract(e){
		var index = $(e).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$mmGrid8.removeRow(index);
		})
	}
	function deleteCooperation(e){
		var index = $(e).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$cooperation_mmGrid.removeRow(index);
		})
	}
  function deleteAddress(e){
		var index = $(e).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$mmGrid.removeRow(index);
		})
	}
  function deleteStoreManager(e){
		var index = $(e).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$mmGrid1.removeRow(index);
		})
	}
  
  function deleteInvoice(e){
		var index = $(e).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$invoice_mmGrid.removeRow(index);
		})
	}
  
  function deleteProductType(e){
		var index = $(e).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$mmGrid2.removeRow(index);
		})
	}
  
  function deleteBusinessRecord(e){
		var index = $(e).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$mmGrid3.removeRow(index);
		})
	}
function deleteCautionMoney(e){
		var index = $(e).closest("tr").index();
		$.message_confirm('您确定要删除吗？',function(){
			$mmGrid4.removeRow(index);
			editCautionMoney();
		})
	}
//
function chooseOne(domEl){
	var $this = $(domEl).find("input[type='checkbox']");
	var $tr = $this.closest("table");
		$tr.find("input.isDefaultCheckbox").prop("checked",false);
		$this.prop("checked",true);
		$tr.find("input.isDefault").val("false");
		$(domEl).find("input.isDefault").val("true");
		var $contact = $("input[name='contact']");
		var $thisTr = $this.closest("tr");
		var $contractNameVal = $thisTr.find("input.storeContractName").val();
		$contact.attr("value",$contractNameVal);
}

function showLegalPerson(e){
	var $tr = $(e).closest("tr");
	var $isDefaultValue = $tr.find("input.isDefaultCheckbox").prop("checked");;
	var $val = $(e).val();
	if($isDefaultValue){
		var $contact = $("input[name='contact']");
		$contact.attr("value",$val);
	}
}

function getMessage(e){
	if($("#name").val()==null||$("#name").val()==''){
		$.message_alert('${message("请先填写客户名称")}');
		return false;
	}
	ajaxSubmit($(e),{
		method:'get',
		url:"getMessage.jhtml?name="+$("#name").val(),
		callback: function(data) {
			var json_m = $.parseJSON(data.content);
			console.log(json_m);
			if(json_m.error_code==300000){
				$.message_alert('${message("不存在该客户名称的信息")}');
				return false;
			}
			if(json_m.error_code==0){
			var map=json_m.result; 
			$.dialog({
				title:"企业注册信息",
				content: '<table id="message_content"></table>',
				width: 950,
				height:400,
				onShow: function() {
					var message_items=[{"name":"注册号","value":map.regNumber},{"name":"注册地址","value":map.regLocation},
					                   {"name":"纳税人识别号","value":map.taxNumber},{"name":"法人","value":map.legalPersonName},
					                   {"name":"登记机关","value":map.regInstitute},{"name":"行业","value":map.industry},
					                   {"name":"经营范围","value":map.businessScope},{"name":"公司类型","value":map.companyOrgType}];
					var cols=[
								{ title:'${message("信息名称")}', name:'name', align:'center',width:100 },
								{ title:'${message("信息内容")}', name:'value', align:'center',width:700},
					          ];
					$message_mmGrid = $('#message_content').mmGrid({
						height:'auto',
					    cols: cols,
					    items:message_items,
					    checkCol:true,
					    autoLoad:true,
					    fullWidthRows: true
					 });
				},
				onOk: function() {
					var rows=$message_mmGrid.selectedRows();
					if(rows.length>0){
						var content='';
						for (var i = 0; i < rows.length;i++) {
							var row=rows[i];
							if(i==rows.length-1){
							content+=row.name+':'+row.value;
							}else{
							content+=row.name+':'+row.value+';  ';
							}
						}
						$("#introduction").val(content);
					}
				}
			});
			}else{
				$.message_alert('${message("获取信息失败，请联系管理员")}');
				return false;
			}
		}
	});
}


function  initProductTypeMmgrid(){
	  var itemsp = [];
	  //经营品类
	  	var colsp = [	
		{ title:'${message("大类")}', align:'center',name:"name",width:130 ,renderer: function(val,item,rowIndex){
			var html = '<span class="productType'+item.bid+'"> </span>';
				return html+'<input type="hidden" name="businessCategoryApplys['+rowIndex+'].productBigType.id"  value="'+item.id+'" class="text countAddress" btn-fun="clear" />'+val;

		}},
		{ title:'${message("中类")}', align:'center',width:130 ,name:"cname" ,renderer: function(val,item,rowIndex){
			
			var cid = item.cid;
			if(cid == "undefined" || typeof(cid)=="undefined" || cid==null){
				cid = "";
			}
			var html = '<span class="search" style="position:relative">'
				+'<input type="hidden" class="productCenterTypeId" name="businessCategoryApplys['+rowIndex+'].productCenterType.id"  value="'+cid+'" >'
				+'<input class="text productCenterTypeName" maxlength="200" type="text"  value="'+item.cname+'"  readonly>'
				+'<input type="button" class="iconSearch" value=""  onclick="findChildrenCategory(this,'+item.id+')" line_index="'+rowIndex+'">'
			+'</span>';
			
			return html;

	}},
		{ title:'${message("操作")}', name:'', align:'center',renderer: function(val,item,rowIndex){
			return '<a href="javascript:;" class="btn-delete" onclick="deleteProductType(this)">删除</a>'
		}}
		
	];
	  $mmGrid2 = $('#table-p1').mmGrid({
		  height:'auto',
	        cols: colsp,
	        items:itemsp,
	        fullWidthRows:true,
	        checkCol: false,
	        autoLoad: true
	 }); 
}

function  initCautionMoneyMmgrid(){
	  var itemsc = [];
	  //经营品类
	  	var colsc = [	
/* 		{ title:'${message("编号")}', align:'center', name:'sn',width:130 ,renderer: function(val,item,rowIndex){
			return '';
		}}, */
		{ title:'${message("账户类型")}', align:'center',name:'account_type',width:130  ,renderer: function(val,item,rowIndex){			
			var html = '<select name="cautionMoneieApplys['+rowIndex+'].accountType" class="text">'
			    +'<option value="2">品牌保证金账户</option>'
				+'<option value="1" >现金账户</option> '
				+'<option value="3">返利账户</option>';
			return html;
		}},
		{ title:'${message("应缴")}', align:'center',name:'payable',width:110 ,renderer: function(val,item,rowIndex){			
			var html = '<div class="nums-input ov fl" style="width: 108px;"> '
				+' <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editCautionMoney()">'
				+' <input type="text"  class="t payable"  name="cautionMoneieApplys['+rowIndex+'].payable" value="" minData="0" onBlur="editCautionMoney()" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'
				+' <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editCautionMoney()">'
				+' </div>';
			
			return html;
		}},
		{ title:'${message("实缴")}', align:'center',name:'paid_in',width:110  ,renderer: function(val,item,rowIndex){			
			var html = '<div class="nums-input ov fl" style="width: 108px;"> '
				+' <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editCautionMoney()">'
				+' <input type="text"  class="t paidIn"  name="cautionMoneieApplys['+rowIndex+'].paidIn" value="" onBlur="editCautionMoney()" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'
				+' <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editCautionMoney()">'
				+' </div>';
			
			return html;
		}},
		{ title:'${message("欠缴")}', align:'center',name:'unpaid',width:110  ,renderer: function(val,item,rowIndex){			
			var html = ' <input type="text"  class="text unpaid"  name="cautionMoneieApplys['+rowIndex+'].unpaid" readonly="readonly"  >'
				
			return html;
		}},
		{ title:'${message("缴纳说明")}', align:'center',name:'paidNote',width:110 ,renderer: function(val,item,rowIndex){			
			var html = '<input  class="text" name="cautionMoneieApplys['+rowIndex+'].paidNote" value="" btn-fun="clear"/>';
			return html;
		}},
		{ title:'${message("创建时间")}', align:'center',name:'create_date',width:130 ,renderer: function(val,item,rowIndex){			
			return '<input name="cautionMoneieApplys['+rowIndex+'].createDate" class="text" value="${nowDate}"  type="text" btn-fun="clear" readonly="readonly"/>'
		}},
		{ title:'${message("操作")}', name:'', align:'center',renderer: function(val,item,rowIndex){
			return '<a href="javascript:;" class="btn-delete" onclick="deleteCautionMoney(this)">删除</a>'
		}}
		
	];
	  $mmGrid4 = $('#table-p3').mmGrid({
		  height:'auto',
	        cols: colsc,
	        items:itemsc,
	        fullWidthRows:true,
	        checkCol: false,
	        autoLoad: true,
	        callback:function(){
		        editCautionMoney();
		        }
	 }); 
}


function  initBusinessRecordMmgrid(){
	  var itemsb = [];
	  //经营品类
	  	var colsb = [	
		{ title:'${message("奖惩日期")}', align:'center',name:'warn_or_award_date',width:130 ,renderer: function(val,item,rowIndex){
			
			return '<input name="businessRecordApplys['+rowIndex+'].warnOrAwardDate" class="text" value="" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>'
		}},
		{ title:'${message("奖励分类")}', align:'center',name:'award_type',width:130  ,renderer: function(val,item,rowIndex){
			var html = '<select name="businessRecordApplys['+rowIndex+'].awardType" class="text">'
						+'<option value=""></option>'
						+'<option value="1">门店返利</option> '
						+'<option value="2">奖车</option>'
						+' <option value="3">其他</option>';
			return html;
	}},
	{ title:'${message("惩罚分类")}', align:'center',name:'warn_type',width:130  ,renderer: function(val,item,rowIndex){
		var html = '<select name="businessRecordApplys['+rowIndex+'].warnType" class="text">'
			+'<option value=""></option>'
			+'<option value="1">侵权</option>'
			+' <option value="2">窜货</option> '
			+'<option value="3">其他</option>';
		return html;
	}},
	{ title:'${message("奖惩事由")}', align:'center',name:'reason',width:130 ,renderer: function(val,item,rowIndex){
	return '<input class="text" name="businessRecordApplys['+rowIndex+'].reason" value="" />';
	}},
	{ title:'${message("公司发出函件")}', align:'center',width:130,name:'letters',renderer: function(val,item,rowIndex){
		var html = '<select name="businessRecordApplys['+rowIndex+'].letters" class="text">'
			+'<option value=""></option>'
			+'<option value="1">整改通知函</option> '
			+'<option value="2">不续约通知</option>'
			+' <option value="3">整改通知书</option>'
			+'<option value="4">终止通知书</option>';
		return html;

	}},
	{ title:'${message("发出时间")}', align:'center',width:130,name:'send_date',renderer: function(val,item,rowIndex){
	
		return '<input name="businessRecordApplys['+rowIndex+'].sendDate" class="text" value="" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
	}},
	{ title:'${message("经销商收到时间")}', align:'center',name:'receive_date',width:130,renderer: function(val,item,rowIndex){
	
		return '<input name="businessRecordApplys['+rowIndex+'].receiveDate" class="text" value="" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd\'})" type="text" btn-fun="clear"/>';
	}},
	{ title:'${message("处理档案编号")}', align:'center',name:'sn',width:130,renderer: function(val,item,rowIndex){
		return '';
	}},
	{ title:'${message("金额")}', align:'center',name:'amount',width:150,renderer: function(val,item,rowIndex){
		var html = '<div class="nums-input ov fl" style="width: 163px;"> '
			+' <input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)">'
			+' <input type="text"  class="t"  name="businessRecordApplys['+rowIndex+'].amount" value="" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" >'
			+' <input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)">'
			+' </div>';
		
		return html;
	}},
	{ title:'${message("备注")}', align:'center',name:'note',width:130,renderer: function(val,item,rowIndex){
		return '<input class="text" name="businessRecordApplys['+rowIndex+'].note" value="" />';
	}},
	{ title:'${message("操作")}', name:'', align:'center',renderer: function(val,item,rowIndex){
		return '<a href="javascript:;" class="btn-delete" onclick="deleteBusinessRecord(this)">删除</a>'
	}}
		
	];
	  $mmGrid3 = $('#table-p2').mmGrid({
		  height:'auto',
	        cols: colsb,
	        items:itemsb,
	        fullWidthRows:true,
	        checkCol: false,
	        autoLoad: true
	 }); 
}




function initMmGrid(){
	if($("#productClick").val()==1){
		initProductTypeMmgrid();
		initBusinessRecordMmgrid();
		initCautionMoneyMmgrid()
		$("#productClick").val(0);
	}
}

/* 	function findChildrenCategory(id){
	//查询产品分类
	$("#addStoreProductType").bindQueryBtn({
		type:'productType',
		title:'${message("查询产品分类 ")}',
		url:'/product/product_category/findTopCategory.jhtml'+id,
		callback:function(rows){
			
		}
	});
} */
function findChildrenCategory(e,id){
	//查询产品分类
	var url = 'http://cloud.etwowin.com.cn/product/product_category/findChildren.jhtml?id='+id;
	var $search = $(e).closest(".search");
	var iframeId = "iframeId" + (new Date()).valueOf() + Math.floor(Math.random() * 1000000);
	var $dialog = $.dialog({
			title:'${message("查询子级分类")}',
			width:1200,
			height:508,
			content: "<iframe  id='"+iframeId+"' src='"+url+"' width='100%'  height='"+420+"px'><\/iframe>",
			onOk: function() {
				var rows = $("#"+iframeId)[0].contentWindow.childMethod();
				var elem = $(".productType"+rows[0].id);
				if(elem.length==0){
				$search.find(".productCenterTypeId").val(rows[0].id);
				$search.find(".productCenterTypeName").val(rows[0].name);
				$search.addClass("productType"+rows[0].id);
			}else{
				$.message_alert('${message("此分类已添加")}');
				return false;
			}
			}	
		});
}


</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("新增客户信息")}
	</div>
	<form id="inputForm" action="/member/storeApply/save.jhtml" method="post" type="ajax" validate-type="validate">
		<input type="hidden" id="productClick"  value="1" />
		<ul id="tab" class="tab">
			<li>
				<input type="button" value="${message("基本信息")}" />
			</li>
			<li>
				<input type="button"  onclick="initMmGrid()" value="${message("其他信息")}" />
			</li>
		</ul>
		<div class="tabContent">
		<table class="input input-edit">
			   <tr>
				<th>
					${message("地区")}:
				</th>
				<td colspan="3">
					<input type="hidden" id="newAreaId" name="headNewArea.id" />
				</td>
			    <th>
            		${message("业务类型")}:
            	</th>
            	<td>
            		<select id="businessTypeId" name="businessTypeId" class="text">
						[#list businessTypes as businessType]
						<option value="${businessType.id}">${businessType.value}</option>
						[/#list]
					</select>
            	</td>
				<th>
            		<span class="requiredField">*</span>${message("sbu")}:
            	</th>
            	<td>
            		<select id="sbu" name="sbuId" class="text  sbuId">
            		<option value=""></option>
						[#list sbus as sbu]
						<option value="${sbu.id}">${sbu.value}</option>
						[/#list]
					</select>
            	</td>
			</tr>
			<tr>
			     <th>
					${message("区域")}:
				</th>
				<td>
					<select class="text region" id="region" name="region">
						<option value="东区">${message("东区")}</option>
						<option value="西区">${message("西区")}</option>
						<option value="北区">${message("北区")}</option>
						<option value="南区">${message("南区")}</option>
					</select>
				</td>
				<th>
				<span class="requiredField">*</span>${message("价格类型")}:
				</th>
				<td>
					<span class="search" style="position:relative">
						<input type="hidden" name="memberRankId" class="text memberRankId" id="memberRankId" btn-fun="clear" />
						<input type="text" name="memberRankName" class="text memberRankName" maxlength="200" onkeyup="clearSelect(this)" id="memberRankName" readOnly/>
						<input type="button" class="iconSearch" value="" id="selectMemberRank"/>
					</span>
				</td>
				<th>      
					${message("乡镇")}:
			    </th>
			    <td>
					<input  type ="text" class="text" name="countryName" value="" btn-fun="clear"/>	
			    </td>
				<th>
					<span class="requiredField">*</span>${message("城市等级")}:
				</th>
				<td>
					<select name="accountTypeCode" class="text">
							<option value="0" >${message("省级")}</option>
							<option value="1"   >${message("地市级")}</option>
							<option value="2"  selected="selected" >${message("区县级")}</option>
							<option value="3"   >${message("乡镇级")}</option>
					</select>
				</td>
				</tr>
				<tr>
				<th>
					<span class="requiredField">*</span>${message("客户名称")}:
				</th>
				<td>
					<input type="text" name="name" id="name" class="text " maxlength="200"  btn-fun="clear" />
				</td>
				<th>
					${message("授权编号")}:
				</th>
				<td>
					<input type="text" name="grantCode" class="text" value="" btn-fun="clear" maxlength="200" />
				</td>
				<th>
					${message("ERP客户编码")}:
				</th>
				<td>
					<input type="hidden" name="outTradeNo" class="text " btn-fun="clear" maxlength="200" />
				</td>
				<th>
					${message("总经销商")}:
				</th>
				<td>
			   <input type="text" class="text" name="franchisee" value=""  btn-fun="clear" />
				</td>
			</tr>
			<tr>
			    <th>
					${message("客户简称")}:
				</th>
				<td>
					<input type="text" name="alias" class="text " maxlength="200"  btn-fun="clear" />
				</td>
				<th>
					${message("经销商姓名")}:
				</th>
				<td>
					<input type="text" name="dealerName" class="text" value="" btn-fun="clear" maxlength="200" />
				</td>
				<th>
					${message("合同主体")}:
				</th>
				<td>
					<input type="hidden" name="contractSubject" class="text" value="" btn-fun="clear" maxlength="200" />
				</td>
				<th>
					<span class="requiredField">*</span>${message("平台性质")}:
				</th>
				<td>
					<select name="platformProperty" class="text">
							<option value="0" >${message("运营管理中心")}</option>
							<option value="1" >${message("控股合资管理中心")}</option>
							<option value="2" >${message("营销服务中心")}</option>
							<option value="3" >${message("营销管理中心")}</option>
					</select>
				</td>
			</tr>
			<tr>
				<th>
					${message("经销商状态")}:
				</th>
				<td>
					<select name="accountStatus" class="text" >
					        <option value="0" >${message("潜在")}</option>
							<option value="1" >${message("有效")}</option>
							<option value="2" >${message("失效")}</option>
							<option value="3" >${message("暂停使用")}</option>
					</select>
				</td>				
				<th>
					${message("法人代表")}:
				</th>
				<td>
					<input type="text" class="text" name="contact" value="" btn-fun="clear"/>
				</td>
				<th>
			    ${message("手机号")}:
			    </th>
			    <td>
			    <input type="text" class="text" name="headPhone" value="" btn-fun="clear"/>
			    </td>
			    <th>
					${message("销售平台")}:
				</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="salesPlatformId" class="text salesPlatformId" btn-fun="clear" value=""/>
					<input type="text" name="salesPlatformName" class="text salesPlatformName" maxlength="200" onkeyup="clearSelect(this)" value="" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectSalesPlatform" />
					</span>
				</td>
				</tr>
			    <tr>
			    <th>
					${message("设置")}:
				</th>
				<td>
					<label>
						<input type="checkbox" name="isEnabled" value="true" checked="checked"/>${message("是否启用")}
						<input type="hidden" name="_isEnabled" value="false" />
					</label>
					&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					<input type="hidden" name="isReduceBalance" value="true" />
					<!-- ${message("订单下达是否校验可发货余额")}:
					<label>
						<input type="checkbox" name="isReduceBalance" value="true" checked="checked"/>${message("检验")}
						<input type="hidden" name="_isReduceBalance" value="false"/>
					</label> -->
	            </td>
	            <th>
					<span class="requiredField">*</span>${message("身份证信息")}:
				</th>
				<td>
				<input  type="text" class="text required" name="identity" value="" btn-fun="clear" />
				</td>
	            <th>
					${message("固定号码")}:
				</th>
				<td >
					<input type="text" class="text" name="fixedNumber" value="" btn-fun="clear"/>
	            </td>
				<th>
					${message("销售区域")}:
				</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="storeApplySalesAreaId" class="text salesAreaId" btn-fun="clear" value=""/>
					<input type="text" name="storeApplySalesAreaName" class="text salesAreaName" maxlength="200" onkeyup="clearSelect(this)" value="" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectStoreSalesArea"/>
					</span>
				</td>
				</tr>
	            <tr>
				<th><span class="requiredField">*</span>${message("区域经理")}:</th>
				<td>
				 	<span class="search" style="position:relative">
                    <input type="hidden" name="storeMemberId" class="text storeMemberId" [#if "${storeMember}" != 0]value="${storeMember.id}" [#else]value=""[/#if] btn-fun="clear"/>
                    <input type="text" name="storeMemberName" class="text storeMemberName"  [#if "${storeMember}" != 0]value="${storeMember.name}" [#else]value=""[/#if] maxlength="200" onkeyup="clearSelect(this)"  readOnly/>
                    <input type="button" class="iconSearch" value="" id="openStore" />
                    </span>
				</td>
				<th>
					${message("经销商地址")}:
				</th>
				<td colspan="3">
					<input  class="text" name="headAddress" value="" btn-fun="clear" />
	            </td>
				<th>
					<span class="requiredField">*</span>${message("机构")}:
				</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="saleOrgId" class="text saleOrgId" btn-fun="clear" value=""/>
					<input type="text" name="saleOrgName" class="text saleOrgName" maxlength="200" onkeyup="clearSelect(this)" value="" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectSaleOrg" />
					</span>
				</td>
				</tr>
				<tr>
				<th>
					${message("经销商关系说明")}:	
				</th>
				<td>
					<input type="text"   class="text" name="dealerRelationShip" value="" btn-fun="clear"/>
				</td>
				<th></th><td></td>
				<th></th><td></td>
				<th></th><td></td>
				</tr>
				<tr>
				<th>
					${message("客户介绍")}:  
				</th>
				<td colspan="7">
					<textarea name="introduction" id="introduction" class="text"></textarea>
				</td>
			</tr>
			<tr>
				<th>
					${message("16055")}:
				</th>
				<td colspan="7">
            		<span>
            			<a href="javascript:;" class="a-upload addProductImage" style="width:80px;height:80px;margin-right:10px;" id="storeImage"></a>
            		</span>
            		<span>
            			<a href="javascript:;" class="a-upload addProductImage" style="width:80px;height:80px;" id="storeImage2"></a>
            		</span>
            		
					
				</td>
			</tr>
			</table>
			<table class="input input-edit" style="width:100%;margin-top:5px;">
			 <tr class="barnk border-L1" id="addressAppend">
				<th>
				${message("添加收货地址")}
				</th>
				<td colspan="7">
				    <a href="javascript:;" class="button" id="addRecviceAddress">${message("添加")}</a>
				</td>
			</tr>
			<tr class="border-L1">
				<td colspan="8">
					<div>
						<table id="table-m1"></table>
					</div>
				</td>
			</tr>
<!--
			<tr class="border-L2">
				<th>
				${message("发票信息")}
				</th>
				<td colspan="7">
				    <a href="javascript:;" class="button" id="addInvoice">${message("添加")}</a>
				</td>
			</tr>
			<tr class="border-L2">
				<td colspan="8">
					<div>
						<table id="table-invoice"></table>
					</div>
				</td>
			</tr>
			<tr style="display:none">
				<th>
					<span class="requiredField">*</span>${message("大客户价格等级")}:
				</th>
				<td>
					<span class="search" style="position:relative">
							<input type="hidden" name="changeMemberRankId" class="text changeMemberRankId" id="changeMemberRankId" btn-fun="clear" />
							<input type="text" name="changeMemberRankName" class="text changeMemberRankName" maxlength="200" onkeyup="clearSelect(this)" id="changeMemberRankName"/>
							<input type="button" class="iconSearch" value="" id="selectChangeMemberRankName">
						</span>
				</td>
				<th>
					${message("启用的数量")}:
				</th>
				<td>
					<input type="number" min="0" name="changeQuantity" class="text " btn-fun="clear" />
				</td>
				<td colspan="4"><span class="requiredField">注：订单下达时，产品的下单数大于或等于“启用的数量”时，当前产品将使用“大客户价格等级”的价格。</span></td>
			</tr>
-->
			<!-- 合作单位-->
<!--			<tr class="barnk border-L1">
				<th>
				${message("合作单位")}
				</th>
				<td colspan="7">
				    <a href="javascript:;" class="button" id="addCooperation">${message("添加")}</a>
				</td>
			</tr>
			<tr class="border-L2">
				<td colspan="8">
					<div>
						<table id="table-cooperation"></table>
					</div>
				</td>
			</tr>
			<tr class="border-L1">
          	<th>${message("sbu列表")}:</th>
          	<td colspan="7"><a href="javascript:void(0);" id="addSbu" class="button">${message("添加")}</a></td>
          </tr>
          <tr class="border-L1">
          	<td colspan="8">
          		<table id="table-sbu"></table>
          	</td>
          </tr>
-->
		</table>
		</div> 
		<div class="tabContent">
			<table class="input input-edit">
			<tr>
				<th>
					${message("设置")}:
				</th>
				<td>
				<label>
						<input type="checkbox" name="isJoinFalse" value="true" />${message("未加盟成功")}
						<input type="hidden" name="_isJoinFalse" value="false" />
				</label>
				</td>
				<th>
					${message("设置")}:
				</th>
				<td>
				<label>
						<input type="checkbox" name="cancelInfoFlag" value="true" />${message("解约时是否资料齐全")}
						<input type="hidden" name="_cancelInfoFlag" value="false" />
					</label>
				</td>
				<th>
					${message("应缴品牌保证金")}:
				</th>
				<td >
					<div class="nums-input ov fl">
						<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editAccSub(this,event)">
						<input type="text"  class="t" id="needCautionPaid"  name="needCautionPaid" value="" minData="0" oninput="editAccSub(this,event)" onpropertychange="editAccSub(this,event)" >
						<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editAccSub(this,event)">
					</div>
				</td>
			    <th>
					${message("设置")}:
				</th>
				<td >
					<label>
						<input type="checkbox" name="cashClientFlag" value="true" />${message("是否现金客户")}
						<input type="hidden" name="_cashClientFlag" value="false" />
					</label>
	            </td>
	            </tr>
				<tr>
				<th>
					${message("加盟日期")}:
				</th>
				<td>
				<input name="activeDate" class="text" value="" onClick="WdatePicker({dateFmt: 'yyyy-MM-dd'})" type="text" btn-fun="clear"/>
				</td>
				<th>
					${message("解约日期")}:
				</th>
				<td>
				<input type="text" class="text" value=""   name="cancelDate"  onClick="WdatePicker({dateFmt: 'yyyy-MM-dd'})"  btn-fun="clear"/>
				</td>
				<th>
					${message("实缴品牌保证金")}:
				</th>
				<td >
					<div class="nums-input ov fl">
						<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" onMouseUp="editAccSub(this,event)" />
						<input type="text"  class="t" id="realCautionPaid" name="realCautionPaid" value="" minData="0" oninput="editAccSub(this,event)" onpropertychange="editAccSub(this,event)" />
						<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)" onMouseUp="editAccSub(this,event)" />
					</div>
				</td>
				<th>
					${message("货币")}:
				</th>
				<td>
				<select name="currencyCode" class="text">
				<option value="人民币" >${message("人民币")}</option>
				</select>
				</td>
				</tr>
				<tr>
					<th>
					${message("加盟档案编号")}:
				</th>
				<td >
				<input  class="text" name="joinFileNumber" value="" btn-fun="clear"/>
	            </td>
	            <th>
					${message("解约档案编号")}:
				</th>
				<td >
				<input  class="text" name="unfileNumber" value="" btn-fun="clear"/>
	            </td>
	            <th>
					${message("欠缴品牌保证金")}:
				</th>
				<td >
					<input class="text" style="border: 0px;text-align:center;color:red;" name="unpaidCautionPaid" id="unpaidCautionPaid" readonly="readonly"/>
				</td>
	            <th>
					${message("门店数量")}:
				</th>
				<td >
					<span id="numberStores"></span>
				</td>
				</tr>
	            <tr>
	            <th>
					${message("经销商类型")}:
				</th>
				<td>
					<select name="distributorType" class="text">
							<option value="0" >${message("国内经销商")}</option>
							<option value="1" >${message("国际经销商")}</option>
							<option value="2" >${message("国产产品经销商")}</option>
							<option value="3" >${message("进口产品经销商")}</option>
					</select>
				</td>
				<th>
					${message("解约原因")}:	
				</th>
				<td>
					<input  class="text" name="cancelReason" value="" btn-fun="clear"/>
				</td>
				<th>
					${message("销量保证金")}:
				</th>
				<td >
					<div class="nums-input ov fl">
						<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" />
						<input type="text"  class="t"  name="salesDeposit" value="" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" />
						<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"/>
					</div>
				</td>
				<th>
					${message("销售品类")}:
				</th>
				<td>
					<span class="search" style="position:relative">
					<input type="hidden" name="" class="text" btn-fun="clear" value=""/>
					<input type="text" name="salesCategory" class="text salesCategory" maxlength="200" onkeyup="clearSelect(this)" value="" readOnly/>
					<input type="button" class="iconSearch" value="" id="selectBusinessCategory" />
					</span>
				</td>
				</tr>
				<tr>
				<th>
					${message("经销商子类型")}:
				</th>
				<td>
					<select name="subType" class="text">
							<option value="" >${message("请选择")}</option>
							<option value="0" >${message("总经销商")}</option>
							<option value="1" >${message("省会城市经销商")}</option>
							<option value="2" >${message("平台经销商")}</option>
							<option value="3" >${message("经销商")}</option>
							<option value="4" >${message("分销商")}</option>
					</select>
				</td>
				<th>${message("税率")}:</th>
	            <td>
	            	<div class="nums-input ov fl" style="width: 163px;">
						<input type="button" class="b decrease" value="-"  onMouseDown ="decrease(this,event)" />
						<input type="text"  class="t"  name="taxRate" value="16" minData="0" oninput="editQty (this,event)" onpropertychange="editQty(this,event)" />
						<input type="button" value="+" class="b increase"  onMouseDown ="increase(this,event)"/>
					</div>
					<div style="line-height: 30px;width: 22px;" class="fl tc">%</div>
	            </td>
				<th>
					${message("缴纳情况/异常说明")}:
				</th>
				<td>
					<input type="text" class="text" name="paymentStatus" value="" btn-fun="clear"/>
				</td>
				<th>      
					${message("创建人")}:
			    </th>
				<td>
					<select name="store_type" class="hidden">
						[#list types as value]
							<option value="${value}" [#if value==4]selected[/#if]>${message('11111111'+value)}</option>
						[/#list]
					</select>
				</td>
				</tr>
				<tr>
				<th>
					${message("备注（门店地址）")}:
				</th>
				<td colspan="5">
				<input  class="text" name="description" value="" btn-fun="clear"/>
				</td>  
				<th>
					${message("创建时间")}:
				</th>
				<td>
				</td>
			</tr>
			</table>
			<table class="input input-edit" style="width:100%;margin-top:5px;">
	<!-- 经营品类 -->
<!--			<tr class="barnk border-L3" id="addProductType">
				<th>
				${message("添加经营品类")}
				</th>
				<td colspan="7">
				    <a href="javascript:;" class="button" id="addStoreProductType">${message("添加")}</a>
				</td>
			</tr>
			<tr class="border-L3">
				<td colspan="8">
					<div>
						<table id="table-p1"></table>
					</div>
				</td>
			</tr>
-->
	<!-- 经营活动记录 -->
<!--			<tr class="barnk border-L2" id="addBusinessRecord">
				<th>
				${message("经营活动记录")}
				</th>
				<td colspan="7">
				    <a href="javascript:;" class="button" id="addStoreBusinessRecord" >${message("添加")}</a>
				</td>
			</tr>
			<tr class="border-L2">
				<td colspan="8">
					<div>
						<table id="table-p2"></table>
					</div>
				</td>
			</tr>
-->
	<!-- 保证金 -->
<!--			<tr class="barnk border-L1" id="addCautionMoney">
				<th>
				${message("保证金")}
				</th>
				<td colspan="7">
				    <a href="javascript:;" class="button" id="addStoreCautionMoney" >${message("添加")}</a>
				</td>
			</tr>
			<tr class="border-L1">
				<td colspan="8">
					<div>
						<table id="table-p3"></table>
					</div>
				</td>
			</tr>
-->
	<!-- 客户联系人-->
<!--			<tr class="barnk border-L5">
				<th>
				${message("客户联系人")}
				</th>
				<td colspan="7">
				    <a href="javascript:;" class="button" id="addStoreContract" >${message("添加")}</a>
				</td>
			</tr>
			<tr class="border-L5">
				<td colspan="8">
					<div>
						<table id="table-storecontract"></table>
					</div>
				</td>
			</tr>
-->
		</table>
				</div>
		<div class="fixed-top">
			[#if companyInfoId==35]
			<input type="button" onclick="getMessage(this)" class="button sureButton" value="${message("查询企业注册信息")}" />
			[/#if]
			<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" />
			<input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
		</div>
	</form>
</body>
</html>