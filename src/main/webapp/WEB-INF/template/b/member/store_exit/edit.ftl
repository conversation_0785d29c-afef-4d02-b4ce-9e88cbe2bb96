<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <title>${message("经销商退出")}</title>
    <link href="/resources/css/common.css" rel="stylesheet" type="text/css"/>
    <link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
    <script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
    <script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
    <script type="text/javascript" src="/resources/js/base/dialog.js"></script>
    <script type="text/javascript" src="/resources/js/base/request.js"></script>
    <script type="text/javascript" src="/resources/js/base/global.js"></script>
    <script type="text/javascript" src="/resources/js/base/file.js"></script>
    <script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
    <script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
    <link href="/resources/css/preview-swiper.min.css" rel="stylesheet" type="text/css"/>
    <script type="text/javascript" src="/resources/js/reference/preview-swiper.min.js"></script>
    <style>
        .bottonss {
            border: 1px solid #0000;
            height: 32px;
            width: 66px;
            background-color: #2190e8;
            border-radius: 5px;
            color: #fff;
            font-size: 14px;
            font-family: Microsoft YaHei;
            cursor: pointer;
            text-align: center;
        }

        .bottonss:hover {
            background-color: #5594c5;
        }

        .tj {
            position: absolute;
            right: 15px;
        }
        label.fieldError {opacity:1;}
    </style>
    <script type="text/javascript">

        function editQty(t, e) {
            extractNumber(t, 3, false, e);
        }

        //没有小数位
        function editQtyEmpty(t, e, n) {
            var rate = 0;
            if (n != undefined) rate = n;
            extractNumber(t, rate, false, e)
        }

        $().ready(function () {
            var $inputForm = $("#inputForm");

            // 校验
            $inputForm.validate({
				rules: {
					businessYear: "required",
					exitCause : "required",
					//shopState: "required",
					//shopAddress: "required",
					exitTime: "required",
					exitStatus: "required",
					brandMargin: "required",
					payAmount: "required",
					whetherArrears : "required",
					brandReceipt : "required",
					openProvince : "required",
					openCity: "required",
					areaManagerOpinion: "required",
				} ,
				messages: {
					exitCause: {
						required: "<br>请选择退出原因"
					},
					shopState: {
						required: "<br>请选择门店状况"
					}, 
					exitStatus: {
						required: "<br>请选择退出状态"
					}, 
					brandMargin: {
						required: "<br>请选择一个选项"
					},
					brandReceipt: {
						required: "<br>请选择一个选项"
					},
					whetherArrears : {
						required: "<br>请选择经销商是否有欠款"
					},
				},
				errorPlacement: function (error, element) { //指定错误信息位置
					if (element.is(':radio') || element.is(':checkbox')) { //如果是radio或checkbox
				        var eid = element.attr('name'); //获取元素的name属性
				        error.appendTo(element.parent().parent()); //将错误信息添加当前元素的父结点后面
				    } else {
				        error.insertAfter(element);
				    }
				}
			});

			// 初始化附件
            initAttach("storeExitAttachs", "addStoreExitAttach", "table-storeExitAttach", 1,${store_exit_attach});
            initAttach("storeReceiptAttachs", "addStoreReceiptAttach", "table-storeReceiptAttach", 2,${store_receipt_attach});

            $("#bankAreaId").lSelect();
            $("#newAreaId").lSelect();
            fixAddress();
            
            initShopInfo(${shopInfos});

            [#if storeExit.wfId!=null]
            $("#wf_area").load("/act/wf/wf.jhtml?wfid=${storeExit.wfId}");
            [/#if]

            // 全屏看图功能 start
            $(".toPreviewBtn").click(function () {
              let a_href = $(this).next().children().find(".real-row .pic a")
              let listData = []
              for(var i = 0; i < a_href.length; i++) {
                let pathname_url = a_href[i].pathname
                let url_index = pathname_url.lastIndexOf('.');
                let getPath = pathname_url.substring(url_index + 1, url_index.length);
                if (getPath == "jpg" || getPath == "jpeg" || getPath == "png" || getPath == "gif") {
                    let item = a_href[i]
                    listData.push(item)
                }
              }

              if (listData.length == 0) {
                alert('当前没有可浏览的图片附件')
                return
              }

              if (listData.length > 0) {
                $(".preview_swiperBox").fadeIn(300)
              }
              if (listData.length <= 1) {
                $("#picture-swiperLeft").hide()
                $("#picture-swiperRight").hide()
              } else {
                $("#picture-swiperLeft").show()
                $("#picture-swiperRight").show()
              }
              let str= "<div class='swiper-container'>";
                str+= "<div class='swiper-wrapper'>";
              $.each(listData, function(index, item){
                str+= "<div class='swiper-slide'>";
                str+= "<img src='" + item + "' />"
                str+= "</div>"
              });  
              str+= "</div>";
              str+= "<div class='swiper-pagination'></div>";
              str+= "</div>";
              $(".preview_swiperBox").append(str);

              var swiper = new Swiper('.swiper-container', {
                slidesPerView: 1,
                spaceBetween: 0,
                loop: true,
                pagination: {
                  el: '.swiper-pagination',
                  clickable: true,
                },
                navigation: {
                  nextEl: '#picture-swiperRight',
                  prevEl: '#picture-swiperLeft',
                },
                on: {
                  touchEnd: function(swiper,event){
                    rotateParam = 0
                    scaleParam = 1
                    $(".swiper-wrapper .swiper-slide-next img").css({"transform": "none", "transition": "none"})
                    $(".swiper-wrapper .swiper-slide-prev img").css({"transform": "none", "transition": "none"})
                  },
                },
              });
            });

            var rotateParam = 0
            var scaleParam = 1

            // 关闭全屏看图
            $("#picture-colse").click(function () {
              $(".preview_swiperBox").fadeOut(300);
              $(".swiper-container").remove();
              rotateParam = 0
              scaleParam = 1
              $(".swiper-wrapper .swiper-slide-next img").css({"transform": "none", "transition": "none"})
              $(".swiper-wrapper .swiper-slide-prev img").css({"transform": "none", "transition": "none"})
            });
            $("#picture-swiperLeft").click(function () {
              rotateParam = 0
              scaleParam = 1
              $(".swiper-wrapper .swiper-slide-next img").css({"transform": "none", "transition": "none"})
              $(".swiper-wrapper .swiper-slide-prev img").css({"transform": "none", "transition": "none"})
            });
            $("#picture-swiperRight").click(function () {
              rotateParam = 0
              scaleParam = 1
              $(".swiper-wrapper .swiper-slide-next img").css({"transform": "none", "transition": "none"})
              $(".swiper-wrapper .swiper-slide-prev img").css({"transform": "none", "transition": "none"})
            });


            $("#previewReset").click(function () {
              rotateParam = 0
              scaleParam = 1
              $(".swiper-wrapper .swiper-slide-active img").css({"transform": "rotate(0deg) scale(1)", "transition": "transform .3s"})
            });
            $("#rotateLeft").click(function () {
              rotateParam-= 90
              let rotateStyle = "rotate(" + rotateParam + "deg)"
              let scaleStyle = "scale(" + scaleParam + ")"
              $(".swiper-wrapper .swiper-slide-active img").css({"transform": (rotateStyle + scaleStyle), "transition": "transform .3s"})
            });
            $("#rotateRight").click(function () {
              rotateParam+= 90
              let rotateStyle = "rotate(" + rotateParam + "deg)"
              let scaleStyle = "scale(" + scaleParam + ")"
              $(".swiper-wrapper .swiper-slide-active img").css({"transform": (rotateStyle + scaleStyle), "transition": "transform .3s"})
            });
            $("#scaleBig").click(function () {
              scaleParam+= 0.1
              let rotateStyle = "rotate(" + rotateParam + "deg)"
              let scaleStyle = "scale(" + scaleParam + ")"
              $(".swiper-wrapper .swiper-slide-active img").css({"transform": (rotateStyle + scaleStyle), "transition": "transform .3s"})
            });
            $("#scaleSmall").click(function () {
              scaleParam-= 0.1
              let rotateStyle = "rotate(" + rotateParam + "deg)"
              let scaleStyle = "scale(" + scaleParam + ")"
              $(".swiper-wrapper .swiper-slide-active img").css({"transform": (rotateStyle + scaleStyle), "transition": "transform .3s"})
            });
            // 全屏看图功能 end

        });

        // 保存更新
        function save(e) {
            var str = '您确定要保存吗？';
            var url = '/member/store_exit/update.jhtml';
            var $form = $("#inputForm");
            if ($form.valid()) {
                ajaxSubmit(e, {
                    url: url,
                    data: $("#inputForm").serialize(),
                    method: "post",
                    isConfirm: true,
                    confirmText: str,
                    callback: function (resultMsg) {
                        $.message_timer(resultMsg.type, resultMsg.content, 1000, function () {
                            //location.href= 'edit/b.jhtml?id='+resultMsg.objx;
                            location.href = '/member/store_exit/edit/${code}.jhtml?id=' + resultMsg.objx;

                        })
                    }
                });
            }
        }


        function check_wf(e) {
            var $this = $(e);
            var $form = $("#inputForm");
            if ($form.valid()) {
                $.message_confirm("您确定要审批流程吗？", function () {
                    var objTypeId = 100021;//大自然测试-正式
                    // var modelId = 72505;//大自然测试
                    var modelId = 60261;//大自然正式
                    //大自然开发
                    // var objTypeId = 100010;
                    // var modelId = 205037;
                    var url = "/member/store_exit/check_wf.jhtml?id=${storeExit.id}&modelId=" + modelId + "&objTypeId=" + objTypeId;
                    var data = $form.serialize();
                    ajaxSubmit(e, {
                        method: 'post',
                        url: url,
                        async: true,
                        callback: function (resultMsg) {
                            $.message_timer(resultMsg.type, resultMsg.content, 1000, function () {
                                //location.reload(true);
                                location.href = '/member/store_exit/edit/${code}.jhtml?id=' + resultMsg.objx;
                            })
                        }
                    });
                });
            }
        }


        //只有选中复选框后才能编辑后面的文本框
        function handle(e) {
            var v = $(e).prev();//找出前一个元素
            console.log(v[0]);
            if ($(e).prop('checked') == true) {
                var $pdiv = $(e).parent("div");
                $pdiv.find('input[type=text]').removeAttr("readonly");
            } else {
                var $pdiv = $(e).parent("div");
                $pdiv.find('input[type=text]').attr("readonly", "readonly");
                $pdiv.find('input[type=text]').val("");
            }
        }

        // 只有算是选中复选框后才能编辑后面的日期
        function handleDate(e) {
            if ($(e).prop('checked') == true) {
                var $pdiv = $(e).parent("div");
                $pdiv.find('input[type=text]').removeAttr("disabled");
            } else {
                var $pdiv = $(e).parent("div");
                $pdiv.find('input[type=text]').attr("disabled", "disabled");
                $pdiv.find('input[type=text]').val("");
            }
        }

        // 只有选中复选框后才能编辑后面的单选框
        function handleRadio(e) {
            if ($(e).prop('checked') == true) {
                var $pdiv = $(e).parent("div");
                $pdiv.find('input[type=radio]').removeAttr("disabled");
            } else {
                var $pdiv = $(e).parent("div");
                $pdiv.find('input[type=radio]').attr("disabled", "disabled");
                $pdiv.find('input[type=radio]').removeAttr("checked");
            }
        }

        function saveform(e) {
            $.message_confirm("您确定要提交吗？", function () {
                //获取表单所有数据
                var params = $("#inputForm").serializeArray();
                //定义url
                var url = '/member/store_exit/saveform.jhtml?type=' + e;
                ajaxSubmit(e, {
                    method: 'post',
                    url: url,
                    data: params,
                    async: true,
                    callback: function (resultMsg) {
                        $.message_timer(resultMsg.type, resultMsg.content, 1000, function () {
                            location.reload(true);
                        })
                    }
                });
            });
        }

        /**
         * 初始化附件，每个参数都是必填的
         * @param paramAttachs 后台实体类接收附件的参数名
         * @param addAttachIdName 前端页面添加附件按钮的id值
         * @param tableIdName 前端页面table中的id值
         * @param type 后台用来区分不同类型附件
         * @param attachsData 附件数据
         */
        function initAttach(paramAttachs, addAttachIdName, tableIdName, type, attachsData) {
            var attachs_data = attachsData;
            var index = 0;
            var attachCols = [
                { title:'${message("附件")}',name:'content',width:400,align:'center',renderer:function(val,item,rowIndex,obj){
                    if (obj==undefined) {
                        var url = item.url;
                        var fileObj = getfileObj(item.name);
                        
                        if (fileObj.suffix == 'null' || fileObj.suffix == '') {
                          let url_index = url.lastIndexOf('.');
                          if(url_index>=0){
                              fileObj.suffix = url.substring(url_index+1, url.length);
                           }
                        }

                        /**设置隐藏值*/
                        var hideValues = {};
                        hideValues[paramAttachs+'['+index+'].id']=item.id;
                        hideValues[paramAttachs+'['+index+'].url']=url;
                        hideValues[paramAttachs+'['+index+'].suffix']=fileObj.suffix;
                        hideValues[paramAttachs+'['+index+'].type']=item.type;
                        return createFileStr({
                            url : url,
                            fileName : fileObj.file_name,
                            name : fileObj.name,
                            suffix : fileObj.suffix,
                            time : item.create_date,
                            textName: paramAttachs+'['+index+'].name',
                            hideValues:hideValues
                        });

                    } else {
                        var url = item.url;
                        var fileObj = getfileObj(item.name);
                        /**设置隐藏值*/
                        var hideValues = {};
                        hideValues[paramAttachs+'['+index+'].url']=url;
                        hideValues[paramAttachs+'['+index+'].suffix']=fileObj.suffix;
                        hideValues[paramAttachs+'['+index+'].type']=type;
                        return createFileStr({
                            url : url,
                            fileName : fileObj.file_name,
                            name : fileObj.name,
                            suffix : fileObj.suffix,
                            time : '',
                            textName: paramAttachs+'['+index+'].name',
                            hideValues:hideValues
                        });
                    }
                }},
                { title:'${message("备注")}', name:'memo' ,width:500 ,align:'center', renderer: function(val,item,rowIndex){
                    return '<div><textarea class="text" name="'+paramAttachs+'['+index+'].memo" style="width:94%;height: 40px">'+val+'</textarea></div>';
                }},
                { title:'${message("操作")}', align:'center', renderer: function(val,item,rowIndex){
                    index++;
                    return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
                }}
            ];
            var $grid=$('#'+tableIdName).mmGrid({
                fullWidthRows:true,
                height:'auto',
                cols: attachCols,
                items: attachs_data,
                checkCol: false,
                autoLoad: true
            });

            var $addAttach = $("#"+addAttachIdName);
            var attachIdnex = 0;
            var option = {
                dataType: "json",
                uploadToFileServer:true,
                uploadSize: "fileurl",
                callback : function(data){
                    var date = new Date();
                    var year = date.getFullYear();
                    var month = date.getMonth()+1;
                    var day = date.getDate();
                    var time = year+'-'+month+'-'+day;
                    for(var i=0;i<data.length;i++){
                        var row = data[i].file_info;
                        $grid.addRow(row,null,1);
                    }
                }
            }
            $addAttach.file_upload(option);

            /* 删除附件 */
            var $deleteAttachment = $(".deleteAttachment");
            $deleteAttachment.live("click", function() {
                var $this = $(this);
                $this.closest("tr").remove();
            });
        }

        //固定地址下拉框
        function fixAddress() {
            $(".area").each(function (index) {
                $(this).find("select").each(function () {
                    $(this).attr("disabled", true);
                });
            })
        }
    	
    	/**
    	 * 当前客户门店
    	 *
    	 */
    	function initShopInfo(shopInfoJson){
			var rowIndex=0;
			var cols = [
				{ title:'${message("门店单号")}', name:'sn',width:80,align:'center'},
				{ title:'${message("门店授权编号")}', name:'authorization_code',width:80,align:'center'},
				{ title:'${message("门店")}', name:'shop_name',width:80,align:'center',renderer: function(val,item,rowIndex){
					var html = '<span>'+val+'</span>';
					html += '<input type="hidden" name="seList['+rowIndex+'].shopInfo.id" value="'+item.id+'" />'
					return html;
				}},
				{ title:'${message("门店地址")}', name:'address',width:200,align:'center'},
				{ title:'${message("门店关闭")}', name:'is_close' ,width:20,align:'center',renderer: function(val,item,rowIndex){
					var ischecked = "";
					if(val == "1"){
						ischecked = 'checked="true"';
					}
					var html = '<label onClick="chooseOne(this)">';
					html += '<input type="checkbox" name="seList['+rowIndex+'].isClose" value="true" '+ischecked+' class="isDefaultCheckbox" />';
					html += '<input type="hidden" name="seList['+rowIndex+']._isClose" value="false" class="isDefault" />';
					html += '</label>';
					return html;
				}},
				{ title:'${message("门店交接")}', name:'is_exit' ,width:20,align:'center',renderer: function(val,item,rowIndex){
					var ischecked = "";
					if(val == "1"){
						ischecked = 'checked="true"';
					}
					var html = '<label onClick="choose(this)">';
					html += '<input type="checkbox" name="seList['+rowIndex+'].isExit" value="true" '+ischecked+' class="isDefaultCheckboxOne" />';
					html += '<input type="hidden" name="seList['+rowIndex+']._isExit" value="false" class="isDefaultOne" />';
					html += '</label>';
					rowIndex++;
					return html;
				}},
				{ title:'门店减少档案',name:'decrease_code',align:'center',width:80 ,renderer: function(val,item,rowIndex){
					return '<input type="text" name="seList['+rowIndex+'].decreaseCode" value="'+val+'" class="text" />';
				}},
				{ title:'日期',name:'off_time',align:'center',width:80 ,renderer: function(val,item,rowIndex){
					return '<input type="text" name="seList['+rowIndex+'].offTime" value="'+val.substring(0,10)+'" onclick="WdatePicker({dateFmt:\'yyyy-MM-dd\'})" class="text" />';
				}},
				{ title:'原因',name:'cause', align:'center',width:120 ,renderer: function(val,item,rowIndex){
						return '<input type="text" name="seList['+rowIndex+'].cause" class="text " value="'+val+'"/>';
				}},
			];
			
			$mmGrid = $('#table-m1').mmGrid({
				width:1500,
				height:'auto',
		        cols: cols,
		        fullWidthRows:true,
		        items:shopInfoJson,
		        checkCol: false,
		        autoLoad: true,
		        callback:function(){
		     
		        }
			});
		}
		
		function chooseOne(domEl){
			var $this = $(domEl).find("input[type='checkbox']");
			var $tr = $this.closest("tr");
			$tr.find("input.isDefaultCheckboxOne").prop("checked",false);
			$this.prop("checked",true);
			
			$tr.find("input.isDefaultOne").val("false");
			$(domEl).find("input.isDefaultOne").val("true");
		}
		
		function choose(domEl){
			var $this = $(domEl).find("input[type='checkbox']");
			var $tr = $this.closest("tr");
			$tr.find("input.isDefaultCheckbox").prop("checked",false);
			$this.prop("checked",true);
			
			$tr.find("input.isDefault").val("false");
			$(domEl).find("input.isDefault").val("true");
		}
    </script>
</head>
<body>
<div class="pathh">
    &nbsp;${message("经销商退出")}
</div>
<form id="inputForm" action="/member/store_exit/save.jhtml"
      method="post" type="ajax" validate-type="validate">
    <div class="tabContent">
        <table class="input input-edit">
            <tr>
                <th>${message("单号")}:</th>
                <td><input type="hidden" name="sid" value="${storeExit.store.id}" class="text" maxlength="200"
                           btn-fun="clear"/>
                    <input type="hidden" name="type" value="${type}" class="text" maxlength="200" btn-fun="clear"/>
                    <input type="hidden" name="id" value="${storeExit.id}" class="text" maxlength="200"
                           btn-fun="clear"/>
                    <input type="text" name="sn" value="${storeExit.sn}" class="text" maxlength="200" btn-fun="clear"
                           readonly/>
                </td>
                <th>${message("单据状态")}:</th>
                <td>
                    <!-- <input type="text" class="text" name="status" btn-fun="clear" readonly="readonly" /> -->
                    [#if storeExit.status == 0]${message("已保存")}[/#if]
                    [#if storeExit.status == 1]${message("已提交")}[/#if]
                    [#if storeExit.status == 2]${message("已终止")}[/#if]
                    [#if storeExit.status == 3]${message("进行中")}[/#if]
                    [#if storeExit.status == 4]${message("已完成")}[/#if]
                </td>
                <th>${message("经销商姓名")}:</th>
                <td><span class="dealerText">${storeExit.store.dealerName}</span>
                    <input type="hidden" class="text" name="dealer" value="${storeExit.store.dealerName}"
                           btn-fun="clear"/>
                </td>
                <th>${message("联系方式")}:</th>
                <td style="width: 230px">
                    <span class="phoneText">${storeExit.store.headPhone}</span>
                    <input type="hidden" class="text" name="phone" value="${storeExit.store.headPhone}" btn-fun="clear"/>
                </td>
            </tr>
            <tr>
                <th>${message("经营地区")}:</th>
                <td colspan="3" class="area">
[#--                    <span class="businessAddressText">${storeExit.store.headAddress}</span>--]
[#--                    <input type="hidden" class="text" name="businessAddress" value="${storeExit.store.headAddress}"--]
[#--                           btn-fun="clear"/>--]
                    <input type="hidden" id="newAreaId" name="headNewArea.id" value="${(storeExit.store.headNewArea.id)!}" treePath="${(storeExit.store.headNewArea.treePath)!}"/>
                    乡镇：[#if storeExit.store.countryName!=null]
                        <span style="border: solid 1px #c7c5cd; padding: 5px 15px 5px 15px">${(storeExit.store.countryName)!} </span>
                    [/#if]
                </td>
                <th><span class="requiredField">*</span>${message("经营期限")}:</th>
                <td>
                    <input class="t" name="businessYear" value="${storeExit.businessYear}" mindata="0"
                           oninput="editQtyEmpty(this,event)" onpropertychange="editQtyEmpty(this,event)" type="text"
                           style="width: 60px; border-bottom: 1px solid #dcdcdc; text-align: center;"/>
                    <span>年</span>
                </td>
                <th>${message("门店数量")}:</th>
                <td>
                    <input type="text" class="text" name="" value="${shopInfoCount}" mindata="0"
                           oninput="editQtyEmpty(this,event)" onpropertychange="editQtyEmpty(this,event)"
                           readonly="readonly" btn-fun="clear"/>
                </td>
            </tr>
            <tr>
                <th><span class="requiredField">*</span>${message("退出原因")}:</th>
                <td colspan="3">
					<label><input type="radio" name="exitCause" value="0" [#if storeExit.exitCause == 0]checked[/#if]/>经营不善</label>&nbsp;&nbsp;
					<label><input type="radio" name="exitCause" value="1" [#if storeExit.exitCause == 1]checked[/#if]/>资金不足</label>&nbsp;&nbsp;
					<label><input type="radio" name="exitCause" value="2" [#if storeExit.exitCause == 2]checked[/#if]/>经营其他品牌产品</label>&nbsp;&nbsp;
					<br/><label><input type="radio" name="exitCause" value="3" [#if storeExit.exitCause == 3]checked[/#if]/>其他原因：<input class="t" name="exitCauseOther" value="${storeExit.exitCauseOther}" maxlength="200" type="text" style="width: 80%; border-bottom: 1px solid #dcdcdc;" /></label>
                </td>
                <!-- <th><span class="requiredField">*</span>${message("门店状况")}:</th>
                <td>
                    <label><input type="radio" name="shopState" value="0" [#if storeExit.shopState == 0]checked[/#if]/>门店关闭</label>&nbsp;&nbsp;&nbsp;
                    <label><input type="radio" name="shopState" value="1" [#if storeExit.shopState == 1]checked[/#if] />门店已交接</label>
                </td>
                <th><span class="requiredField">*</span>${message("门店地址")}:</th>
                <td>
                    <input type="text" class="text" name="shopAddress" value="${storeExit.shopAddress}"
                           btn-fun="clear"/>
                </td> 
            </tr>
            <tr> -->
                <th><span class="requiredField">*</span>${message("退出时间")}:</th>
                <td>
                    <input type="text" name="exitTime" value="${storeExit.exitTime}" class="text" maxlength="200"
                           onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});"/>
                </td>
                <th><span class="requiredField">*</span>${message("退出状态")}:</th>
                <td>
                    <label><input type="radio" name="exitStatus" value="0"
                                  [#if storeExit.exitStatus == 0]checked[/#if] />自主退出</label>&nbsp;&nbsp;&nbsp;
                    <label><input type="radio" name="exitStatus" value="1"
                                  [#if storeExit.exitStatus == 1]checked[/#if]/>终止经销关系</label>&nbsp;&nbsp;&nbsp;
                </td>
            </tr>
            <tr>
                <th rowspan="3"><span class="requiredField">*</span>${message("品牌保证金")}:</th>
                <td colspan="3">
                    <label><input type="radio" name="brandMargin" value="0"
                                  [#if storeExit.brandMargin == '0']checked[/#if] />保证金缴纳到公司总部</label>&nbsp;&nbsp;&nbsp;
                    <label><input type="radio" name="brandMargin" value="1"
                                  [#if storeExit.brandMargin == '1']checked[/#if]/>保证金缴纳到总经销商</label>&nbsp;&nbsp;&nbsp;
                    <label><input type="radio" name="brandMargin" value="2"
                                  [#if storeExit.brandMargin == '2']checked[/#if]/>保证金缴纳到省运营中心</label>&nbsp;&nbsp;&nbsp;
                </td>
                <th rowspan="2">${message("保证金退还银行帐户信息")}:</th>
                <td colspan="3">
                    <input type="hidden" id="bankAreaId" name="bankArea.id" value="${storeExit.bankArea.id}" treePath="${storeExit.bankArea.treePath}"/>
                    <input class="t" name="openBank" value="${storeExit.openBank}" type="text"
                                          style="width: 65px; height: 28px; border: 1px solid #dcdcdc; padding: 0 4px; border-radius: 2px;"/>
                    <span>银行</span> <input class="t" name="openBranch" value="${storeExit.openBranch}" type="text"
                                           style="width: 65px; height: 28px; border: 1px solid #dcdcdc; padding: 0 4px; border-radius: 2px;"/>
                    <span>支行</span>
                </td>
            </tr>
            <tr>
                <td colspan="3">
                    <label><input type="radio" name="brandReceipt" value="0"
                                  [#if storeExit.brandReceipt == '0']checked[/#if] />有公司开具收据</label>&nbsp;&nbsp;&nbsp;
                    <label><input type="radio" name="brandReceipt" value="1"
                                  [#if storeExit.brandReceipt == '1']checked[/#if] />无公司开具收据</label>&nbsp;&nbsp;&nbsp;
                    <label><input type="radio" name="brandReceipt" value="2"
                                  [#if storeExit.brandReceipt == '2']checked[/#if]/>收据丢失</label>&nbsp;&nbsp;&nbsp;
                    <label> <input type="radio" name="brandReceipt" value="3"
                                   [#if storeExit.brandReceipt == '3']checked[/#if] />其他：<input class="t"
                                                                                                name="brandReceiptOther"
                                                                                                value="${storeExit.brandReceiptOther!''}"
                                                                                                type="text"
                                                                                                style="width: 120px; border-bottom: 1px solid #dcdcdc;"/></label>
                </td>
                <td colspan="3">
                    <span>开户姓名：</span><input class="t" name="openMaster" value="${storeExit.openMaster}" type="text"
                                              style="width: 130px; height: 28px; border: 1px solid #dcdcdc; padding: 0 4px; border-radius: 2px;"/>
                    <span>开户账号：</span><input class="t" name="openAccount" value="${storeExit.openAccount}" type="text"
                                              style="width: 180px; height: 28px; border: 1px solid #dcdcdc; padding: 0 4px; border-radius: 2px;"/>
                </td>
            </tr>
            <tr>
                <td>
                    <span>缴纳金额：</span> <input class="t acreage" name="payAmount" value="${storeExit.payAmount}"
                                              mindata="0" oninput="editQty(this,event)"
                                              onpropertychange="editQty(this,event)" type="text"
                                              style="width: 100px; border-bottom: 1px solid #dcdcdc; text-align: center;"/>
                </td>
                <th>${message("缴纳时间")}:</th>
                <td>
                    <input type="text" name="payTime" value="${storeExit.payTime}" class="text" maxlength="200"
                           onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',readOnly:true});"/>
                </td>

                <th>${message("核心经销商")}:</th>
                <td>
	                [#if storeExit.store.isCoreStroe == true]是[/#if]
	                [#if storeExit.store.isCoreStroe == false]否[/#if]
                </td>
                <th></th>
                <td></td>
            </tr>
        </table>
        <!-- 门店关系 -->
	    <div class="title-style">${message("门店关系")}</div>
	    <table id="table-m1"></table>

        <div style="margin: 15px 0 0 0;">
            <span style="color: rgba(227, 18, 25, 1);">★提示：上述内容属实，本人确认并承诺自退出之日7个工作日撤除大自然相关产品、物料。</span>
        </div>

        <div class="title-style">${message("附件")}:</div>
        <table class="input input-edit">
            <tr>
                <th style="width: 100px;"><span class="requiredField">*</span>${message("退出/交接协议申请签字版")}:</th>
                <td>
                    <a href="javascript:;" id="addStoreExitAttach" class="button">添加附件</a>
                </td>
            </tr>
        </table>
        <div>
            <span class="title-style fujian">${message("退出/交接协议申请签字版")}:</span><span class="toPreviewBtn button">浏览全图</span>
            <table id="table-storeExitAttach" style="width:850px"></table>
        </div>
		<div style="margin: 15px 0 0 0;">
			<span style="color: rgba(227, 18, 25, 1);">★提示：退出，交接协议申请签字版原件寄回公司。</span>
		</div>

        <div class="title-style">
            ${message("区域经理意见：")}
            [#--[#if node.name?contains("区域经理")]<input type="button" class="bottonss tj" onclick="saveform(1)" value="提交"/>[/#if]--]
            <div
                    style="border: 1px solid #dcdcdc; margin-top: 8px; padding: 10px 15px; font: 13px 'Microsoft YaHei';">
                <div style="margin: 0 0 10px 0;">
                    <span class="requiredField">*</span><span>经销商是否有欠款：</span>
                    <label> <input type="radio" name="whetherArrears" value="0"
                                   [#if storeExit.whetherArrears == 0]checked[/#if]
                                   onchange="handle(this)"/>有，金额：</label>
                    <input class="t acreage" name="arrearsMoney" value="${storeExit.arrearsMoney!''}" mindata="0"
                           oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text"
                           style="width: 60px; border-bottom: 1px solid #dcdcdc; text-align: center;"/>&nbsp;&nbsp;&nbsp;
                    <label> <input type="radio" name="whetherArrears" value="1"
                                   [#if storeExit.whetherArrears == 1]checked[/#if] />无</label>
                </div>
                <div style="margin: 0 0 10px 0;">
                    <label>    <!-- <input type="checkbox" name="dealerContact" value="0" /> -->经销商无法联系：</label>
                    <label><input type="checkbox" name="dealerContact" value="1"
                                  [#if storeExit.dealerContact?contains('1')]checked[/#if]>①电话拒接或关机</label>&nbsp;&nbsp;&nbsp;
                    <label><input type="checkbox" name="dealerContact" value="2"
                                  [#if storeExit.dealerContact?contains('2')]checked[/#if]/>②携款潜逃 </label>&nbsp;&nbsp;&nbsp;
                    <label><input type="checkbox" name="dealerContact" value="3"
                                  [#if storeExit.dealerContact?contains('3')]checked[/#if]/>③门店关门</label>&nbsp;&nbsp;&nbsp;
                    <label><input type="checkbox" name="dealerContact" value="4"
                                  [#if storeExit.dealerContact?contains('4')]checked[/#if]/>④门店已转让</label>&nbsp;&nbsp;&nbsp;
                    <label><input type="checkbox" name="deaflerContact" value="5"
                                  [#if storeExit.dealerContact?contains('5')]checked[/#if]/>⑤私自与他人交接</label>&nbsp;&nbsp;&nbsp;
                    <label><input type="checkbox" name="dealerContact" value="6"
                                  [#if storeExit.dealerContact?contains('6')]checked[/#if]/>⑥经销商无法联系，店员营业</label>
                </div>
                <div>
                    <div style="margin: 0 0 5px 0;"><span class="requiredField">*</span>意见:</div>
                    <textarea rows="3" name="areaManagerOpinion"
                              style="width: 85%; border: 1px solid #dcdcdc; padding: 5px;">${storeExit.areaManagerOpinion}</textarea>
                </div>
            </div>
        </div>
        <!-- 审核相关内容-->
        
            [#if szsh ||szshs]
                <div class="title-style">
                    <div class="title-style">${message("省长意见")}:[#if node.name?contains("省长")]<input type="button" class="bottonss tj" onclick="saveform(1)" value="提交"/>[/#if]
                    </div>
                    <div style="border: 1px solid #dcdcdc; margin-top: 8px; padding: 5px 8px; font: 13px 'Microsoft YaHei';">
                        <div>
                            <textarea rows="3" name="governorOpinion" style="width: 100%;">${storeExit.governorOpinion}</textarea>
                        </div>
                    </div>
                </div>
            [/#if]

            [#if qdzy || qdzys]
                <div class="title-style">
                	${message("渠道部意见")}:
                    [#if node.name?contains("渠道")]
                    <input type="button" class="bottonss tj" onclick="saveform(2)" value="提交"/>
                    [/#if]
                </div>
                <table class="input input-edit">
					<tr>
						<th><span class="requiredField">*</span>${message("解约时间")}:</th>
		                <td>
		                    <input type="text" name="cancelDate" value="${storeExit.cancelDate}" class="text" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd HH:mm:ss',startDate:'%y-%M-%d',readOnly:true});"/>
		                </td>
						<th><span class="requiredField">*</span>${message("解约档案编号")}:</th>
						<td>
							<input type="text" name="unfileNumber" value="${storeExit.unfileNumber}" class="text" maxlength="200"  btn-fun="clear" />
						</td>
						<th><span class="requiredField">*</span>${message("解约原因")}:</th>
						<td>
							<input type="text" name="cancelReason" value="${storeExit.cancelReason}" class="text" maxlength="200" btn-fun="clear" />
						</td>
						<th></th>
						<td></td>
					</tr>
					<!-- <tr>
						<th>${message("解约原因")}:</th>
						<td colspan="7">
		                    <textarea class="text" name="" maxlength="4000" style="width:800px;height:160px;"></textarea>
		                </td>
					</tr> -->
					<tr>
						<th>${message("意见")}:</th>
						<td colspan="7">
		                    <textarea class="text" name="channelOpinion" maxlength="4000" style="width:800px;height:160px;">${storeExit.channelOpinion}</textarea>
		                </td>			
					</tr>
				</table>
           	[/#if]

        [#--[#if qdzy||qdzys]
            <div class="title-style">
                <div class="title-style">${message("销售中心意见")}:[#if node.name?contains("渠道")]<input type="button" class="bottonss tj" onclick="saveform(3)" value="提交"/>[/#if]</div>
                <div style="border: 1px solid #dcdcdc; margin-top: 8px; padding: 5px 8px; font: 13px 'Microsoft YaHei';">
                    <div>
                        <textarea rows="3" name="channelOpinion" style="width: 100%;">
                            ${storeExit.channelOpinion}
                        </textarea>
                    </div>
                </div>
            </div>
        [/#if]--]
        
    </div>
    <div class="fixed-top">
        [#if storeExit.wfId == null]
            <a id="shengheButton" class="iconButton" onclick="check_wf(this)"><span
                        class="ico-shengheIcon">&nbsp;</span>${message("审核")}</a>
            <input type="button" id="submit_button" onclick="save(this)" class="button sureButton"
                   value="${message("保存")}"/>

        [#--<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" autocomplete="off" />--]
        [/#if]
        <input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}">
    </div>
</form>

<!-- 全屏看图结构 -->
<div class="preview_swiperBox">
  <div id="picture-swiperLeft" class="swiperButton swiper-button-prev"></div>
  <div id="picture-swiperRight" class="swiperButton swiper-button-next"></div>
  <div id="picture-colse" class="swiper-button-close">
    <span></span>
    <span></span>
  </div>
  <div class="preview_control">
    <div id="scaleBig" class="scaleBig"><span></span></div>
    <div id="scaleSmall" class="scaleSmall"><span></span></div>
    <div id="previewReset" class="previewReset"><span></span></div>
    <div id="rotateLeft" class="rotateLeft"><span></span></div>
    <div id="rotateRight" class="rotateRight"><span></span></div>
  </div>
</div>
<!-- 全屏看图结构 end-->

<div id="wf_area" style="width:100%"></div>
</body>
</html>