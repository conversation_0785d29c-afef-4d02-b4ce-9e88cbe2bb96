<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("经销商退出")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript"
	src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/base/file.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<style>
.upload-list {width:82px;display: inline-block;padding-top:0px;margin-right:10px;}
label.fieldError {opacity:1;}
</style>
<script type="text/javascript">
function editQty(t,e){
	extractNumber(t,3,false,e);
}
//没有小数位
function editQtyEmpty(t,e,n){
    var rate = 0;
    if(n!=undefined)rate = n;
    extractNumber(t,rate,false,e)
}
$().ready(function() {
	var $inputForm = $("#inputForm");
	
	// 校验
	$inputForm.validate({
		rules: {
			businessYear: "required",
			exitCause : "required",
			//shopState: "required",
			//shopAddress: "required",
			exitTime: "required",
			exitStatus: "required",
			brandMargin: "required",
			payAmount: "required",
			whetherArrears : "required",
			brandReceipt : "required",
			openProvince : "required",
			openCity: "required",
			areaManagerOpinion: "required",
		} ,
		messages: {
			exitCause: {
				required: "<br>请选择退出原因"
			},
			shopState: {
				required: "<br>请选择门店状况"
			}, 
			exitStatus: {
				required: "<br>请选择退出状态"
			}, 
			brandMargin: {
				required: "<br>请选择一个选项"
			},
			brandReceipt: {
				required: "<br>请选择一个选项"
			},
			whetherArrears : {
				required: "<br>请选择经销商是否有欠款"
			},
			
		},
		errorPlacement: function (error, element) { //指定错误信息位置
			if (element.is(':radio') || element.is(':checkbox')) { //如果是radio或checkbox
		        var eid = element.attr('name'); //获取元素的name属性
		        error.appendTo(element.parent().parent()); //将错误信息添加当前元素的父结点后面
		    } else {
		        error.insertAfter(element);
		    }
		}
	});

	initAttach("storeExitAttachs", "addStoreExitAttach", "table-storeExitAttach", 1);
    initAttach("storeReceiptAttachs", "addStoreReceiptAttach", "table-storeReceiptAttach", 2);

	$("#bankAreaId").lSelect();
	$("#newAreaId").lSelect();
	fixAddress();
	
	
	initShopInfo(${shopInfos});
	
});

// 保存
function save(e){
	var str = '您确定要保存吗？';
	var url = '/member/store_exit/save.jhtml';
	var $form = $("#inputForm");
	if($form.valid()){
		ajaxSubmit(e,{
			url: url,
			data:$("#inputForm").serialize(),
			method: "post",
			isConfirm:true,
			confirmText:str,
			callback:function(resultMsg){
				$.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					//location.href= 'edit/b.jhtml?id='+resultMsg.objx;
                    location.href= '/member/store_exit/edit/${code}.jhtml?id='+resultMsg.objx;
                })
				$.message_alert("操作成功")
			}
		});
	}
}

//只有选中复选框后才能编辑后面的文本框
function handle(e) {
    if ($(e).prop('checked') == true) {
        var $pdiv = $(e).parent("div");
        $pdiv.find('input[type=text]').removeAttr("readonly");
    } else {
        var $pdiv = $(e).parent("div");
        $pdiv.find('input[type=text]').attr("readonly", "readonly");
        $pdiv.find('input[type=text]').val("");
    }
}
// 只有算是选中复选框后才能编辑后面的日期
function handleDate(e) {
    if ($(e).prop('checked') == true) {
        var $pdiv = $(e).parent("div");
        $pdiv.find('input[type=text]').removeAttr("disabled");
    } else {
        var $pdiv = $(e).parent("div");
        $pdiv.find('input[type=text]').attr("disabled", "disabled");
        $pdiv.find('input[type=text]').val("");
    }
}
// 只有选中复选框后才能编辑后面的单选框
function handleRadio(e) {
    if ($(e).prop('checked') == true) {
        var $pdiv = $(e).parent("div");
        $pdiv.find('input[type=radio]').removeAttr("disabled");
    } else {
        var $pdiv = $(e).parent("div");
        $pdiv.find('input[type=radio]').attr("disabled", "disabled");
        $pdiv.find('input[type=radio]').removeAttr("checked");
    }
}

/**
 * 初始化附件，每个参数都是必填的
 * @param paramAttachs 后台实体类接收附件的参数名
 * @param addAttachIdName 前端页面添加附件按钮的id值
 * @param tableIdName 前端页面table中的id值
 * @param type 后台用来区分不同类型附件
 */
function initAttach(paramAttachs, addAttachIdName, tableIdName, type) {
    var index = 0;
	var attachCols = [
    	{ title:'${message("附件")}',name:'content',width:260,align:'center',renderer:function(val,item,rowIndex){
    		var url = item.url;
			var fileObj = getfileObj(item.name);
			/**设置隐藏值*/
			var hideValues = {};
			hideValues[paramAttachs+'['+index+'].url']=url;
			hideValues[paramAttachs+'['+index+'].suffix']=fileObj.suffix;
			hideValues[paramAttachs+'['+index+'].type']=type;

			return createFileStr({
				url : url,
				fileName : fileObj.file_name,
				name : fileObj.name,
				suffix : fileObj.suffix,
				time : '',
				textName: paramAttachs+'['+index+'].name',
				hideValues:hideValues
			});
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:580 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text" name="'+paramAttachs+'['+index+'].memo" >'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
    		index++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}}
	];
	var $grid=$('#'+tableIdName).mmGrid({
		fullWidthRows:true,
		width:'1147',
		height:'auto',
        cols: attachCols,
        checkCol: false,
        autoLoad: true
    });

    var $addAttach = $("#"+addAttachIdName);
	var attachIdnex = 0;
	var option = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row = data[i].file_info;
				$grid.addRow(row,null,1);
	        }
        }
    }
    $addAttach.file_upload(option);

	/* 删除附件 */
    var $deleteAttachment = $(".deleteAttachment");
	$deleteAttachment.live("click", function() {
		var $this = $(this);
		$this.closest("tr").remove();
	});
}

//固定地址下拉框
function fixAddress() {
	$(".area").each(function (index) {
		$(this).find("select").each(function () {
			$(this).attr("disabled", true);
		});
	})
}

function initShopInfo(shopInfoJson){

	var rowIndex=0;
	var items=[];
	
	var cols = [
		{ title:'${message("门店单号")}', name:'sn',width:80,align:'center'},
		{ title:'${message("门店授权编号")}', name:'authorization_code',width:80,align:'center'},
		{ title:'${message("门店")}', name:'shop_name',width:80,align:'center',renderer: function(val,item,rowIndex){
			var html = '<span>'+val+'</span>';
			html += '<input type="hidden" name="seList['+rowIndex+'].shopInfo.id" value="'+item.id+'" />'
			return html;
		}},
		{ title:'${message("门店地址")}', name:'address',width:200,align:'center'},
		{ title:'${message("门店关闭")}', name:'isMeasure',width:20,align:'center',renderer: function(val,item,rowIndex){
			var html = '<label onClick="chooseOne(this)">';
			html += '<input type="checkbox" name="seList['+rowIndex+'].isClose" value="true" class="isDefaultCheckbox" />';
			html += '<input type="hidden" name="seList['+rowIndex+']._isClose" value="false" class="isDefault" />';
			html += '</label>';
			return html;
		}},
		{ title:'${message("门店交接")}', name:'isInstall' ,align:'center',width:20,renderer: function(val,item,rowIndex){
			var html = '<label onClick="choose(this)">';
			html += '<input type="checkbox" name="seList['+rowIndex+'].isExit" value="true" checked="true" class="isDefaultCheckboxOne" />';
			html += '<input type="hidden" name="seList['+rowIndex+']._isExit" value="false" class="isDefaultOne" />';
			html += '</label>';
			rowIndex++;
			return html;
		}},
		{ title:'门店减少档案',name:'', align:'center',width:80 ,renderer: function(val,item,rowIndex){
				return '<input type="text" name="seList['+rowIndex+'].decreaseCode" class="text" value="'+val+'"/>';
		}},
		{ title:'日期',name:'',align:'center',width:80 ,renderer: function(val,item,rowIndex){
				return '<input name="seList['+rowIndex+'].offTime" class="text" value="'+val+'" onClick="WdatePicker({dateFmt: \'yyyy-MM-dd HH:mm:ss\',startDate:\'%y-%M-%d %H:%m:%s\',readOnly:true})" type="text" btn-fun="clear"/>';
		}},
		{ title:'原因',name:'', align:'center',width:120 ,renderer: function(val,item,rowIndex){
				return '<input type="text" name="seList['+rowIndex+'].cause" class="text " value="'+val+'"/>';
		}},
	];
	
	$mmGrid = $('#table-m1').mmGrid({
		width:1500,
		height:'auto',
        cols: cols,
        fullWidthRows:true,
        items:shopInfoJson,
        checkCol: false,
        autoLoad: true,
        callback:function(){
     
        }
	});
}

function chooseOne(domEl){
	var $this = $(domEl).find("input[type='checkbox']");
	var $tr = $this.closest("tr");
	$tr.find("input.isDefaultCheckboxOne").prop("checked",false);
	$this.prop("checked",true);
	
	//$tr.find("input.isDefault").val("false");
	//$(domEl).find("input.isDefault").val("true");
}

function choose(domEl){
	var $this = $(domEl).find("input[type='checkbox']");
	var $tr = $this.closest("tr");
	$tr.find("input.isDefaultCheckbox").prop("checked",false);
	$this.prop("checked",true);
	
	//$tr.find("input.isDefault").val("false");
	//$(domEl).find("input.isDefault").val("true");
}
</script>
</head>
<body>
	<div class="pathh">&nbsp;${message("经销商退出信息")}</div>
	<form id="inputForm" action="/member/store_exit/save.jhtml" method="post" type="ajax" validate-type="validate">
		<div class="tabContent">
			<table class="input input-edit">
				<tr>
					<th>${message("经销商姓名")}:</th>
					<td>
						<span class="dealerText">${store.dealerName}</span> 
						<input type="hidden" name="sid" value="${store.id}" class="text" maxlength="200" btn-fun="clear" /> 
						<input type="hidden" name="type" value="${type}" class="text" maxlength="200" btn-fun="clear" />
						<input type="hidden" class="text" name="dealer" value="${store.dealerName}" btn-fun="clear" />
					</td>
					<th>${message("联系方式")}:</th>
					<td style="width: 230px">
						<span class="phoneText">${store.headPhone}</span>
						<input type="hidden" class="text" name="phone" value="${store.headPhone}" btn-fun="clear" />
					</td>
					<th>${message("授权编号")}:</th>
					<td>
						<span>${store.grantCode}</span>
					</td>
					<th>${message("经销商类别")}:</th>
					<td>
						[#if store.category == '0']<span>一城多商</span>[/#if]
						[#if store.category == '1']<span>一城一商</span>[/#if]
					</td>
				</tr>
				<tr>
					<th>${message("经营地区")}:</th>
					<td colspan="3" class="area">
[#--						<span class="businessAddressText">${store.headAddress}</span>--]
[#--						<input type="hidden" class="text" name="businessAddress"--]
[#--						value="${store.headAddress}" btn-fun="clear" />--]
						<input type="hidden" id="newAreaId" name="headNewArea.id" value="${(store.headNewArea.id)!}" treePath="${(store.headNewArea.treePath)!}"/>
					</td>
					
					<th><span class="requiredField">*</span>${message("经营期限")}:</th>
					<td>
						<input class="t" name="businessYear" value="${dateLength}" mindata="0" 
						oninput="editQtyEmpty(this,event)" onpropertychange="editQtyEmpty(this,event)"
						type="text" style="width: 60px; border-bottom: 1px solid #dcdcdc; text-align: center;" />
						<span>年</span>
					</td>
					<th>${message("门店数量")}:</th>
					<td>
						<input type="text" class="text" name="shopNumber" value="${shopInfoCount}" mindata="0"
						oninput="editQtyEmpty(this,event)" onpropertychange="editQtyEmpty(this,event)" readonly="readonly" btn-fun="clear" />
					</td>
				</tr>
				<tr>
					<th><span class="requiredField">*</span>${message("退出原因")}:</th>
					<td colspan="3">
						<label><input type="radio" name="exitCause" value="0" />经营不善</label>&nbsp;&nbsp;&nbsp;
						<label><input type="radio" name="exitCause" value="1" />资金不足</label>&nbsp;&nbsp;&nbsp;
						<label><input type="radio" name="exitCause" value="2" />经营其他品牌产品</label>&nbsp;&nbsp;&nbsp;
						<br/><label><input type="radio" name="exitCause" value="3" />其他原因：<input class="t" name="exitCauseOther" maxlength="200" type="text" style="width: 80%; border-bottom: 1px solid #dcdcdc;" />
					</label>
					</td>
					<!-- <th><span class="requiredField">*</span>${message("门店状况")}:</th>
					<td>
						<label><input type="radio" name="shopState" value="0" />门店关闭</label>&nbsp;&nbsp;&nbsp;
						<label><input type="radio" name="shopState" value="1" />门店已交接</label>
					</td>
					<th><span class="requiredField">*</span>${message("门店地址")}:</th>
					<td>
						<input type="text" class="text" name="shopAddress" btn-fun="clear" />
					</td> -->
				</tr>
				<tr>
					<th><span class="requiredField">*</span>${message("退出时间")}:</th>
					<td>
						<input type="text" name="exitTime" class="text" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" />
					</td>
					<th><span class="requiredField">*</span>${message("退出状态")}:</th>
					<td>
						<label><input type="radio" name="exitStatus" value="0" />自主退出</label>&nbsp;&nbsp;&nbsp;
						<label><input type="radio" name="exitStatus" value="1" />终止经销关系</label>&nbsp;&nbsp;&nbsp;
					</td>
					<th></th>
					<td></td>
					<th></th>
					<td></td>
				</tr>
				<tr>
					<th rowspan="3"><span class="requiredField">*</span>${message("品牌保证金")}:</th>
					<td colspan="3">
						<label><input type="radio" name="brandMargin" value="0" />保证金缴纳到公司总部</label>&nbsp;&nbsp;&nbsp;
						<label><input type="radio" name="brandMargin" value="1" />保证金缴纳到总经销商</label>&nbsp;&nbsp;&nbsp;
						<label><input type="radio" name="brandMargin" value="2" />保证金缴纳到省运营中心</label>&nbsp;&nbsp;&nbsp;
					</td>
					<th rowspan="2">${message("保证金退还银行帐户信息")}:</th>
					<td colspan="3">
						<input type="hidden" id="bankAreaId" name="bankArea.id" value="" treePath=""/>
						<input class="t" name="openBank" value="" type="text" style="width: 65px; height: 28px; border: 1px solid #dcdcdc; padding: 0 4px; border-radius: 2px;" />
						<span>银行</span> <input class="t" name="openBranch" value="" type="text" style="width: 65px; height: 28px; border: 1px solid #dcdcdc; padding: 0 4px; border-radius: 2px;" />
						<span>支行</span>
					</td>
				</tr>
				<tr>
					<td colspan="3">
						<label><input type="radio" name="brandReceipt" value="0" />有公司开具收据</label>&nbsp;&nbsp;&nbsp;
						<label><input type="radio" name="brandReceipt" value="1" />无公司开具收据</label>&nbsp;&nbsp;&nbsp;
						<label><input type="radio" name="brandReceipt" value="2" />收据丢失</label>&nbsp;&nbsp;&nbsp;
						<label> <input type="radio" name="brandReceipt" value="3" />其他：
							<input class="t" name="brandReceiptOther" value="" type="text" style="width: 120px; border-bottom: 1px solid #dcdcdc;" /></label>
					</td>
					<td colspan="3">
						<span>开户姓名：</span><input class="t" name="openMaster" value="" type="text" style="width: 130px; height: 28px; border: 1px solid #dcdcdc; padding: 0 4px; border-radius: 2px;" />
						<span>开户账号：</span><input class="t" name="openAccount" value="" type="text" style="width: 180px; height: 28px; border: 1px solid #dcdcdc; padding: 0 4px; border-radius: 2px;" />
					</td>
				</tr>
				<tr>
					<td>
						<span>缴纳金额：</span> <input class="t acreage" name="payAmount" value="${store.realCautionPaid}" mindata="0" oninput="editQty(this,event)" onpropertychange="editQty(this,event)" type="text" style="width: 100px; border-bottom: 1px solid #dcdcdc; text-align: center;" />
					</td>
					<th>${message("缴纳时间")}:</th>
					<td>
						<input type="text" name="payTime" class="text" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d',readOnly:true});" />
					</td>
                    <th>${message("核心经销商")}:</th>
	                <td>
		                [#if store.isCoreStroe == true]是[/#if]
		                [#if store.isCoreStroe == false]否[/#if]
	                </td>
                    <th></th>
                    <td></td>
				</tr>
			</table>
			
			<!-- 门店关系 -->
		   <div class="title-style">
			${message("门店关系")}
		    </div>
		    <table id="table-m1"></table>

			<div style="margin: 15px 0 0 0;">
				<span style="color: rgba(227, 18, 25, 1);">★提示：上述内容属实，本人确认并承诺自退出之日7个工作日撤除大自然相关产品、物料。</span>
			</div>
			
			<div class="title-style">${message("附件")}:</div>
		   	<table class="input input-edit">
		        <tr>
            		<th style="width: 100px;"><span class="requiredField">*</span>${message("退出/交接协议申请签字版")}:</th>
            		<td>
            			<a href="javascript:;" id="addStoreExitAttach" class="button">添加附件</a>
            		</td>
[#--            		<th><span class="requiredField">*</span>${message("品牌保证金收据")}:</th>--]
[#--            		<td>--]
[#--            			<a href="javascript:;" id="addStoreReceiptAttach" class="button">添加附件</a>--]
[#--            		</td>--]
            	</tr>
			</table>
        	<div>
            	<span>${message("退出/交接协议申请签字版")}</span>
		        <table id="table-storeExitAttach" style="width:850px"></table>
[#--		        <span>${message("品牌保证金收据")}</span>--]
[#--		        <table id="table-storeReceiptAttach" style="width:850px"></table>--]
	        </div>
        	<div style="margin: 15px 0 0 0;">
				<span style="color: rgba(227, 18, 25, 1);">★提示：退出，交接协议申请签字版原件寄回公司。</span>
			</div>
	        
            <div class="title-style">
			${message("区域经理意见：")}
                <div style="border: 1px solid #dcdcdc; margin-top: 8px; padding: 10px 15px; font: 13px 'Microsoft YaHei';">
                    <div style="margin: 0 0 10px 0;">
						<span class="requiredField">*</span><span>经销商是否有欠款：</span> <label>
						<input type="radio" name="whetherArrears" value="0" onchange="handle(this)" />有，金额：
                    </label> <input class="t acreage" name="arrearsMoney" value="" mindata="0" oninput="editQty(this,event)"
                                    onpropertychange="editQty(this,event)" type="text"
                                    style="width: 60px; border-bottom: 1px solid #dcdcdc; text-align: center;" />&nbsp;&nbsp;&nbsp;
                        <label> <input type="radio" name="whetherArrears"
                                       value="1" />无
                        </label>
                    </div>
                    <div style="margin: 0 0 10px 0;">
                        <label>
                            [#--<input type="checkbox" name="dealerContact" value="0" /> &ndash;&gt;--]经销商无法联系：
						</label> <label><input type="checkbox" name="dealerContact"
							value="1" />①电话拒接或关机</label>&nbsp;&nbsp;&nbsp; <label><input
							type="checkbox" name="dealerContact" value="2" />②携款潜逃</label>&nbsp;&nbsp;&nbsp;
						<label><input type="checkbox" name="dealerContact"
							value="3" />③门店关门</label>&nbsp;&nbsp;&nbsp; <label><input
							type="checkbox" name="dealerContact" value="4" />④门店已转让</label>&nbsp;&nbsp;&nbsp;
						<label><input type="checkbox" name="dealerContact"
							value="5" />⑤私自与他人交接</label>&nbsp;&nbsp;&nbsp; <label><input
							type="checkbox" name="dealerContact" value="6" />⑥经销商无法联系，店员营业</label>
					</div>
					<div>
						<div style="margin: 0 0 5px 0;"><span class="requiredField">*</span>意见:</div>
						<textarea rows="3" name="areaManagerOpinion"
							style="width: 85%; border: 1px solid #dcdcdc; padding: 5px;"></textarea>
					</div>
				</div>
			</div>


			[#--
<!-- 一隐藏审核相关内容-->
			<div class="title-style">
				${message("省长意见")}：
				<div
					style="border: 1px solid #dcdcdc; margin-top: 8px; padding: 5px 8px; font: 13px 'Microsoft YaHei';">
					<div>
						<textarea rows="3" name="governorOpinion" style="width: 100%;"></textarea>
					</div>
				</div>
			</div>

			<div class="title-style">
				${message("渠道部意见")}：
				<div
					style="border: 1px solid #dcdcdc; margin-top: 8px; padding: 5px 8px; font: 13px 'Microsoft YaHei';">
					<div>
						<textarea rows="3" name="channelOpinion" style="width: 100%;"></textarea>
					</div>
				</div>
			</div>
			--]

		</div>
        <div class="fixed-top">

		[#--<input type="submit" id="submit_button" class="button sureButton" value="${message("1013")}" autocomplete="off" />--]
            <input type="button" id="submit_button" onclick="save(this)" class="button sureButton" value="${message("保存")}" />
            <input type="button" onclick="location.reload(true);" class="button resetButton ml15" value="${message("重置")}"/>
        </div>
	</form>
</body>
</html>