<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("经销商终止申请")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript"
	src="/resources/js/reference/jquery.tools.js"></script>
<script type="text/javascript"
	src="/resources/js/reference/jquery.validate.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js?version=0.02"></script>
<script type="text/javascript" src="/resources/js/base/file.js?version=0.02"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<link href="/resources/css/preview-swiper.min.css?version=0.01" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="/resources/js/reference/preview-swiper.min.js"></script>

<style>
/* 调整附件 */
.nowrap>div>.t-edit {
	width: 80%;
	margin: 5px 0 -6px 0;
}
</style>
<script type="text/javascript">
function editQty(t,e){
	extractNumber(t,3,false,e);
}
$().ready(function() {
	var $inputForm = $("#inputForm");
	
	var $areaId = $("#areaId");
	var $bAreaId = $("#bAreaId");
	
	//地区选择
	$("#regionAreaId").lSelect();
	$("#expressAreaId").lSelect();
	$areaId.lSelect();
	$bAreaId.lSelect();

	
	// 校验
	$inputForm.validate({
		rules: {
            letters:"required",
			whetherSignedContract:"required",
			whetherSignature:"required",
			whetherSubmitPlan:"required",
			rectificationContent:"required",
		}
	});
	
	$(".area").find("select").each(function(){
			$(this).attr("disabled",true);
	});

	// 全屏看图功能 start
  $(".toPreviewBtn").click(function () {
    let a_href = $(this).next().children().find(".real-row .pic a")
    let listData = []
    for(var i = 0; i < a_href.length; i++) {
      let pathname_url = a_href[i].pathname
      let url_index = pathname_url.lastIndexOf('.');
      let getPath = pathname_url.substring(url_index + 1, url_index.length);
      if (getPath == "jpg" || getPath == "jpeg" || getPath == "png" || getPath == "gif" || getPath == "mp3" || getPath == "mp4" || getPath == "avi") {
        let item = a_href[i]
        listData.push(item)
      }
    }

    if (listData.length == 0) {
      alert('当前没有可浏览的图片附件')
      return
    }

    if (listData.length > 0) {
      $(".preview_swiperBox").fadeIn(300)
    }
    if (listData.length <= 1) {
      $("#picture-swiperLeft").hide()
      $("#picture-swiperRight").hide()
    } else {
      $("#picture-swiperLeft").show()
      $("#picture-swiperRight").show()
    }
    let str= "<div class='swiper-container'>";
    str+= "<div class='swiper-wrapper'>";
    $.each(listData, function(index, item){
      let itemHref = item.href
      let item_Url = itemHref.lastIndexOf('.');   
      let item_path = itemHref.substring(item_Url + 1, itemHref.length);
      str+= "<div class='swiper-slide'>";
      if (item_path == 'mp3' || item_path == 'mp4' || item_path == 'avi') {
        str+= "<video controls>";
        str+= "<source src='" + itemHref + "' type='video/mp4'>";
        str+= "</video>";
      } else {
        str+= "<img src='" + itemHref + "' />"
      }
      str+= "</div>"
    });  
    str+= "</div>";
    str+= "<div class='swiper-pagination'></div>";
    str+= "</div>";
    $(".preview_swiperBox").append(str);

    var swiper = new Swiper('.swiper-container', {
      slidesPerView: 1,
      spaceBetween: 0,
      loop: true,
      pagination: {
        el: '.swiper-pagination',
        clickable: true,
      },
      navigation: {
        nextEl: '#picture-swiperRight',
        prevEl: '#picture-swiperLeft',
      },
      on: {
        touchEnd: function(swiper,event){
          rotateParam = 0
          scaleParam = 1
          $(".swiper-wrapper .swiper-slide-next img").css({"transform": "none", "transition": "none"})
          $(".swiper-wrapper .swiper-slide-prev img").css({"transform": "none", "transition": "none"})
        },
      },
    });
  });

  var rotateParam = 0
  var scaleParam = 1

// 关闭全屏看图
$("#picture-colse").click(function () {
  $(".preview_swiperBox").fadeOut(300);
  $(".swiper-container").remove();
  rotateParam = 0
  scaleParam = 1
  $(".swiper-wrapper .swiper-slide-next img").css({"transform": "none", "transition": "none"})
  $(".swiper-wrapper .swiper-slide-prev img").css({"transform": "none", "transition": "none"})
});
$("#picture-swiperLeft").click(function () {
  rotateParam = 0
  scaleParam = 1
  $(".swiper-wrapper .swiper-slide-next img").css({"transform": "none", "transition": "none"})
  $(".swiper-wrapper .swiper-slide-prev img").css({"transform": "none", "transition": "none"})
});
$("#picture-swiperRight").click(function () {
  rotateParam = 0
  scaleParam = 1
  $(".swiper-wrapper .swiper-slide-next img").css({"transform": "none", "transition": "none"})
  $(".swiper-wrapper .swiper-slide-prev img").css({"transform": "none", "transition": "none"})
});


$("#previewReset").click(function () {
  rotateParam = 0
  scaleParam = 1
  $(".swiper-wrapper .swiper-slide-active img").css({"transform": "rotate(0deg) scale(1)", "transition": "transform .3s"})
});
$("#rotateLeft").click(function () {
  rotateParam-= 90
  let rotateStyle = "rotate(" + rotateParam + "deg)"
  let scaleStyle = "scale(" + scaleParam + ")"
  $(".swiper-wrapper .swiper-slide-active img").css({"transform": (rotateStyle + scaleStyle), "transition": "transform .3s"})
});
$("#rotateRight").click(function () {
  rotateParam+= 90
  let rotateStyle = "rotate(" + rotateParam + "deg)"
  let scaleStyle = "scale(" + scaleParam + ")"
  $(".swiper-wrapper .swiper-slide-active img").css({"transform": (rotateStyle + scaleStyle), "transition": "transform .3s"})
});
$("#scaleBig").click(function () {
  scaleParam+= 0.1
  let rotateStyle = "rotate(" + rotateParam + "deg)"
  let scaleStyle = "scale(" + scaleParam + ")"
  $(".swiper-wrapper .swiper-slide-active img").css({"transform": (rotateStyle + scaleStyle), "transition": "transform .3s"})
});
$("#scaleSmall").click(function () {
  scaleParam-= 0.1
  let rotateStyle = "rotate(" + rotateParam + "deg)"
  let scaleStyle = "scale(" + scaleParam + ")"
  $(".swiper-wrapper .swiper-slide-active img").css({"transform": (rotateStyle + scaleStyle), "transition": "transform .3s"})
});
// 全屏看图功能 end

	var storeStopedAttachs_0 = ${storeStopAttachs_0!}
	var storeStopedAttachs_1 = ${storeStopAttachs_1!}
	var storeStopedAttachs_2 = ${storeStopAttachs_2!}
	var storeStopedAttachs_3 = ${storeStopAttachs_3!}
	var storeStopedAttachs_4 = ${storeStopAttachs_4!}
	// 初始化附件
	initAttach("storeStop0Attachs", "addStoreStoped0Attach", "table-storeStoped0-attach", 0,storeStopedAttachs_0);
	initAttach("storeStop1Attachs", "addStoreStoped1Attach", "table-storeStoped1-attach", 1,storeStopedAttachs_1);
	initAttach("storeStop2Attachs", "addStoreStoped2Attach", "table-storeStoped2-attach", 2,storeStopedAttachs_2);
	initAttach("storeStop3Attachs", "addStoreStoped3Attach", "table-storeStoped3-attach", 3,storeStopedAttachs_3);
	initAttach("storeStop4Attachs", "addStoreStoped4Attach", "table-storeStoped4-attach", 4,storeStopedAttachs_4);
	<#if storeStop.wfId!=null>
		$("#wf_area").load("/act/wf/wf.jhtml?wfid=${storeStop.wfId}");
	</#if>




/**
 * 初始化附件，每个参数都是必填的
 * @param paramAttachs 后台实体类接收附件的参数名
 * @param addAttachIdName 前端页面添加附件按钮的id值
 * @param tableIdName 前端页面table中的id值 
 * @param type 后台用来区分不同类型附件
 */
function initAttach(paramAttachs, addAttachIdName, tableIdName, type,storeStopedAttachs) {

    var index = 0;
	var attachCols = [				
    	{ title:'${message("附件")}',name:'content',width:400,align:'center',renderer:function(val,item,rowIndex){
    		var url = item.url;
			var fileObj = getfileObj(item.name);

			if (fileObj.suffix == 'null' || fileObj.suffix == '') {
        let url_index = url.lastIndexOf('.');
        if(url_index>=0){
            fileObj.suffix = url.substring(url_index+1, url.length);
         }
      }

			/**设置隐藏值*/
			var hideValues = {};
			hideValues[paramAttachs+'['+index+'].url']=url;
			hideValues[paramAttachs+'['+index+'].suffix']=fileObj.suffix;
			hideValues[paramAttachs+'['+index+'].type']=type;  // 装修验收照片附件1
			
			return createFileStr({
				url : url,
				fileName : fileObj.file_name,
				name : fileObj.name,
				suffix : fileObj.suffix,
				time : '',
				textName: paramAttachs+'['+index+'].name',
				hideValues:hideValues
			});
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:500 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text" name="'+paramAttachs+'['+index+'].memo" style="width:94%;height: 40px">'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}', align:'center', renderer: function(val,item,rowIndex){
    		index++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}}
	];
	var $grid=$('#'+tableIdName).mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: attachCols,
        items:storeStopedAttachs,
        checkCol: false,
        autoLoad: true
    });
    
    var $addAttach = $("#"+addAttachIdName);
	var attachIdnex = 0;
	var option = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row = data[i].file_info;
				$grid.addRow(row,null,1);
	        }
        }
    }
    $addAttach.file_upload(option);
	
	/* 删除附件 */
    var $deleteAttachment = $(".deleteAttachment");
	$deleteAttachment.live("click", function() {
		var $this = $(this);
		$this.closest("tr").remove();
	});
}
});
// 保存
function save(e){
	var str = '您确定要保存吗？';
	var url = 'save.jhtml';
	var $form = $("#inputForm");
	if($form.valid()){
		ajaxSubmit(e,{
			url: '/member/store_stop/update.jhtml?type='${type},
			data:$("#inputForm").serialize(),
			method: "post",
			isConfirm:true,
			confirmText:str,
			callback:function(resultMsg){
				/* $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.href= 'edit.jhtml?id='+resultMsg.objx;
				}) */
				$.message_alert("操作成功");
                location.reload(true);
			}
		});
	}
}

function selectLetters(e){
	$('.letters option').each(function(){
		if(e=="是"&&$(this).val() == "3"){
			$(this).attr("selected",true);
		}
		if(e=="否"&&$(this).val() == "2"){
			$(this).attr("selected",true);
		}
	});
}

function check_wf(e){
    var $this = $(e);
    var $form = $("#inputForm");
    if($form.valid()){
        $.message_confirm("您确定要审批流程吗？",function(){
            //正式
			var objTypeId = 100020;
			var modelId =60238
			var url="/member/store_stop/check_wf.jhtml?id=${storeStop.id}&modelId="+modelId+"&objTypeId="+objTypeId;
            var data = $form.serialize();
            ajaxSubmit(e,{
                method:'post',
                url:url,
                async: true,
                callback: function(resultMsg) {
                    $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                        location.reload(true);
                    })
                }
            });
        });
    }
}


function saveform(e){
    $.message_confirm("您确定要提交吗？",function(){
        //获取表单所有数据
        var params = $("#inputForm").serializeArray();
        //定义url
        var url = '/member/store_stop/saveform.jhtml?type='+e;
        ajaxSubmit(e,{
            method:'post',
            url:url,
            data:params,
            async: true,
            callback: function(resultMsg) {
                $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
                    location.reload(true);
                })
            }
        });
    });
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("经销商终止申请")}
	</div>
	<form id="inputForm" action="<#--/shop/reorganize/update.jhtml-->" method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="id" value="${storeStop.id}"/>
		<div class="tabContent">
			<div style="margin: 0 0 10px 0; font-size: 15px;">
				${message("基本信息")}</div>
			<table class="input input-edit">
                <tr>
					<th>${message("处罚单号")}:</th>
					<td>${storeStop.sn}
						<input type="hidden" name="sid" value="${store.id}" />
					</td>
					<th>${message("单据状态")}:</th>
					<td>
                        <#if storeStop.status == 0>${message("已保存")}</#if>
                        <#if storeStop.status == 1>${message("已提交")}</#if>
                        <#if storeStop.status == 2>${message("已终止")}</#if>
                        <#if storeStop.status == 3>${message("进行中")}</#if>
                        <#if storeStop.status == 4>${message("已完成")}</#if>
					</td>
					<th>${message("经销商授权编号")}:</th>
					<td>${store.grantCode}</td>
					<th>${message("区域")}:</th>
					<td>${store.region}</td>
				</tr>
				<tr>
					<th>${message("地区")}:</th>
                    <td colspan="3" class="area">
                        <input type="hidden" id="regionAreaId" value="${(store.headNewArea.id)!}" treePath="${(store.headNewArea.treePath)!}" />
                        <input type="text" value="${store.headNewArea.fullName}" name="region" class="detailed-address" style="width:157.2px;" readonly />
                    </td>
					<th>${message("经销商姓名")}:</th>
					<td>${store.dealerName}</td>
					<th>
						${message("是否核心经销商")}:
                    </th>
                    <td>
                    	<#if storeStop.store.isCoreStroe == true> 是 </#if>
                    	<#if storeStop.store.isCoreStroe == false> 否 </#if>
                    </td>
				</tr>
				<tr>
					<th><span class="requiredField">*</span>${message("是否签订今年合同")}:</th>
					<td>
						<select name="whetherSignedContract" class="text" onchange="selectLetters(this.options[this.options.selectedIndex].text)">
							<option></option>
							<option value="1" <#if storeStop.whetherSignedContract == 1> selected </#if>>是</option>
							<option value="0" <#if storeStop.whetherSignedContract == 0> selected </#if>>否</option>
						</select>
					</td>
					<th><span class="requiredField">*</span>${message("公司发出函件")}:</th>
					<td>
						<select name="letters" class="text letters">
							<option></option>
							<#--<option value="0" <#if reorganize.letters == 0> selected </#if> >整改通知函-->
                            <#--<option value="1" <#if reorganize.letters == 1> selected </#if> >整改通知书</option>-->
                            <option value="2" <#if storeStop.letters == 2> selected </#if>>不续约通知</option>
                            <option value="3" <#if storeStop.letters == 3> selected </#if>>终止通知书</option>
						</select>
					</td>
					<th><span class="requiredField">*</span>${message("省长是否签字")}:</th>
					<td>
						<select name="whetherSignature" class="text">
							<option></option>
							<option value="1" <#if storeStop.whetherSignature == 1> selected </#if> >是</option>
							<option value="0" <#if storeStop.whetherSignature == 0> selected </#if> >否</option>
						</select>
					</td>
					<th><span class="requiredField">*</span>${message("是否提交计划")}:</th>
					<td>
						<select name="whetherSubmitPlan" class="text">
							<option></option>
							<option value="1" <#if storeStop.whetherSubmitPlan == 1> selected </#if> >是</option>
							<option value="0" <#if storeStop.whetherSubmitPlan == 0> selected </#if> >否</option>
					    </select>
                    </td>
				</tr>
                <tr>
					<th><span class="requiredField">*</span>${message("整改内容")}:</th>
					<td>
						<input type="text" class="text" name="rectificationContent" value="${storeStop.rectificationContent}" maxlength="200" btn-fun="clear" />
					</td>
                </tr>
			</table>

			<div class="title-style">
				${message("上传附件")}
				<div
					style="border: 1px solid #dcdcdc; margin-top: 8px; padding: 0px 15px 10px;">

					<span class="title-style fujian">三年销量数据签字版:</span><span id="addStoreStoped0Attach" class="button toPreviewBtn_prev">添加附件</span><span class="toPreviewBtn button">浏览全图</span>
					<table id="table-storeStoped0-attach"></table>

					<span class="title-style fujian">函件省长签字版:</span><span id="addStoreStoped1Attach" class="button toPreviewBtn_prev">添加附件</span><span class="toPreviewBtn button">浏览全图</span>
					<table id="table-storeStoped1-attach"></table>

					<span class="title-style fujian">函件Word版:</span><span id="addStoreStoped2Attach" class="button toPreviewBtn_prev">添加附件</span><span class="toPreviewBtn button">浏览全图</span>
					<table id="table-storeStoped2-attach"></table>

					<span class="title-style fujian">其他:</span><span id="addStoreStoped3Attach" class="button toPreviewBtn_prev">添加附件</span><span class="toPreviewBtn button">浏览全图</span>
					<table id="table-storeStoped3-attach"></table>
				</div>
			</div>
            <#if storeStop.wfId != null>
            <#if szsh || szshs>
			<div class="title-style">
				${message("省长意见")} <#if node.name?contains("省长")><input type="button" class="bottonss tj" onclick="saveform(1)" value="提交"/> </#if>
				<div style="border: 1px solid #dcdcdc; margin-top: 8px; padding: 5px 8px; font: 13px 'Microsoft YaHei';">
					<div>
						<textarea rows="3" name="governorOpinion" style="width: 100%;">${storeStop.governorOpinion}</textarea>
					</div>
				</div>
			</div>
            </#if>
            <#if (qdzy||qdzys)||(qdbsh || qdbshs)>
			<div class="title-style">
				${message("渠道部意见")}<#if node.name?contains("渠道")><input type="button" class="bottonss tj" onclick="saveform(2)" value="提交"/> </#if>
				<div style="border: 1px solid #dcdcdc; margin-top: 8px; padding: 5px 8px; font: 13px 'Microsoft YaHei';">
					<div>
						<textarea rows="3" name="channelOpinion" style="width: 100%;">
							${storeStop.channelOpinion}
						</textarea>
					</div>·
				</div>
			</div>
			<span class="title-style fujian">渠道附件:</span><#if node.name?contains("渠道")><span id="addStoreStoped4Attach" class="button toPreviewBtn_prev">添加附件</span></#if><span class="toPreviewBtn button">浏览全图</span>
			<table id="table-storeStoped4-attach"></table>
            </#if>
            
        	<#if storeStop.status == 1>
        	<div class="title-style">
				<input type="button" class="bottonss tj" onclick="saveform(99)" value="提交"/>
				
				<table class="input input-edit">
					<tr>
						<th>${message("档案文号")}:</th>
						<td><input type="text" class="text" name="archivesNo" value="${storeStop.archivesNo}" maxlength="200" btn-fun="clear" /></td>
						<th>${message("文件编号")}:</th>
						<td><input type="text" class="text" name="fileNo" value="${storeStop.fileNo}" maxlength="200" btn-fun="clear" /></td>
						<th>${message("整改期限")}</th>
						<td><input type="text" name="reorganizeTimeLimit" value="${storeStop.reorganizeTimeLimit}" class="text" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" />
						</td>
						<th>${message("经销商收到时间")}</th>
						<td><input type="text" name="dealerReceivedTime" class="text" value="${storeStop.dealerReceivedTime}" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" />
						</td>
					</tr>
					<tr>
						<th>${message("原件发出时间")}</th>
						<td>
							<input type="text" name="sendOutTime" class="text" value="${storeStop.sendOutTime}" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" />
						</td>
						<th></th>
						<td></td>
						<th></th>
						<td></td>
						<th></th>
						<td></td>
					</tr>
				</table>
				<table class="input input-edit">
					<tr>
						<th>${message("收件人")}:</th>
						<td>
							<input type="text" class="text" name="consignee" value="${storeStop.consignee}" maxlength="200" btn-fun="clear" />
						</td>
						<th>${message("收件类别")}:</th>
						<td><select name="consigneeType" class="text">
								<option></option>
								<option value="0"  <#if storeStop.consigneeType == 0> selected </#if>>经销商</option>
								<option value="1" <#if storeStop.consigneeType == 1> selected </#if> >区域经理</option>
							</select>
						</td>
						<th>${message("联系方式")}:</th>
						<td>
							<input type="text" class="text" name="consigneePhone" oninput="editQty (this,event)" value="${storeStop.consigneePhone}" maxlength="200" btn-fun="clear" />
						</td>
						<th>${message("快递单号")}:</th>
						<td>
							<input type="text" class="text" name="trackingNumber" value="${storeStop.trackingNumber}" maxlength="200" btn-fun="clear" />
						</td>
					</tr>
					<tr>
						<th>${message("快递签收时间")}:</th>
						<td>
							<input type="text" name="courierReceivedTime" value="${storeStop.courierReceivedTime}" class="text" maxlength="200" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d'});" />
						</td>
						<th>${message("是否有回执单")}:</th>
						<td>
							<select name="whetherReceipt" class="text">
								<option></option>
								<option value="1"  <#if storeStop.whetherReceipt == 1> selected </#if> >是</option>
								<option value="0" <#if storeStop.whetherReceipt == 0> selected </#if> >否</option>
							</select>
						</td>
						<th>${message("快递地址")}:</th>
						<td colspan="3" style="position: relative;">
							<input type="hidden" id="expressAreaId" name="expressArea.id" value="${storeStop.expressArea.id}" treePath="${(storeStop.expressArea.treePath)!}"/>
							<input type="text" name="expressAddress" value="${storeStop.expressAddress}" placeholder="详细地址" style="width: 183px;" class="text detailed-address" maxlength="200" />
						</td>
					</tr>
					<tr>
						<th>${message("是否终止经销商")}:</th>
						<td>
							<label><input type="radio" name="whetherStopDealer" value="1"  <#if storeStop.whetherStopDealer == 1> checked </#if> />是</label>&nbsp;&nbsp;&nbsp;
							<label><input type="radio" name="whetherStopDealer" value="0"  <#if storeStop.whetherStopDealer == 0> checked </#if> />否</label>
						</td>
						<th></th>
						<td></td>
						<th></th>
						<td></td>
						<th></th>
						<td></td>
					</tr>
					
				</table>
			</div>
			</#if>
            </#if>
		</div>


		<div class="fixed-top">
			<#if storeStop.wfId == null>
            <a id="shengheButton" class="iconButton" onclick="check_wf(this)"  ><span class="ico-shengheIcon">&nbsp;</span>${message("审核")}</a>
            <input type="button" onclick="save(this)" id="submit_button" class="button sureButton" value="${message("1013")}" />
            </#if>
            <input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
	</form>

	<!-- 全屏看图结构 -->
	<div class="preview_swiperBox">
	  <div id="picture-swiperLeft" class="swiperButton swiper-button-prev"></div>
	  <div id="picture-swiperRight" class="swiperButton swiper-button-next"></div>
	  <div id="picture-colse" class="swiper-button-close">
	    <span></span>
	    <span></span>
	  </div>
	  <div class="preview_control">
	    <div id="scaleBig" class="scaleBig"><span></span></div>
	    <div id="scaleSmall" class="scaleSmall"><span></span></div>
	    <div id="previewReset" class="previewReset"><span></span></div>
	    <div id="rotateLeft" class="rotateLeft"><span></span></div>
	    <div id="rotateRight" class="rotateRight"><span></span></div>
	  </div>
	</div>
	<!-- 全屏看图结构 end-->


  <div id="wf_area" style="width: 100%"></div>
</body>
</html>