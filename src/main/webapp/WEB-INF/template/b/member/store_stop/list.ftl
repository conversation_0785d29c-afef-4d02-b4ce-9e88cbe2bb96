<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("经销商终止")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
$(function(){
	$('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete', 'off'); 
});


$().ready(function() {
	
	/**初始化多选的下拉框*/
	initMultipleSelect();
	var cols = [
		{ title:'${message("单号")}', name:'sn',width:120, align:'center',renderer:function(val,item){
			return '<a href="javascript:void(0);" onClick="parent.change_tab(0,\'/member/store_stop/edit/b.jhtml?id='+item.id+'\')" class="red">'+val+'</a>';
		}},
		{ title:'${message("单据状态")}', name:'status' ,align:'center', renderer:function(val,item){
            var result="";
            if (val == '0') result = '<span class="blue">已保存</span>';
            else if (val == '1') result = '<span class="green">已生效</span>';
            else if (val == '2') result = '<span style="color:orange;">进行中</span>';
            else if (val == '4') result = '<span class="red">已作废</span>';
            return result;
        }},
		{ title:'${message("客户名称")}', name:'store_name' ,width:120, align:'center'},
		{ title:'${message("客户姓名")}', name:'dealer_names' ,width:120, align:'center'},
		{ title:'${message("授权编码")}', name:'grant_code' ,width:120, align:'center'},
		{ title:'${message("客户简称")}', name:'alias' ,width:120, align:'center'},
		{ title:'${message("区域")}', name:'region1' ,width:120, align:'center'},
		{ title:'${message("是否核心经销商")}', name:'is_core' ,width:120, align:'center'},
		{ title:'${message("终止时间")}', name:'' ,width:120, align:'center'},
		{ title:'${message("创建人")}', name:'create_name' ,width:120, align:'center'},
		{ title:'${message("创建时间")}', name:'create_date' ,width:120, align:'center'},
	];

	$mmGrid = $('#table-m1').mmGrid({
		autoLoad: true,
        cols: cols,
        fullWidthRows:true,
        url: '/member/store_stop/list_data.jhtml',
        params:function(){
        	return $("#listForm").serializeObject();
        },
		plugins : [
            $('#paginator').mmPaginator()
        ]
    });
    
	dialog_excel_title.init_excel_title(cols);
    
    //查询客户
	$("#selectStore").bindQueryBtn({
		type:'store',
		title:'${message("查询客户")}',
		url:'/member/store/select_store.jhtml'
	});
	
});

//条件导出		    
function segmentedExport(e) {
	var needConditions = false;//
	var page_url = '/member/store_stop/to_condition_export.jhtml';//分页导出统计页面
	var url = '/member/store_stop/condition_export.jhtml';//导出的方法
	conditions_export(e, {
		needConditions : needConditions,
		page_url : page_url,
		url : url
	});
}
</script>
</head>
<body>
	<form id="listForm" action="/member/store_stop/list_data/b.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
				<div class="flag-wrap flagImp-wrap">
					<a href="javascript:void(0);" class="iconButton" id="export1Button">
						<span class="impIcon">&nbsp;</span>导出
					</a>
					<ul class="flag-list">							 
						<li>
							<a href="javascript:void(0)" onclick="segmentedExport(this)">
								<i class="flag-imp02"></i>
								${message("条件导出")}
							</a>
						</li>		
					</ul>
				</div>
			</div>
			<div id="searchDiv">
				<div id="search-content">
					<dl>
						<dt>
							<p>${message("单号")}：</p>
						</dt>
						<dd>
							<input type="text" class="text" name="sn" btn-fun="clear" />
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("单据状态")}：</p>
						</dt>
						<dd>
							<select class="text" name="status" >
    							<option value="">请选择</option>
    							<option value="0">${message("已保存")}</option>
    							<option value="1">${message("已生效")}</option>
    							<option value="2">${message("进行中")}</option>
    							<option value="4">${message("已作废")}</option>
    						</select>
						</dd>
					</dl>
					<dl>
	        			<dt><p>${message("客户")}：</p></dt>
	        			<dd >
	        				<span class="search" style="position:relative">
								<input type="hidden" name="storeId" class="text storeId" btn-fun="clear" />
								<input type="text" name="storeName" class="text storeName" maxlength="200" onkeyup="clearSelect(this)" readonly/>
								<input type="button" class="iconSearch" value="" id="selectStore">
							</span>
	        			</dd>
	        		</dl>
	        		<dl>
						<dt>
							<p>${message("是否核心经销商")}：</p>
						</dt>
						<dd>
							<select class="text" name="isCoreStroe" >
    							<option value="">请选择</option>
    							<option value="1">${message("是")}</option>
    							<option value="0">${message("否")}</option>
    						</select>
						</dd>
					</dl>
					<dl>
						<dt>
							<p>${message("创建人")}：</p>
						</dt>
						<dd>
							<input type="text" class="text" name="createName" btn-fun="clear" />
						</dd>
					</dl>
					<dt>
						<p>${message("创建日期")}:</p>
					</dt>
					<dd class="date-wrap">
						<input id="startTime" name="firstTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', maxDate: '#F{$dp.$D(\'endTime\')}'});" type="text" btn-fun="clear" />
						<div class="fl">--</div>
						<input id="endTime" name="lastTime" class="text" value="" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd',startDate:'%y-%M-%d', minDate: '#F{$dp.$D(\'startTime\')}'});"  type="text" btn-fun="clear" />
					</dd>
				</div>
				<div class="search-btn">
					<a href="javascript:;" id="searchBtn" class="iconButton">${message("1004")}</a>
				</div>
			</div>
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
			<div id="body-paginator" style="text-align: left;">
				<div id="paginator"></div>
			</div>
		</div>
	</form>
</body>
</html>