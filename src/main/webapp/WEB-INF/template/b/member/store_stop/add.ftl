<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("经销商终止申请")}</title>
    <link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
    <script type="text/javascript" src="/resources/js/reference/jquery.tools.js"></script>
    <script type="text/javascript" src="/resources/js/reference/jquery.validate.js"></script>
    <script type="text/javascript" src="/resources/js/base/dialog.js"></script>
    <script type="text/javascript" src="/resources/js/base/request.js"></script>
    <script type="text/javascript" src="/resources/js/base/global.js"></script>
    <script type="text/javascript" src="/resources/js/base/file.js"></script>
    <script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
    <link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<style>

/* 调整附件 */
.nowrap>div>.t-edit {
	width: 80%;
	margin: 5px 0 -6px 0;
}
</style>
<script type="text/javascript">
    $(function(){
        $('input:not([autocomplete]),textarea:not([autocomplete]),select:not([autocomplete])').attr('autocomplete', 'off');
    });
function editQty(t,e){
	extractNumber(t,3,false,e);
}
$().ready(function() {
	var $inputForm = $("#inputForm");
	
	var $areaId = $("#areaId");
	var $bAreaId = $("#bAreaId");
	
	//地区选择
	$("#regionAreaId").lSelect();
	$("#expressAreaId").lSelect();
	$areaId.lSelect();
	$bAreaId.lSelect();

	
	// 校验
	$inputForm.validate({
		rules: {
            letters:"required",
			whetherSignedContract:"required",
			whetherSignature:"required",
			whetherSubmitPlan:"required",
			rectificationContent:"required",
		}
	});
	
	$(".area").find("select").each(function(){
			$(this).attr("disabled",true);
	});

	// 初始化附件
	initAttach("storeStop0Attachs", "addStoreStoped0Attach", "table-storeStoped0-attach", 0);
	initAttach("storeStop1Attachs", "addStoreStoped1Attach", "table-storeStoped1-attach", 1);
	initAttach("storeStop2Attachs", "addStoreStoped2Attach", "table-storeStoped2-attach", 2);
	initAttach("storeStop3Attachs", "addStoreStoped3Attach", "table-storeStoped3-attach", 3);
});

/**
 * 初始化附件，每个参数都是必填的
 * @param paramAttachs 后台实体类接收附件的参数名
 * @param addAttachIdName 前端页面添加附件按钮的id值
 * @param tableIdName 前端页面table中的id值 
 * @param type 后台用来区分不同类型附件
 */
function initAttach(paramAttachs, addAttachIdName, tableIdName, type) {
    var index = 0;
	var attachCols = [				
    	{ title:'${message("附件")}',name:'content',width:260,align:'center',renderer:function(val,item,rowIndex){
    		var url = item.url;
			var fileObj = getfileObj(item.name);
			/**设置隐藏值*/
			var hideValues = {};
			hideValues[paramAttachs+'['+index+'].url']=url;
			hideValues[paramAttachs+'['+index+'].suffix']=fileObj.suffix;
			hideValues[paramAttachs+'['+index+'].type']=type;  // 装修验收照片附件1
			
			return createFileStr({
				url : url,
				fileName : fileObj.file_name,
				name : fileObj.name,
				suffix : fileObj.suffix,
				time : '',
				textName: paramAttachs+'['+index+'].name',
				hideValues:hideValues
			});
    	}},
		{ title:'${message("备注")}', name:'memo' ,width:580 ,align:'center', renderer: function(val,item,rowIndex){
			return '<div><textarea class="text" name="'+paramAttachs+'['+index+'].memo" >'+val+'</textarea></div>';
		}},
    	{ title:'${message("操作")}',width:5 ,align:'center', renderer: function(val,item,rowIndex){
    		index++;
			return '<a href="javascript:;" class=" btn-delete deleteAttachment" >${message("删除")}</a>';
		}}
	];
	var $grid=$('#'+tableIdName).mmGrid({
		fullWidthRows:true,
		height:'auto',
        cols: attachCols,
        checkCol: false,
        autoLoad: true
    });
    
    var $addAttach = $("#"+addAttachIdName);
	var attachIdnex = 0;
	var option = {
		dataType: "json",
	    uploadToFileServer:true,
	    uploadSize: "fileurl",
        callback : function(data){
        	var date = new Date();
			var year = date.getFullYear();
			var month = date.getMonth()+1;
			var day = date.getDate();
			var time = year+'-'+month+'-'+day;
	        for(var i=0;i<data.length;i++){
				var row = data[i].file_info;
				$grid.addRow(row,null,1);
	        }
        }
    }
    $addAttach.file_upload(option);
	
	/* 删除附件 */
    var $deleteAttachment = $(".deleteAttachment");
	$deleteAttachment.live("click", function() {
		var $this = $(this);
		$this.closest("tr").remove();
	});
}

function selectLetters(e){
	$('.letters option').each(function(){
		if(e=="是"&&$(this).val() == "3"){
			$(this).attr("selected",true);
		}
		if(e=="否"&&$(this).val() == "2"){
			$(this).attr("selected",true);
		}
	});
}

// 保存
function save(e){
	var str = '您确定要保存吗？';
	var url = 'save.jhtml';
	var $form = $("#inputForm");
	if($form.valid()){
		ajaxSubmit(e,{
			url: '/member/store_stop/save.jhtml',
			data:$("#inputForm").serialize(),
			method: "post",
			isConfirm:true,
			confirmText:str,
			callback:function(resultMsg){
				/* $.message_timer(resultMsg.type,resultMsg.content,1000,function(){
					location.href= 'edit.jhtml?id='+resultMsg.objx;
				}) */
                location.href= '/member/store_stop/edit/${code}.jhtml?id='+resultMsg.objx;
                $.message_alert("操作成功");
			}
		});
	}
}
</script>
</head>
<body>
	<div class="pathh">
		&nbsp;${message("提交经销商终止申请")}
	</div>
	<form id="inputForm" action="/member/store_stop/save.jhtml" method="post" type="ajax" validate-type="validate">
		<input type="hidden" name="id" value="${storeStop.id}"/>
        <input type="hidden" name="sn" value="${storeStop.sn}"/>
		<div class="tabContent">
			<div style="margin: 0 0 10px 0; font-size: 15px;">
				${message("基本信息")}</div>
			<table class="input input-edit">

                <tr>
					<th>${message("处罚单号")}:</th>
					<td><input type="hidden" name="sid" value="${store.id}" /></td>
					<th>${message("单据状态")}:</th>
					<td></td>
					<th>${message("经销商授权编号")}:</th>
					<td>${store.grantCode}</td>
					<th>${message("区域")}:</th>
					<td>${store.region}</td>
				</tr>
				<tr>
					<th>${message("地区")}:</th>
					<td colspan="3" class="area">
						<input type="hidden" id="regionAreaId" value="${(store.headNewArea.id)!}" treePath="${(store.headNewArea.treePath)!}" />
						<input type="text" value="${store.headNewArea.fullName}" class="detailed-address" style="width:157.2px;" name="region" class="text" readonly />
					</td>
					<th>${message("经销商姓名")}:</th>
					<td>${store.dealerName}</td>
					<th>
						${message("是否核心经销商")}:
                    </th>
                    <td>
                    	<#if store.isCoreStroe == true> 是 </#if>
                    	<#if store.isCoreStroe == false> 否 </#if>
                    </td>
				</tr>
				<tr>
					<th><span class="requiredField">*</span>${message("是否签订今年合同")}:</th>
					<td>
						<select name="whetherSignedContract" class="text" onchange="selectLetters(this.options[this.options.selectedIndex].text)">
							<option></option>
							<option value="1">是</option>
							<option value="0">否</option>
						</select>
					</td>
					<th><span class="requiredField">*</span>${message("公司发出函件")}:</th>
					<td>
						<select name="letters" class="text letters">
							<option></option>
							<#--<option value="0">整改通知函</option>-->
							<#--<option value="1">整改通知书</option>-->
							<option value="2">不续约通知</option>
							<option value="3">终止通知书</option>
						</select>
					</td>
					<th><span class="requiredField">*</span>${message("省长是否签字")}:</th>
					<td>
						<select name="whetherSignature" class="text">
							<option></option>
							<option value="1">是</option>
							<option value="0">否</option>
						</select>
					</td>
					<th><span class="requiredField">*</span>${message("是否提交计划")}:</th>
					<td>
						<select name="whetherSubmitPlan" class="text">
							<option></option>
							<option value="1">是</option>
							<option value="0">否</option>
						</select>
					</td>
				</tr>
				<tr>
					<th><span class="requiredField">*</span>${message("整改内容")}:</th>
					<td>
						<input type="text" class="text" name="rectificationContent" value="" maxlength="200" btn-fun="clear" />
					</td>
				</tr>
			</table>

			<div class="title-style">
				${message("上传附件")}
				<div
					style="border: 1px solid #dcdcdc; margin-top: 8px; padding: 0px 15px 10px;">
					<div class="title-style">
						三年销量数据签字版
						<div class="btns">
							<a href="javascript:;" id="addStoreStoped0Attach" class="button">添加附件</a>
						</div>
					</div>
					<table id="table-storeStoped0-attach"></table>
					<div class="title-style">
						函件省长签字版
						<div class="btns">
							<a href="javascript:;" id="addStoreStoped1Attach" class="button">添加附件</a>
						</div>
					</div>
					<table id="table-storeStoped1-attach"></table>
					<div class="title-style">
						函件Word版
						<div class="btns">
							<a href="javascript:;" id="addStoreStoped2Attach" class="button">添加附件</a>
						</div>
					</div>
					<table id="table-storeStoped2-attach"></table>
					<div class="title-style">
						其他
						<div class="btns">
							<a href="javascript:;" id="addStoreStoped3Attach" class="button">添加附件</a>
						</div>
					</div>
					<table id="table-storeStoped3-attach"></table>
				</div>
			</div>
		<div class="fixed-top">
            <input type="button" onclick="save(this)" id="submit_button" class="button sureButton" value="${message("1013")}" />
            <input type="button" onclick="location.reload(true);" class="button refreshButton ml15" value="${message("刷新")}">
		</div>
	</form>
</body>
</html>