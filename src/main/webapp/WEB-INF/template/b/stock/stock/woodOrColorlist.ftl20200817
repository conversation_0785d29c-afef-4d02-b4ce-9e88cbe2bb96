<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("木种/花色查询")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />	
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
function showPage(resultMsg){
	var pageNumber=1;
	var totaNum=0;
	var pageSize=200;
	if(resultMsg!=undefined){
		var content_data = $.parseJSON(resultMsg.content);
		totaNum = content_data.total;
		pageSize = content_data.pageSize;
		pageNumber = content_data.pageNumber;
	}
	var page = parseInt(totaNum/pageSize);
	if(totaNum%pageSize!=0){
		page = page+1;
	}
	if(page==0){
		page = 1;
	}
	$("#s-page").remove();
}

$().ready(function() {
	/**初始化多选的下拉框*/
	initMultipleSelect();
	
	//查询仓库
	$("#selectWarehouse").bindQueryBtn({
		type:'warehouse',
		title:'${message("查询仓库")}',
		url:'/stock/warehouse/select_warehouse.jhtml?multi=2',
		callback:function(rows){
			if(rows.length>0){
				var vhtml="";
				if($("input[name='warehouseName']").val() == null){
					var allName= "";
				}else{
					var allName= $("input[name='warehouseName']").val();
				}
				var idH ="";
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".warehouseId_"+rows[i].id).length;
					if(idH == 0){
						allName =allName +','+ rows[i].name;
						vhtml = '<div><input name="warehouseId" class="text warehouseId warehouseId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newclosePro(this)"></i></div>';
						$(".warehouse").append(vhtml);
					}
				}
				$("input[name='warehouseName']").attr("value",allName);
			}
		}
	});
	
	//查询产品
	$("#selectProduct").bindQueryBtn({
		type:'product',
		title:'${message("查询产品")}',
		url:'/product/product/selectProduct.jhtml?multi=2',
		callback:function(rows){
			if(rows.length>0){
				var vhtml="";
				if($("input[name='productName']").val() == null){
					var allName= "";
				}else{
					var allName= $("input[name='productName']").val();
				}
				var idH ="";
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".productId_"+rows[i].id).length;
					if(idH == 0){
						allName =allName +','+ rows[i].name;
						vhtml = '<div><input name="productId" class="text productId productId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="closeProductPro(this)"></i></div>';
						$(".product").append(vhtml);
					}
				}
				$("input[name='productName']").attr("value",allName);
			}
		}
	});
	
	var cols = [
		{ title:'${message("物料编码")}', name:'itemCode' ,align:'center',width:120},
		{ title:'${message("名称")}', name:'fullName' ,align:'center',width:100},
		{ title:'${message("产品描述")}', name:'detailDescription' ,align:'center',width:200},		
		{ title:'${message("仓库名称")}', name:'subinvName' ,align:'center',width:80},
		{ title:'${message("仓库代码")}', name:'subinvCode' ,align:'center',width:50},
		{ title:'${message("经营组织")}', name:'product_organization_name' ,align:'center',renderer:function(val,item,rowIndex){
			return val;
		}},
		{ title:'${message("等级")}', name:'levelName' ,align:'center',width:80},
		{ title:'${message("色号")}', name:'color_numbers_name' ,align:'center' ,renderer:function(val,item,rowIndex){
		    return val;
		}},
		{ title:'${message("含水率")}', name:'moisture_content_name' ,align:'center' ,renderer:function(val,item,rowIndex){
		    return val;
		}},
		{ title:'${message("库位编码")}', name:'warehouseLocationCode' ,align:'center',width:80},
		{ title:'${message("新旧标识")}', name:'new_old_logos_name' ,align:'center' ,renderer:function(val,item,rowIndex){
		    return val;
		}},
		{ title:'批次编码', name:'batch_encoding' ,align:'center',width:80},  
		{ title:'作业单号', name:'warehouse_batch_sn' ,align:'center',width:80},  
		{ title:'完工日期', name:'completion_date' ,align:'center',width:80},  
		{ title:'批次备注', name:'batch_memo' ,align:'center',width:80},
		{ title:'${message("现有量（箱）")}', name:'onhandQuantity2' ,align:'center',renderer:function(val,item,rowIndex){
		    return val;
		}},
		{ title:'${message("现有量（支）")}', name:'onhandQuantity3' ,align:'center',renderer:function(val,item,rowIndex){
		    return val;
		}},
		{ title:'${message("现有量平方数")}', name:'onhandQuantity1' ,align:'center' ,renderer:function(val,item,rowIndex){
		    return val;
		}},
		{ title:'${message("可处理量（箱）")}', name:'attQuantity2' ,align:'center',renderer:function(val,item,rowIndex){
		    return val;
		}},
		{ title:'${message("可处理量（支）")}', name:'attQuantity3' ,align:'center',renderer:function(val,item,rowIndex){    
		    return val;
		}},
		{ title:'${message("可处理量平方数")}', name:'attQuantity1' ,align:'center' ,renderer:function(val,item,rowIndex){
		    return val;
		}},		
		{ title:'${message("木种/花色")}', name:'woodTypeOrColor' ,align:'center',width:80},
		{ title:'${message("型号")}', name:'model' ,align:'center',width:80},
		{ title:'${message("规格")}', name:'spec' ,align:'center',width:80},
		{ title:'${message("单位")}', name:'unit' ,align:'center',width:80},
		{ title:'${message("库存组织")}', name:'stock_name',align:'center',renderer:function(val,item,rowIndex){
			return val;
		}}
	];

	$mmGrid = $('#table-m1').mmGrid({
        cols: cols,
        //autoLoad:true,
        fullWidthRows:true,
        url: '/stock/stock/woodOrColorlist_data.jhtml',
        params:function(){
        	return $("#listForm").serializeObject();
        },
		plugins : [
            $('#paginator').mmPaginator()
        ],
        callback:function(data,resultMsg){
        	showPage(resultMsg);
        }
    });
    showPage();
    
    //批次
    $("#selectWarehouseBatch").bindQueryBtn({
		type:'warehouseBatch',
		title:'${message("查询批次")}',
		url:'/stock/batch/select_bacth.jhtml',
		callback:function(rows){
			if(rows.length>0){
				var vhtml="";
				if($("input[name='warehouseBatchName']").val() == null){
					var allName= "";
				}else{
					var allName= $("input[name='warehouseBatchName']").val();
				}
				var idH ="";
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".warehouseBatchId_"+rows[i].id).length;
					if(idH == 0){
						allName =allName +','+ rows[i].batch_encoding;
						vhtml = '<div><input name="warehouseBatchId" class="text warehouseBatchId warehouseBatchId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].batch_encoding+'</p><i class="close" onClick="closeWarehouseBatchPro(this)"></i></div>';
						$(".warehouseBatch").append(vhtml);
					}
				}
				$("input[name='warehouseBatchName']").attr("value",allName);
			}
		}
	});
    
});

function newclosePro(e){
	$(e).closest("div").remove();
	var allName2 = '';
	$(".warehouse > div").each(function(){
		allName2 = allName2 +','+  $(this).find("p").html();
	})
	$("input[name='warehouseName']").attr("value",allName2)
};

function closePro(e){
	$(e).closest("div").remove();
	var all2 = '';
	$(".product > div").each(function(){
		all2 = all2 +','+  $(this).find("p").html();
	})
	$("input[name='productName']").attr("value",all2);
};

function closeProductPro(e){
	$(e).closest("div").remove();
	var all2 = '';
	$(".product > div").each(function(){
		all2 = all2 +','+  $(this).find("p").html();
	})
	$("input[name='productName']").attr("value",all2);
};

function closeWarehouseBatchPro(e){
	$(e).closest("div").remove();
	var all2 = '';
	$(".warehouseBatch > div").each(function(){
		all2 = all2 +','+  $(this).find("p").html();
	})
	$("input[name='warehouseBatchName']").attr("value",all2);
};

//条件导出		    
function segmentedExport(e){
	var needConditions = false;
	var page_url = '/stock/stock/to_condition_woodOrColorCount.jhtml';//分页导出统计页面
	var url = '/stock/stock/condition_woodOrColorList.jhtml';//导出的方法
	conditions_export(e,{needConditions:needConditions,page_url:page_url,url:url});
}


function showZero(e){
    var flag = 0;
    if($(e).prop("checked")){
     	flag = 1;
	}
	$("#isShowZero").val(flag);
	$mmGrid.load();
    showPage();
	 
}

function show(){
   	var flag = 0;
   	if($("#show").prop("checked")){
    		flag = 1;
	}
    	$("#isShowZero").val(flag);
	$mmGrid.load();
   	showPage();
}
</script>
</head>
<body>
	<form id="listForm" action="/stock/stock/woodOrColorlist/${code}.jhtml" method="get">
		<div class="bar">
			<div class="buttonWrap">
				<div class="flag-wrap flagImp-wrap">
					<a href="javascript:void(0);" class="iconButton" id="export1Button">
						<span class="impIcon">&nbsp;</span>导入导出
					</a>
					<ul class="flag-list">
						<li>
							<a href="javascript:void(0)" onclick="segmentedExport(this)">
								<i class="flag-imp02"></i>
								${message("条件导出")}
							</a>
						</li>
					</ul>
				</div>
			</div>
			<div class="search-btn">
				<a href="javascript:;" id="searchBtn" onclick="show()" class="find iconButton">${message("1004")}</a>
			</div>
			<div id="searchDiv">
		        <div id="search-content">
		          	<dl>
						<dt><p>${message("仓库")}:</p></dt>
						<dd>
							<span class="search" style="position:relative">
								<input type="hidden" name="warehouseId" class="text warehouseId" btn-fun="clear"/>
								<input type="text" name="warehouseName" class="text warehouseName" maxlength="200" onkeyup="clearSelect(this)" />
								<input type="button" class="iconSearch" id="selectWarehouse">
								<div class="pupTitleName  warehouse"></div>
							</span>
						</dd>
					</dl>
					<dl>
	        			<dt><p>${message("经营组织")}：</p></dt>
	        			<dd>
	        				<div class="checkbox-style">
								<a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
						       	<input type="text" class="text pointer doStatus" value="" autocomplete="off" />
					       		<div class="statusList cs-box" data-value="off">
					       			[#list storeMemberOrganizationList as storeMemberOrganization]
										<label><input class="check js-iname" name="organizationId" value="${storeMemberOrganization.organization.id}" type="checkbox"/>${storeMemberOrganization.organization.name}</label>
									[/#list]
					       		</div>
						     </div>
	        			</dd>
	        		</dl>
	        		<dl>
						<dt><p>${message("产品")}:</p></dt>
						<dd>
							<span class="search" style="position:relative">
								<input type="hidden" name="productId" class="text productId" btn-fun="clear"/>
								<input type="text" name="productName" class="text productName" maxlength="200" onkeyup="clearSelect(this)" />
								<input type="button" class="iconSearch" id="selectProduct">
								<div class="pupTitleName  product"></div>
							</span>
						</dd>
					</dl>	
					<dl>
        				<dt><p>${message("产品级别")}:</p></dt>
	        			<dd>
	        				<div class="checkbox-style">
								<a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
						       	<input type="text" class="text pointer doStatus" value="" autocomplete="off" />
					       		<div class="statusList cs-box" data-value="off">
					       			[#list productLevelList as productLevel]
					       					<label><input class="check js-iname" name="productLevelId" value="${productLevel.id}" type="checkbox"/>${productLevel.value}</label>
					          		[/#list]
					       		</div>
						     </div>
	        			</dd>
	        		</dl>
	        		<dl>
	        			<dt><p>${message("色号")}：</p></dt>
	        			<dd>
	        				<div class="checkbox-style">
								<a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
						       	<input type="text" class="text pointer doStatus" value="" autocomplete="off" />
					       		<div class="statusList cs-box" data-value="off">
					       			[#list colorNumberList as colorNumber]
										<label><input class="check js-iname" name="colorNumbersId" value="${colorNumber.id}" type="checkbox"/>${colorNumber.value}</label>
									[/#list]
					       		</div>
						     </div>
	        			</dd>
	        		</dl>
	        		<dl>
	        			<dt><p>${message("含水率")}：</p></dt>
	        			<dd>
	        				<div class="checkbox-style">
								<a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
						       	<input type="text" class="text pointer doStatus" value="" autocomplete="off" />
					       		<div class="statusList cs-box" data-value="off">
					       			[#list moistureContentList as moistureContent]
										<label><input class="check js-iname" name="moistureContentId" value="${moistureContent.id}" type="checkbox"/>${moistureContent.value}</label>
									[/#list]
					       		</div>
						     </div>
	        			</dd>
	        		</dl>
	        		<dl>
				    	<dt><p>${message("新旧标识")}：</p></dt>
				    	<dd>
					    	<div class="checkbox-style">
								<a href="javascript:" onclick="clearText(this)" class="deleteText close"></a>
						       	<input type="text" class="text pointer doStatus" value=""  autocomplete="off" />
					       		<div class="statusList cs-box" data-value="off">
					       			[#list newOldLogosList as newOldLogos]
										<label><input class="check js-iname"  value="${newOldLogos.id}" name="newOldLogosIds" type="checkbox"/>${newOldLogos.value}</label>
									[/#list]
					       		</div>
						    </div>						
					   	</dd>
				    </dl>
				    <dl>
						<dt><p>${message("批次")}:</p></dt>
						<dd>
							<span class="search" style="position:relative">
								<input type="hidden" name="warehouseBatchId" class="text warehouseBatchId" btn-fun="clear"/>
								<input type="text" name="warehouseBatchName" class="text warehouseBatchName" maxlength="200" onkeyup="clearSelect(this)" />
								<input type="button" class="iconSearch" id="selectWarehouseBatch">
								<div class="pupTitleName  warehouseBatch"></div>
							</span>
						</dd>
					</dl>
		        </div>
			</div>
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
	   		<div id="body-paginator" style="text-align:left;">
	    		<div id="paginator"></div>
	   		</div>
		</div>
	</form>
</body>
</html>