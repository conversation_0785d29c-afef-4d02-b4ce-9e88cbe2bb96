<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8" />
<title>${message("实时库存信息")}</title>
<link href="/resources/css/common.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/resources/js/reference/jquery.js"></script>
<script type="text/javascript" src="/resources/js/base/dialog.js"></script>
<script type="text/javascript" src="/resources/js/base/request.js"></script>
<script type="text/javascript" src="/resources/js/base/global.js"></script>
<script type="text/javascript" src="/resources/js/reference/mmGrid.js"></script>
<link href="/resources/css/mmGrid.css" rel="stylesheet" type="text/css" />	
<script type="text/javascript" src="/resources/plugin/datePicker/WdatePicker.js"></script>
<script type="text/javascript">
function showPage(resultMsg){
	var pageNumber=1;
	var totaNum=0;
	var pageSize=200;
	if(resultMsg!=undefined){
		var content_data = $.parseJSON(resultMsg.content);
		totaNum = content_data.total;
		pageSize = content_data.pageSize;
		pageNumber = content_data.pageNumber;
	}
	
	
	var page = parseInt(totaNum/pageSize);
	if(totaNum%pageSize!=0){
		page = page+1;
	}
	if(page==0){
		page = 1;
	}
	
	
	
	$("#s-page").remove();
/* 	var $str = $('<p id="s-page" style="float:left;line-height:38px;font-size:14px;margin-left:5px">当前第<span style="color:blue">'+pageNumber+'</span>页，共<span style="color:blue">'+page+'</span>页，总数<span style="color:blue">'+totaNum+'</span></p>');
	$str.insertAfter(".pageList .next"); */

}
$().ready(function() {
//查询仓库
	$("#selectProduct").bindQueryBtn({
		type:'product',
		title:'${message("查询产品")}',
		url:'/product/product/selectProduct.jhtml?multi=2',
		callback:function(rows){
			if(rows.length>0){
				var vhtml="";
				if($("input[name='productName']").val() == null){
					var allName= "";
				}else{
					var allName= $("input[name='productName']").val();
				}
				
/* 				for (var i = 0; i < rows.length;i++) {
					var idH = $(".productId_"+rows[i].id).length;
					if(idH > 0){
						$.message_alert('产品【'+rows[i].name+'】已添加');
						return false;
					}
				} */
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".productId_"+rows[i].id).length;
					if(idH == 0){
						allName =allName +','+ rows[i].name;
						vhtml = '<div><input name="itemCode" class="text itemCode itemCode_'+rows[i].vonder_code+'" type="hidden" value="'+rows[i].vonder_code+'"><p>'+rows[i].name+'</p><i class="close" onClick="closePro(this)"></i></div>'
					}
					$(".product").append(vhtml);
				}
				$("input[name='productName']").attr("value",allName);
			}
		}
	});

	$("#selectWarehouse").bindQueryBtn({
		type:'warehouse',
		title:'${message("查询仓库")}',
		url:'/stock/warehouse/select_warehouse.jhtml?multi=2',
		callback:function(rows){
			if(rows.length>0){
				var vhtml="";
				if($("input[name='warehouseName']").val() == null){
					var allName= "";
				}else{
					var allName= $("input[name='warehouseName']").val();
				}
				
			/* 	for (var i = 0; i < rows.length;i++) {
					var idH = $(".warehouseId_"+rows[i].id).length;
					if(idH > 0){
						$.message_alert('仓库【'+rows[i].name+'】已添加');
						return false;
					}
				} */
				var idH ="";
				for (var i = 0; i < rows.length;i++) {
					var idH = $(".warehouseId_"+rows[i].id).length;
					if(idH == 0){
						allName =allName +','+ rows[i].name;
						vhtml = '<div><input name="warehouseId" class="text warehouseId warehouseId_'+rows[i].id+'" type="hidden" value="'+rows[i].id+'"><p>'+rows[i].name+'</p><i class="close" onClick="newclosePro(this)"></i></div>';
						$(".warehouse").append(vhtml);
					}
				}
				$("input[name='warehouseName']").attr("value",allName);
			}
		}
	});
	
	var cols = [
		{ title:'${message("名称")}', name:'fullName' ,align:'center',width:200},
		{ title:'${message("木种/花色")}', name:'woodTypeOrColor' ,align:'center'},
		{ title:'${message("等级")}', name:'itemRank' ,align:'center',width:50},
		{ title:'${message("型号")}', name:'model' ,align:'center'},
		{ title:'${message("规格")}', name:'spec' ,align:'center'},
		{ title:'${message("单位")}', name:'unit' ,align:'center'},
		{ title:'${message("物料编码")}', name:'itemCode' ,align:'center'},
		{ title:'${message("产品描述")}', name:'detailDescription' ,align:'center'},
		{ title:'${message("仓库名称")}', name:'subinvName' ,align:'center',width:50},
		{ title:'${message("仓库代码")}', name:'subinvCode' ,align:'center',width:50},
		{ title:'${message("现有量（箱）")}', name:'onhandQuantity2' ,align:'center',renderer:function(val,item,rowIndex){
			
		     var onhandQuantity2='';
		     if(item.isDisplay!='' && item.isDisplay=='true'){
		       onhandQuantity2=item.onhandQuantity2; 
		     }else{
		       onhandQuantity2='***';
		     }
		    return onhandQuantity2;
		
		}},
		{ title:'${message("现有量（支）")}', name:'onhandQuantity3' ,align:'center',renderer:function(val,item,rowIndex){
			
		     var onhandQuantity3='';
		     if(item.isDisplay!='' && item.isDisplay=='true'){
		       onhandQuantity3=item.onhandQuantity3; 
		     }else{
		       onhandQuantity3='***';
		     }
		    return onhandQuantity3;
		
		}},
		{ title:'${message("现有量平方数")}', name:'onhandQuantity1' ,align:'center' ,renderer:function(val,item,rowIndex){
			
		     var onhandQuantity1='';
		     if(item.isDisplay!='' && item.isDisplay=='true'){
		       onhandQuantity1=item.onhandQuantity1; 
		     }else{
		       onhandQuantity1='***';
		     }
		    return onhandQuantity1;
		
		}},
		{ title:'${message("可处理量（箱）")}', name:'attQuantity2' ,align:'center',renderer:function(val,item,rowIndex){
			
		     var attQuantity2='';
		     if(item.isDisplay!='' && item.isDisplay=='true'){
		       attQuantity2=item.attQuantity2; 
		     }else{
		       attQuantity2='***';
		     }
		    return attQuantity2;
		
		}},
		{ title:'${message("可处理量（支）")}', name:'attQuantity3' ,align:'center',renderer:function(val,item,rowIndex){
		    
		     var attQuantity3='';
		     if(item.isDisplay!='' && item.isDisplay=='true'){
		       attQuantity3=item.attQuantity3; 
		     }else{
		       attQuantity3='***';
		     }
		    return attQuantity3;
		
		}},
		{ title:'${message("可处理量平方数")}', name:'attQuantity1' ,align:'center' ,renderer:function(val,item,rowIndex){
		     
		     var attQuantity1='';
		     if(item.isDisplay!='' && item.isDisplay=='true'){
		       attQuantity1=item.attQuantity1; 
		     }else{
		       attQuantity1='***';
		     }
		    return attQuantity1;
		
		}},
		{ title:'${message("经营组织")}', name:'orgId' ,align:'center',renderer:function(val,item,rowIndex){
			var orgId='11';
			if (item.orgId == '107') {
				orgId ='大自然家居（中国）有限公司';	
			}else if(item.orgId == '381'){
				orgId ='大自然家居(中国)地板';
			}
			return orgId;
		}},
		{ title:'${message("库存组织")}', name:'organizationId',align:'center',renderer:function(val){
			if (val == '125') {
				return '大自然家居(中国)地板';	
			}else if(val == '381'){
				return '广西柏景地板有限公司';
			}
		}},
	];

	$mmGrid = $('#table-m1').mmGrid({
        cols: cols,
        //autoLoad:true,
        fullWidthRows:true,
        url: '/stock/stock/stockInfolist_data.jhtml',
        params:function(){
        	return $("#listForm").serializeObject();
        },
		plugins : [
            $('#paginator').mmPaginator()
        ],
        callback:function(data,resultMsg){
        	showPage(resultMsg);
        }
    });
    showPage();
    
});
function newclosePro(e){
	$(e).closest("div").remove();
	var allName2 = '';
	$(".warehouse > div").each(function(){
		allName2 = allName2 +','+  $(this).find("p").html();
	})
	$("input[name='warehouseName']").attr("value",allName2)
};
function closePro(e){
	$(e).closest("div").remove();
	var all2 = '';
	$(".product > div").each(function(){
		all2 = all2 +','+  $(this).find("p").html();
	})
	$("input[name='productName']").attr("value",all2);
};
//条件导出		    
function segmentedExport(e){
	var needConditions = false;
	var page_url = '/stock/stock/to_condition_exportInfo.jhtml';//分页导出统计页面
	var url = '/stock/stock/condition_exportInfo.jhtml';//导出的方法
	conditions_export(e,{needConditions:needConditions,page_url:page_url,url:url});
}


function showZero(e){
	
    var flag = 0;
    if($(e).prop("checked")){
     	flag = 1;
	}
	$("#isShowZero").val(flag);
	$mmGrid.load();
    showPage();
	 
}
function show(){
    	 var flag = 0;
    	if($("#show").prop("checked")){
     		flag = 1;
		}
     	$("#isShowZero").val(flag);
		$mmGrid.load();
    	showPage();
    }
</script>
</head>
<body>
	<form id="listForm" action="/stock/stock/stockInfolist/${code}.jhtml" method="get">
	
		<div class="bar">
			<div class="buttonWrap">
						<div class="flag-wrap flagImp-wrap">
						<a href="javascript:void(0);" class="iconButton" id="export1Button">
							<span class="impIcon">&nbsp;</span>导入导出
						</a>
						<ul class="flag-list">
							<li><a href="javascript:void(0)" onclick="segmentedExport(this)"><i class="flag-imp02"></i>${message("条件导出")}</a></li>

						</ul>
			</div>
				</div>
			<div class="search-btn">
				<a href="javascript:;" id="searchBtn" onclick="show()" class="find iconButton">${message("1004")}</a>
			</div>
			<div id="searchDiv">
		        <div id="search-content">
		          	<dl>
						<dt><p>${message("仓库名称")}:</p></dt>
						<dd>
							<span class="search" style="position:relative">
							<input type="hidden" name="warehouseId" class="text warehouseId" btn-fun="clear"/>
							<input type="text" name="warehouseName" class="text warehouseName" maxlength="200" onkeyup="clearSelect(this)" />
							<input type="button" class="iconSearch" id="selectWarehouse">
							<div class="pupTitleName  warehouse"></div>
							</span>
						</dd>
					</dl>
		               	[#--<dl>
						<dt><p>${message("物料编码")}:</p></dt> 
						<dd>
							<input type="text" name="itemCode" class="text itemCode" maxlength="200"   onkeyup="clearSelect(this)" />
						</dd>
						</dl>--]
						<dl><dt><p>${message("产品名称")}:</p></dt>
	    				<dd>
	    					<span style="position:relative">
								<input class="text productName" maxlength="200" type="text" name="productName" value="" onkeyup="clearSelect(this)" readonly>
								<input type="button" class="iconSearch" value="" id="selectProduct">
								<div class="pupTitleName product"></div>
							</span>
	    				</dd></dl>			
						
		               	<dl>
						<dt><p>${message("产品级别")}:</p></dt> 
						<dd>
						<select id="itemGrade" name="itemGrade" class="text" >
							    <option value="优等品">优等品</option>
								<option value="二等品">二等品</option>
								<option value="一等品">一等品</option>
								<option value="无等级">无等级</option>
						</select>
						</dd>
					</dl> 
		     		 <dl>
					<dt><p>${message("库存组织")}:</p></dt>
					<dd>
					<select name="organizationId" class="text" >
						[#list stockSystemDicts as stockSystemDict]
							<option value="${stockSystemDict.remark}">${stockSystemDict.value}</option>
						[/#list]
					</select>
					</dd>
				</dl>
		        </div>
			</div>
		</div>
		<div class="table-responsive">
			<table id="table-m1"></table>
	   		<div id="body-paginator" style="text-align:left;">
	    		<div id="paginator"></div>
	   		</div>
		</div>
	</form>
</body>
</html>